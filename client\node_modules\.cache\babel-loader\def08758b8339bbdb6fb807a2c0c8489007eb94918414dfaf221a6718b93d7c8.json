{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\DoctorDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styles from './DoctorDashboard.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorDashboard = () => {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n  const stats = [{\n    title: \"Today's Appointments\",\n    value: '8',\n    icon: '📅',\n    change: '+2 from yesterday'\n  }, {\n    title: 'This Week',\n    value: '32',\n    icon: '📊',\n    change: '+5 from last week'\n  }, {\n    title: 'Total Patients',\n    value: '156',\n    icon: '👥',\n    change: '+12 this month'\n  }, {\n    title: 'Pending Reviews',\n    value: '3',\n    icon: '⏳',\n    change: 'Needs attention'\n  }];\n  const appointments = [{\n    id: 1,\n    patient: '<PERSON>',\n    time: '09:00',\n    reason: 'Regular checkup',\n    status: 'confirmed'\n  }, {\n    id: 2,\n    patient: '<PERSON>',\n    time: '10:30',\n    reason: 'Follow-up consultation',\n    status: 'pending'\n  }, {\n    id: 3,\n    patient: 'Mike <PERSON>',\n    time: '14:00',\n    reason: 'Chest pain evaluation',\n    status: 'confirmed'\n  }, {\n    id: 4,\n    patient: 'Sarah Brown',\n    time: '15:30',\n    reason: 'Medication review',\n    status: 'completed'\n  }];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold\",\n          children: \"Doctor Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted-foreground\",\n          children: \"Welcome back, Dr. Sarah Johnson\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), \"Patient Records\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          children: [/*#__PURE__*/_jsxDEV(Stethoscope, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), \"Update Availability\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: stats.map((stat, index) => {\n        const IconComponent = stat.icon;\n        return /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-muted-foreground\",\n                  children: stat.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: stat.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconComponent, {\n                className: `h-8 w-8 ${stat.color}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center justify-between\",\n              children: [\"Today's Appointments\", /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"text-sm border rounded px-2 py-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: appointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-lg\",\n                        children: appointment.patient\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 86,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        className: getStatusColor(appointment.status),\n                        children: appointment.status\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 87,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 85,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-muted-foreground space-y-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [/*#__PURE__*/_jsxDEV(Clock, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 93,\n                          columnNumber: 29\n                        }, this), appointment.time]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 92,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: appointment.reason\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 96,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 91,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [appointment.status === 'pending' && /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      children: \"Confirm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 101,\n                      columnNumber: 27\n                    }, this), appointment.status === 'confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline\",\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 105,\n                        columnNumber: 29\n                      }, this), \"Start Visit\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 104,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this)\n              }, appointment.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              className: \"w-full justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                className: \"mr-2 h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), \"View All Patients\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              className: \"w-full justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"mr-2 h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), \"Manage Schedule\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              className: \"w-full justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(FileText, {\n                className: \"mr-2 h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), \"Prescription Pad\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              children: \"Availability Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Currently Available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-green-600\",\n                    children: \"Online\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-muted-foreground\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Today: 9:00 AM - 6:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Next available: Tomorrow 9:00 AM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                size: \"sm\",\n                className: \"w-full\",\n                children: \"Update Availability\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(DoctorDashboard, \"LngQ16GzRNiFRzmehLf5ShHFUs8=\");\n_c = DoctorDashboard;\nexport default DoctorDashboard;\nvar _c;\n$RefreshReg$(_c, \"DoctorDashboard\");", "map": {"version": 3, "names": ["React", "useState", "styles", "jsxDEV", "_jsxDEV", "DoctorDashboard", "_s", "selectedDate", "setSelectedDate", "Date", "toISOString", "split", "stats", "title", "value", "icon", "change", "appointments", "id", "patient", "time", "reason", "status", "getStatusColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "variant", "FileText", "Stethoscope", "map", "stat", "index", "IconComponent", "Card", "<PERSON><PERSON><PERSON><PERSON>", "color", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "type", "appointment", "Badge", "Clock", "size", "CheckCircle", "Users", "Calendar", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/DoctorDashboard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styles from './DoctorDashboard.module.css';\n\nconst DoctorDashboard = () => {\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n\n  const stats = [\n    { title: \"Today's Appointments\", value: '8', icon: '📅', change: '+2 from yesterday' },\n    { title: 'This Week', value: '32', icon: '📊', change: '+5 from last week' },\n    { title: 'Total Patients', value: '156', icon: '👥', change: '+12 this month' },\n    { title: 'Pending Reviews', value: '3', icon: '⏳', change: 'Needs attention' },\n  ];\n\n  const appointments = [\n    { id: 1, patient: '<PERSON>', time: '09:00', reason: 'Regular checkup', status: 'confirmed' },\n    { id: 2, patient: '<PERSON>', time: '10:30', reason: 'Follow-up consultation', status: 'pending' },\n    { id: 3, patient: '<PERSON>', time: '14:00', reason: 'Chest pain evaluation', status: 'confirmed' },\n    { id: 4, patient: '<PERSON>', time: '15:30', reason: 'Medication review', status: 'completed' },\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'confirmed': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'completed': return 'bg-blue-100 text-blue-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold\">Doctor Dashboard</h1>\n          <p className=\"text-muted-foreground\">Welcome back, Dr. Sarah Johnson</p>\n        </div>\n        <div className=\"flex space-x-4\">\n          <Button variant=\"outline\">\n            <FileText className=\"mr-2 h-4 w-4\" />\n            Patient Records\n          </Button>\n          <Button>\n            <Stethoscope className=\"mr-2 h-4 w-4\" />\n            Update Availability\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => {\n          const IconComponent = stat.icon;\n          return (\n            <Card key={index}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">{stat.title}</p>\n                    <p className=\"text-2xl font-bold\">{stat.value}</p>\n                  </div>\n                  <IconComponent className={`h-8 w-8 ${stat.color}`} />\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n\n      {/* Appointments */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        <div className=\"lg:col-span-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                Today's Appointments\n                <input type=\"date\" className=\"text-sm border rounded px-2 py-1\" />\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {appointments.map((appointment) => (\n                  <div key={appointment.id} className=\"border rounded-lg p-4 hover:shadow-md transition-shadow\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-2\">\n                          <h3 className=\"font-semibold text-lg\">{appointment.patient}</h3>\n                          <Badge className={getStatusColor(appointment.status)}>\n                            {appointment.status}\n                          </Badge>\n                        </div>\n                        <div className=\"text-sm text-muted-foreground space-y-1\">\n                          <div className=\"flex items-center gap-2\">\n                            <Clock className=\"h-4 w-4\" />\n                            {appointment.time}\n                          </div>\n                          <div>{appointment.reason}</div>\n                        </div>\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        {appointment.status === 'pending' && (\n                          <Button size=\"sm\">Confirm</Button>\n                        )}\n                        {appointment.status === 'confirmed' && (\n                          <Button size=\"sm\" variant=\"outline\">\n                            <CheckCircle className=\"h-4 w-4 mr-1\" />\n                            Start Visit\n                          </Button>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        <div className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Quick Actions</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <Button variant=\"outline\" className=\"w-full justify-start\">\n                <Users className=\"mr-2 h-4 w-4\" />\n                View All Patients\n              </Button>\n              <Button variant=\"outline\" className=\"w-full justify-start\">\n                <Calendar className=\"mr-2 h-4 w-4\" />\n                Manage Schedule\n              </Button>\n              <Button variant=\"outline\" className=\"w-full justify-start\">\n                <FileText className=\"mr-2 h-4 w-4\" />\n                Prescription Pad\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Availability Status</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span>Currently Available</span>\n                  <div className=\"flex items-center\">\n                    <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n                    <span className=\"text-sm text-green-600\">Online</span>\n                  </div>\n                </div>\n                <div className=\"text-sm text-muted-foreground\">\n                  <div>Today: 9:00 AM - 6:00 PM</div>\n                  <div>Next available: Tomorrow 9:00 AM</div>\n                </div>\n                <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                  Update Availability\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DoctorDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,IAAIQ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAExF,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,sBAAsB;IAAEC,KAAK,EAAE,GAAG;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAoB,CAAC,EACtF;IAAEH,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAoB,CAAC,EAC5E;IAAEH,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAiB,CAAC,EAC/E;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,GAAG;IAAEC,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAkB,CAAC,CAC/E;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,UAAU;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,iBAAiB;IAAEC,MAAM,EAAE;EAAY,CAAC,EAC7F;IAAEJ,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,YAAY;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,wBAAwB;IAAEC,MAAM,EAAE;EAAU,CAAC,EACpG;IAAEJ,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,uBAAuB;IAAEC,MAAM,EAAE;EAAY,CAAC,EACtG;IAAEJ,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,mBAAmB;IAAEC,MAAM,EAAE;EAAY,CAAC,CACnG;EAED,MAAMC,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,6BAA6B;MACtD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,WAAW;QAAE,OAAO,2BAA2B;MACpD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,oBACElB,OAAA;IAAKoB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBrB,OAAA;MAAKoB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrB,OAAA;QAAAqB,QAAA,gBACErB,OAAA;UAAIoB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDzB,OAAA;UAAGoB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrB,OAAA,CAAC0B,MAAM;UAACC,OAAO,EAAC,SAAS;UAAAN,QAAA,gBACvBrB,OAAA,CAAC4B,QAAQ;YAACR,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzB,OAAA,CAAC0B,MAAM;UAAAL,QAAA,gBACLrB,OAAA,CAAC6B,WAAW;YAACT,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEb,KAAK,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC1B,MAAMC,aAAa,GAAGF,IAAI,CAACpB,IAAI;QAC/B,oBACEX,OAAA,CAACkC,IAAI;UAAAb,QAAA,eACHrB,OAAA,CAACmC,WAAW;YAACf,SAAS,EAAC,KAAK;YAAAC,QAAA,eAC1BrB,OAAA;cAAKoB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAGoB,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEU,IAAI,CAACtB;gBAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEzB,OAAA;kBAAGoB,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEU,IAAI,CAACrB;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNzB,OAAA,CAACiC,aAAa;gBAACb,SAAS,EAAE,WAAWW,IAAI,CAACK,KAAK;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GATLO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDrB,OAAA;QAAKoB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BrB,OAAA,CAACkC,IAAI;UAAAb,QAAA,gBACHrB,OAAA,CAACqC,UAAU;YAAAhB,QAAA,eACTrB,OAAA,CAACsC,SAAS;cAAClB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,sBAEvD,eAAArB,OAAA;gBAAOuC,IAAI,EAAC,MAAM;gBAACnB,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACbzB,OAAA,CAACmC,WAAW;YAAAd,QAAA,eACVrB,OAAA;cAAKoB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBR,YAAY,CAACiB,GAAG,CAAEU,WAAW,iBAC5BxC,OAAA;gBAA0BoB,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,eAC3FrB,OAAA;kBAAKoB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDrB,OAAA;oBAAKoB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBrB,OAAA;sBAAKoB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/CrB,OAAA;wBAAIoB,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEmB,WAAW,CAACzB;sBAAO;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChEzB,OAAA,CAACyC,KAAK;wBAACrB,SAAS,EAAED,cAAc,CAACqB,WAAW,CAACtB,MAAM,CAAE;wBAAAG,QAAA,EAClDmB,WAAW,CAACtB;sBAAM;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNzB,OAAA;sBAAKoB,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,gBACtDrB,OAAA;wBAAKoB,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCrB,OAAA,CAAC0C,KAAK;0BAACtB,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC5Be,WAAW,CAACxB,IAAI;sBAAA;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eACNzB,OAAA;wBAAAqB,QAAA,EAAMmB,WAAW,CAACvB;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzB,OAAA;oBAAKoB,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAC5BmB,WAAW,CAACtB,MAAM,KAAK,SAAS,iBAC/BlB,OAAA,CAAC0B,MAAM;sBAACiB,IAAI,EAAC,IAAI;sBAAAtB,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAClC,EACAe,WAAW,CAACtB,MAAM,KAAK,WAAW,iBACjClB,OAAA,CAAC0B,MAAM;sBAACiB,IAAI,EAAC,IAAI;sBAAChB,OAAO,EAAC,SAAS;sBAAAN,QAAA,gBACjCrB,OAAA,CAAC4C,WAAW;wBAACxB,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA5BEe,WAAW,CAAC1B,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrB,OAAA,CAACkC,IAAI;UAAAb,QAAA,gBACHrB,OAAA,CAACqC,UAAU;YAAAhB,QAAA,eACTrB,OAAA,CAACsC,SAAS;cAAAjB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACbzB,OAAA,CAACmC,WAAW;YAACf,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAChCrB,OAAA,CAAC0B,MAAM;cAACC,OAAO,EAAC,SAAS;cAACP,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACxDrB,OAAA,CAAC6C,KAAK;gBAACzB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAEpC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzB,OAAA,CAAC0B,MAAM;cAACC,OAAO,EAAC,SAAS;cAACP,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACxDrB,OAAA,CAAC8C,QAAQ;gBAAC1B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzB,OAAA,CAAC0B,MAAM;cAACC,OAAO,EAAC,SAAS;cAACP,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACxDrB,OAAA,CAAC4B,QAAQ;gBAACR,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPzB,OAAA,CAACkC,IAAI;UAAAb,QAAA,gBACHrB,OAAA,CAACqC,UAAU;YAAAhB,QAAA,eACTrB,OAAA,CAACsC,SAAS;cAAAjB,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACbzB,OAAA,CAACmC,WAAW;YAAAd,QAAA,eACVrB,OAAA;cAAKoB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrB,OAAA;gBAAKoB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDrB,OAAA;kBAAAqB,QAAA,EAAM;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChCzB,OAAA;kBAAKoB,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrB,OAAA;oBAAKoB,SAAS,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9DzB,OAAA;oBAAMoB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzB,OAAA;gBAAKoB,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAC5CrB,OAAA;kBAAAqB,QAAA,EAAK;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnCzB,OAAA;kBAAAqB,QAAA,EAAK;gBAAgC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNzB,OAAA,CAAC0B,MAAM;gBAACC,OAAO,EAAC,SAAS;gBAACgB,IAAI,EAAC,IAAI;gBAACvB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,EAAC;cAEvD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAlKID,eAAe;AAAA8C,EAAA,GAAf9C,eAAe;AAoKrB,eAAeA,eAAe;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}