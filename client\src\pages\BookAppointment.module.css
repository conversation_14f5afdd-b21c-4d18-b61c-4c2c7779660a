.container {
  min-height: 100vh;
  background: var(--light-grey);
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.successToast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #10b981;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.checkIcon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.bookingCard {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 2rem;
  width: 100%;
  max-width: 600px;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.header p {
  color: #666;
  font-size: 1rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 500;
  color: var(--dark-grey);
  font-size: 0.875rem;
}

.dateTimeRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .bookingCard {
    padding: 1.5rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .dateTimeRow {
    grid-template-columns: 1fr;
  }
  
  .successToast {
    right: 10px;
    left: 10px;
    top: 10px;
  }
}

/* Form input styles are inherited from global CSS */
.form .input {
  margin-bottom: 0;
}

.form .input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 121, 107, 0.1);
}

.form textarea.input {
  resize: vertical;
  min-height: 100px;
}

.form .btn-primary {
  margin-top: 1rem;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
}
