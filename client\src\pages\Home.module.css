.home {
  min-height: 100vh;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: 4rem 0;
  margin-bottom: 3rem;
}

.heroContent {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.heroSubtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.heroActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .heroSubtitle {
    font-size: 1rem;
  }
  
  .heroActions {
    flex-direction: column;
    align-items: center;
  }
}

/* Features Section */
.features {
  padding: 3rem 0;
  background: var(--white);
}

.featureCard {
  text-align: center;
  padding: 2rem;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.featureCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.featureCard h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.featureCard p {
  color: #666;
  line-height: 1.6;
}

/* Search Section */
.searchSection {
  padding: 3rem 0;
  background: var(--light-grey);
}

.searchCard {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.searchCard h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.searchCard p {
  color: #666;
  margin-bottom: 1.5rem;
}

.searchBox {
  display: flex;
  gap: 1rem;
  align-items: center;
}

@media (max-width: 768px) {
  .searchBox {
    flex-direction: column;
  }
  
  .searchBox .input {
    margin-bottom: 0;
  }
}

/* Hospital Listings */
.hospitalListings {
  padding: 3rem 0;
}

.sectionTitle {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 2rem;
}

.noResults {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.noResults p {
  margin-bottom: 1rem;
  font-size: 1.125rem;
}

/* Stats Section */
.stats {
  padding: 3rem 0;
  background: var(--primary-color);
  color: var(--white);
}

.stats .sectionTitle {
  color: var(--white);
  margin-bottom: 3rem;
}

.statCard {
  text-align: center;
  padding: 1.5rem;
}

.statNumber {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--accent-color);
}

.statLabel {
  font-size: 1rem;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .statNumber {
    font-size: 2rem;
  }
}
