.container {
  min-height: 100vh;
  background: var(--light-grey);
}

/* Header */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  padding: 2rem 0;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.greeting h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 0.5rem 0;
}

.greeting p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

/* Main Content */
.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.statIcon {
  font-size: 2.5rem;
  background: rgba(0, 121, 107, 0.1);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statContent h3 {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.statChange {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  margin-bottom: 2rem;
  overflow: hidden;
}

.tabButton {
  flex: 1;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: var(--transition);
  border-right: 1px solid var(--border-color);
}

.tabButton:last-child {
  border-right: none;
}

.tabButton:hover {
  background: rgba(0, 121, 107, 0.05);
  color: var(--primary-color);
}

.tabActive {
  background: var(--primary-color);
  color: var(--white);
}

.tabActive:hover {
  background: var(--primary-dark);
  color: var(--white);
}

/* Tab Content */
.tabContent {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 2rem;
}

.tabContent h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1.5rem 0;
}

/* Appointments Section */
.appointmentsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.appointmentCard {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.appointmentCard:hover {
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.appointmentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.timeSlot {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  background: rgba(0, 121, 107, 0.1);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

.checkedin {
  background: #dbeafe;
  color: #1e40af;
}

.waiting {
  background: #fef3c7;
  color: #92400e;
}

.inprogress {
  background: #dcfce7;
  color: #166534;
}

.completed {
  background: #f3f4f6;
  color: #374151;
}

.scheduled {
  background: #e0e7ff;
  color: #3730a3;
}

.appointmentBody {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.patientInfo h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-grey);
  margin: 0 0 0.5rem 0;
}

.patientInfo p {
  font-size: 0.875rem;
  color: #666;
  margin: 0.25rem 0;
}

.appointmentActions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.appointmentActions button {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

/* Walk-in Section */
.walkinList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.walkinCard {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.walkinCard:hover {
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-color);
}

.walkinHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.patientName h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-grey);
  margin: 0 0 0.25rem 0;
}

.patientName p {
  font-size: 0.875rem;
  color: #666;
  margin: 0;
}

.priorityBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
}

.high {
  background: #fee2e2;
  color: #dc2626;
}

.normal {
  background: #dcfce7;
  color: #166534;
}

.walkinDetails {
  margin-bottom: 1rem;
}

.reason {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 0.5rem 0;
  font-style: italic;
}

.waitTime {
  font-size: 0.875rem;
  color: var(--accent-color);
  font-weight: 500;
  margin: 0;
}

.walkinActions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.walkinActions button {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.emptyState {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Actions Section */
.actionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.actionCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--light-grey);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.actionCard:hover {
  background: var(--white);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.actionIcon {
  font-size: 2rem;
  background: var(--white);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-light);
}

.actionContent h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dark-grey);
  margin: 0 0 0.25rem 0;
}

.actionContent p {
  font-size: 0.875rem;
  color: #666;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 0 1rem;
  }
  
  .headerActions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .mainContent {
    padding: 1rem;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .tabNavigation {
    flex-direction: column;
  }
  
  .tabButton {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .tabButton:last-child {
    border-bottom: none;
  }
  
  .appointmentBody {
    flex-direction: column;
    gap: 1rem;
  }
  
  .appointmentActions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .walkinHeader {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .walkinActions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .actionsGrid {
    grid-template-columns: 1fr;
  }
}
