"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=e(require("postcss-value-parser"));function t(e){const t=e.value.toLowerCase();if(!function(e,r){let t=!1,n=!1,u=!1;const a=r.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));for(let o=0;o<a.length;o++){const l=a[o];if("word"===l.type&&"from"===l.value.toLowerCase())return!1;"div"!==l.type||","!==l.value?e&&"word"===l.type&&l.value.endsWith("%")?u=!0:o===r.length-1&&"word"===l.type&&l.value.endsWith("%")&&(n=!0):t=!0}if(t&&(n||u))return!0;if(t)return!1;return!0}("rgb"===t||"rgba"===t,e.nodes))return;const i=function(e){let r=0;for(let t=0;t<e.length;t++){const n=e[t];if("div"===n.type&&","===n.value){if(r<2&&(n.value=" ",n.type="space"),2===r&&(n.value="/"),r>2)return;r++}}return e}(e.nodes),s=i.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));let f=null;if("hsl"===t||"hsla"===t?f=function(e){if(!function(e){if(!e||"word"!==e.type)return!1;if(!l(e))return!1;const t=r.default.unit(e.value);if(!t)return!1;const n=t.unit.toLowerCase();return!!t.number&&("deg"===n||"grad"===n||"rad"===n||"turn"===n||""===t.unit)}(e[0]))return null;if(!n(e[1]))return null;if(!n(e[2]))return null;const t={h:r.default.unit(e[0].value),hNode:e[0],s:r.default.unit(e[1].value),sNode:e[1],l:r.default.unit(e[2].value),lNode:e[2]};if(function(e){switch(e.unit.toLowerCase()){case"deg":return void(e.unit="");case"rad":return e.unit="",void(e.number=Math.round(180*parseFloat(e.number)/Math.PI).toString());case"grad":return e.unit="",void(e.number=Math.round(.9*parseFloat(e.number)).toString());case"turn":e.unit="",e.number=Math.round(360*parseFloat(e.number)).toString()}}(t.h),""!==t.h.unit)return null;t.hNode.value=t.h.number,o(e[3])&&(t.slash=e[3]);(n(e[4])||u(e[4])||a(e[4]))&&(t.alpha=e[4]);return t}(s):"rgb"!==t&&"rgba"!==t||(f=function(e){if(!n(e[0]))return null;if(!n(e[1]))return null;if(!n(e[2]))return null;const t={r:r.default.unit(e[0].value),rNode:e[0],g:r.default.unit(e[1].value),gNode:e[1],b:r.default.unit(e[2].value),bNode:e[2]};"%"===t.r.unit&&(t.r.number=String(Math.floor(Number(t.r.number)/100*255)),t.rNode.value=t.r.number);"%"===t.g.unit&&(t.g.number=String(Math.floor(Number(t.g.number)/100*255)),t.gNode.value=t.g.number);"%"===t.b.unit&&(t.b.number=String(Math.floor(Number(t.b.number)/100*255)),t.bNode.value=t.b.number);o(e[3])&&(t.slash=e[3]);(n(e[4])||u(e[4])||a(e[4]))&&(t.alpha=e[4]);return t}(s)),!f)return;if(s.length>3&&(!f.slash||!f.alpha))return;!function(e,t,n){"hsl"===e.value.toLowerCase()||"hsla"===e.value.toLowerCase()?e.value="hsl":"rgb"!==e.value.toLowerCase()&&"rgba"!==e.value.toLowerCase()||(e.value="rgb");if(!t||!n)return;"hsl"===e.value.toLowerCase()?e.value="hsla":e.value="rgba";if(t.value=",",t.before="",!function(e){if(!e||"word"!==e.type)return!1;if(!l(e))return!1;const t=r.default.unit(e.value);if(!t)return!1;return!!t.number}(n))return;const u=r.default.unit(n.value);if(!u)return;"%"===u.unit&&(u.number=String(parseFloat(u.number)/100),n.value=String(u.number))}(e,f.slash,f.alpha);const[c,d]=function(e){if(function(e){if(void 0!==e.r)return!0;return!1}(e))return[e.rNode,e.gNode,e.bNode];return[e.hNode,e.sNode,e.lNode]}(f);e.nodes.splice(e.nodes.indexOf(c)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),e.nodes.splice(e.nodes.indexOf(d)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""})}function n(e){if(!e||"word"!==e.type)return!1;if(!l(e))return!1;const t=r.default.unit(e.value);return!!t&&("%"===t.unit||""===t.unit)}function u(e){return e&&"function"===e.type&&"calc"===e.value.toLowerCase()}function a(e){return e&&"function"===e.type&&"var"===e.value.toLowerCase()}function o(e){return e&&"div"===e.type&&"/"===e.value}function l(e){if(!e||!e.value)return!1;try{return!1!==r.default.unit(e.value)}catch(e){return!1}}const i=e=>{const n="preserve"in Object(e)&&Boolean(e.preserve);return{postcssPlugin:"postcss-color-functional-notation",Declaration:(e,{result:u,postcss:a})=>{if(function(e){let r=e.parent;for(;r;)if("atrule"===r.type){if("supports"===r.name&&-1!==r.params.toLowerCase().indexOf("(color: rgb(0 0 0 / 0.5)) and (color: hsl(0 0% 0% / 0.5))"))return!0;r=r.parent}else r=r.parent;return!1}(e))return;const o=e.value,l=o.toLowerCase();if(!(l.includes("rgb")||l.includes("rgba")||l.includes("hsl")||l.includes("hsla")))return;let i;try{i=r.default(o)}catch(r){e.warn(u,`Failed to parse value '${o}' as a hsl or rgb function. Leaving the original value intact.`)}if(void 0===i)return;i.walk((e=>{if(!e.type||"function"!==e.type)return;const r=e.value.toLowerCase();"hsl"!==r&&"hsla"!==r&&"rgb"!==r&&"rgba"!==r||t(e)}));const s=String(i);if(s!==o)if(n&&e.variable){const r=e.parent,t="(color: rgb(0 0 0 / 0.5)) and (color: hsl(0 0% 0% / 0.5))",n=a.atRule({name:"supports",params:t,source:e.source}),u=r.clone();u.removeAll(),u.append(e.clone()),n.append(u);let o=r,l=r.next();for(;o&&l&&"atrule"===l.type&&"supports"===l.name&&l.params===t;)o=l,l=l.next();o.after(n),e.replaceWith(e.clone({value:s}))}else n?e.cloneBefore({value:s}):e.replaceWith(e.clone({value:s}))}}};i.postcss=!0,module.exports=i;
