{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n    // Clear specific error when user types\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n    setFormData({\n      ...formData,\n      role: tab\n    });\n    setErrors({});\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Common validations\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n\n    // Hospital specific validations\n    if (activeTab === 'hospital') {\n      if (!formData.hospitalName.trim()) {\n        newErrors.hospitalName = 'Hospital name is required';\n      }\n      if (!formData.address.trim()) {\n        newErrors.address = 'Address is required';\n      }\n      if (!formData.city.trim()) {\n        newErrors.city = 'City is required';\n      }\n      if (!formData.licenseNumber.trim()) {\n        newErrors.licenseNumber = 'License number is required';\n      }\n    }\n    return newErrors;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n\n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name,\n        hospitalName: formData.hospitalName || null\n      }));\n      setIsLoading(false);\n\n      // Redirect based on role\n      if (activeTab === 'hospital') {\n        navigate('/hospital-dashboard');\n      } else {\n        navigate('/login');\n      }\n    }, 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.header,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.headerContent,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.logo,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.logoIcon,\n            children: \"\\uD83C\\uDFE5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.logoText,\n            children: \"Hope Medics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Join Hope Medics Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Register as a patient or hospital to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.mainContent,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.tabNavigation,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.tabButton} ${activeTab === 'patient' ? styles.tabActive : ''}`,\n          onClick: () => handleTabChange('patient'),\n          children: \"\\uD83D\\uDC64 Patient Registration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.tabButton} ${activeTab === 'hospital' ? styles.tabActive : ''}`,\n          onClick: () => handleTabChange('hospital'),\n          children: \"\\uD83C\\uDFE5 Hospital Registration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.formContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: styles.form,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.section,\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Basic Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formRow,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"name\",\n                  children: [activeTab === 'hospital' ? 'Contact Person Name' : 'Full Name', \" *\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"name\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  placeholder: activeTab === 'hospital' ? 'Enter contact person name' : 'Enter your full name',\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), errors.name && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 35\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"email\",\n                  children: \"Email Address *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  id: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  placeholder: \"Enter email address\",\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 36\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formRow,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"phone\",\n                  children: \"Phone Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  id: \"phone\",\n                  name: \"phone\",\n                  value: formData.phone,\n                  onChange: handleChange,\n                  placeholder: \"+91 98765 43210\",\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.phone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 36\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), activeTab === 'patient' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"gender\",\n                  children: \"Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"gender\",\n                  name: \"gender\",\n                  value: formData.gender,\n                  onChange: handleChange,\n                  className: \"input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Gender\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"male\",\n                    children: \"Male\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"female\",\n                    children: \"Female\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"other\",\n                    children: \"Other\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), activeTab === 'patient' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formRow,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"dateOfBirth\",\n                  children: \"Date of Birth\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  id: \"dateOfBirth\",\n                  name: \"dateOfBirth\",\n                  value: formData.dateOfBirth,\n                  onChange: handleChange,\n                  className: \"input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), activeTab === 'hospital' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.section,\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Hospital Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formRow,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"hospitalName\",\n                  children: \"Hospital Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"hospitalName\",\n                  name: \"hospitalName\",\n                  value: formData.hospitalName,\n                  onChange: handleChange,\n                  placeholder: \"Enter hospital name\",\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), errors.hospitalName && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.hospitalName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"licenseNumber\",\n                  children: \"License Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"licenseNumber\",\n                  name: \"licenseNumber\",\n                  value: formData.licenseNumber,\n                  onChange: handleChange,\n                  placeholder: \"Enter license number\",\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this), errors.licenseNumber && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.licenseNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 46\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formGroup,\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"address\",\n                children: \"Hospital Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"address\",\n                name: \"address\",\n                value: formData.address,\n                onChange: handleChange,\n                placeholder: \"Enter complete hospital address\",\n                className: \"input\",\n                rows: \"3\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), errors.address && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.error,\n                children: errors.address\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 38\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formRow,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"city\",\n                  children: \"City *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"city\",\n                  name: \"city\",\n                  value: formData.city,\n                  onChange: handleChange,\n                  placeholder: \"Enter city\",\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this), errors.city && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.city\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"state\",\n                  children: \"State\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"state\",\n                  name: \"state\",\n                  value: formData.state,\n                  onChange: handleChange,\n                  className: \"input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select State\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"delhi\",\n                    children: \"Delhi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"maharashtra\",\n                    children: \"Maharashtra\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"karnataka\",\n                    children: \"Karnataka\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"tamil-nadu\",\n                    children: \"Tamil Nadu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"gujarat\",\n                    children: \"Gujarat\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"rajasthan\",\n                    children: \"Rajasthan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"uttar-pradesh\",\n                    children: \"Uttar Pradesh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"west-bengal\",\n                    children: \"West Bengal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"pincode\",\n                  children: \"Pincode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"pincode\",\n                  name: \"pincode\",\n                  value: formData.pincode,\n                  onChange: handleChange,\n                  placeholder: \"Enter pincode\",\n                  className: \"input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formRow,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"hospitalType\",\n                  children: \"Hospital Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"hospitalType\",\n                  name: \"hospitalType\",\n                  value: formData.hospitalType,\n                  onChange: handleChange,\n                  className: \"input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"government\",\n                    children: \"Government\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"private\",\n                    children: \"Private\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"trust\",\n                    children: \"Trust/NGO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"corporate\",\n                    children: \"Corporate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"totalBeds\",\n                  children: \"Total Beds\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"totalBeds\",\n                  name: \"totalBeds\",\n                  value: formData.totalBeds,\n                  onChange: handleChange,\n                  placeholder: \"Enter total beds\",\n                  className: \"input\",\n                  min: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"establishedYear\",\n                  children: \"Established Year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"establishedYear\",\n                  name: \"establishedYear\",\n                  value: formData.establishedYear,\n                  onChange: handleChange,\n                  placeholder: \"Enter year\",\n                  className: \"input\",\n                  min: \"1900\",\n                  max: new Date().getFullYear()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formGroup,\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                children: \"Hospital Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleChange,\n                placeholder: \"Brief description of your hospital, specialties, and services\",\n                className: \"input\",\n                rows: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.checkboxGroup,\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Services Available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.checkboxRow,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: styles.checkboxLabel,\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"emergencyServices\",\n                    checked: formData.emergencyServices,\n                    onChange: handleChange,\n                    className: styles.checkbox\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 23\n                  }, this), \"\\uD83D\\uDEA8 24/7 Emergency Services\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: styles.checkboxLabel,\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"ambulanceService\",\n                    checked: formData.ambulanceService,\n                    onChange: handleChange,\n                    className: styles.checkbox\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), \"\\uD83D\\uDE91 Ambulance Service\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.section,\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.formRow,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"password\",\n                  children: \"Password *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  id: \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleChange,\n                  placeholder: \"Enter password (min 6 characters)\",\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this), errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.password\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 39\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.formGroup,\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"confirmPassword\",\n                  children: \"Confirm Password *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  id: \"confirmPassword\",\n                  name: \"confirmPassword\",\n                  value: formData.confirmPassword,\n                  onChange: handleChange,\n                  placeholder: \"Confirm your password\",\n                  className: \"input\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.error,\n                  children: errors.confirmPassword\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 46\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.section,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.termsSection,\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: styles.checkboxLabel,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"terms\",\n                  required: true,\n                  className: styles.checkbox\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this), \"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: styles.link,\n                  children: \"Terms and Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: styles.link,\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"btn-primary w-full\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: styles.spinner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 21\n                }, this), activeTab === 'hospital' ? 'Registering Hospital...' : 'Creating Account...']\n              }, void 0, true) : activeTab === 'hospital' ? '🏥 Register Hospital' : '👤 Create Account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.loginLink,\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: styles.link,\n              children: \"Sign in here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"PW8Qs9IORXp2FcZbLYvkiJt4RU4=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "activeTab", "setActiveTab", "formData", "setFormData", "name", "email", "password", "confirmPassword", "phone", "role", "dateOfBirth", "gender", "hospitalName", "address", "city", "state", "pincode", "licenseNumber", "establishedYear", "hospitalType", "totalBeds", "emergencyServices", "ambulanceService", "description", "isLoading", "setIsLoading", "errors", "setErrors", "navigate", "handleChange", "e", "value", "type", "checked", "target", "handleTabChange", "tab", "validateForm", "newErrors", "trim", "test", "length", "handleSubmit", "preventDefault", "Object", "keys", "setTimeout", "console", "log", "localStorage", "setItem", "JSON", "stringify", "className", "container", "children", "header", "headerContent", "logo", "logoIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "logoText", "mainContent", "tabNavigation", "tabButton", "tabActive", "onClick", "formContainer", "onSubmit", "form", "section", "formRow", "formGroup", "htmlFor", "id", "onChange", "placeholder", "required", "error", "rows", "min", "max", "Date", "getFullYear", "checkboxGroup", "checkboxRow", "checkboxLabel", "checkbox", "termsSection", "to", "link", "disabled", "spinner", "loginLink", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\n\nconst Register = () => {\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n    // Clear specific error when user types\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n    setFormData({\n      ...formData,\n      role: tab\n    });\n    setErrors({});\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Common validations\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n\n    // Hospital specific validations\n    if (activeTab === 'hospital') {\n      if (!formData.hospitalName.trim()) {\n        newErrors.hospitalName = 'Hospital name is required';\n      }\n      if (!formData.address.trim()) {\n        newErrors.address = 'Address is required';\n      }\n      if (!formData.city.trim()) {\n        newErrors.city = 'City is required';\n      }\n      if (!formData.licenseNumber.trim()) {\n        newErrors.licenseNumber = 'License number is required';\n      }\n    }\n\n    return newErrors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n\n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name,\n        hospitalName: formData.hospitalName || null\n      }));\n\n      setIsLoading(false);\n\n      // Redirect based on role\n      if (activeTab === 'hospital') {\n        navigate('/hospital-dashboard');\n      } else {\n        navigate('/login');\n      }\n    }, 2000);\n  };\n\n  return (\n    <div className={styles.container}>\n      {/* Header */}\n      <div className={styles.header}>\n        <div className={styles.headerContent}>\n          <div className={styles.logo}>\n            <span className={styles.logoIcon}>🏥</span>\n            <span className={styles.logoText}>Hope Medics</span>\n          </div>\n          <h1>Join Hope Medics Platform</h1>\n          <p>Register as a patient or hospital to get started</p>\n        </div>\n      </div>\n\n      <div className={styles.mainContent}>\n        {/* Tab Navigation */}\n        <div className={styles.tabNavigation}>\n          <button\n            className={`${styles.tabButton} ${activeTab === 'patient' ? styles.tabActive : ''}`}\n            onClick={() => handleTabChange('patient')}\n          >\n            👤 Patient Registration\n          </button>\n          <button\n            className={`${styles.tabButton} ${activeTab === 'hospital' ? styles.tabActive : ''}`}\n            onClick={() => handleTabChange('hospital')}\n          >\n            🏥 Hospital Registration\n          </button>\n        </div>\n\n        {/* Registration Form */}\n        <div className={styles.formContainer}>\n          <form onSubmit={handleSubmit} className={styles.form}>\n            {/* Common Fields */}\n            <div className={styles.section}>\n              <h3>Basic Information</h3>\n\n              <div className={styles.formRow}>\n                <div className={styles.formGroup}>\n                  <label htmlFor=\"name\">\n                    {activeTab === 'hospital' ? 'Contact Person Name' : 'Full Name'} *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    placeholder={activeTab === 'hospital' ? 'Enter contact person name' : 'Enter your full name'}\n                    className=\"input\"\n                    required\n                  />\n                  {errors.name && <span className={styles.error}>{errors.name}</span>}\n                </div>\n\n                <div className={styles.formGroup}>\n                  <label htmlFor=\"email\">Email Address *</label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    placeholder=\"Enter email address\"\n                    className=\"input\"\n                    required\n                  />\n                  {errors.email && <span className={styles.error}>{errors.email}</span>}\n                </div>\n              </div>\n\n              <div className={styles.formRow}>\n                <div className={styles.formGroup}>\n                  <label htmlFor=\"phone\">Phone Number *</label>\n                  <input\n                    type=\"tel\"\n                    id=\"phone\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleChange}\n                    placeholder=\"+91 98765 43210\"\n                    className=\"input\"\n                    required\n                  />\n                  {errors.phone && <span className={styles.error}>{errors.phone}</span>}\n                </div>\n\n                {activeTab === 'patient' && (\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"gender\">Gender</label>\n                    <select\n                      id=\"gender\"\n                      name=\"gender\"\n                      value={formData.gender}\n                      onChange={handleChange}\n                      className=\"input\"\n                    >\n                      <option value=\"\">Select Gender</option>\n                      <option value=\"male\">Male</option>\n                      <option value=\"female\">Female</option>\n                      <option value=\"other\">Other</option>\n                    </select>\n                  </div>\n                )}\n              </div>\n\n              {activeTab === 'patient' && (\n                <div className={styles.formRow}>\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"dateOfBirth\">Date of Birth</label>\n                    <input\n                      type=\"date\"\n                      id=\"dateOfBirth\"\n                      name=\"dateOfBirth\"\n                      value={formData.dateOfBirth}\n                      onChange={handleChange}\n                      className=\"input\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Hospital Specific Fields */}\n            {activeTab === 'hospital' && (\n              <div className={styles.section}>\n                <h3>Hospital Information</h3>\n\n                <div className={styles.formRow}>\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"hospitalName\">Hospital Name *</label>\n                    <input\n                      type=\"text\"\n                      id=\"hospitalName\"\n                      name=\"hospitalName\"\n                      value={formData.hospitalName}\n                      onChange={handleChange}\n                      placeholder=\"Enter hospital name\"\n                      className=\"input\"\n                      required\n                    />\n                    {errors.hospitalName && <span className={styles.error}>{errors.hospitalName}</span>}\n                  </div>\n\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"licenseNumber\">License Number *</label>\n                    <input\n                      type=\"text\"\n                      id=\"licenseNumber\"\n                      name=\"licenseNumber\"\n                      value={formData.licenseNumber}\n                      onChange={handleChange}\n                      placeholder=\"Enter license number\"\n                      className=\"input\"\n                      required\n                    />\n                    {errors.licenseNumber && <span className={styles.error}>{errors.licenseNumber}</span>}\n                  </div>\n                </div>\n\n                <div className={styles.formGroup}>\n                  <label htmlFor=\"address\">Hospital Address *</label>\n                  <textarea\n                    id=\"address\"\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleChange}\n                    placeholder=\"Enter complete hospital address\"\n                    className=\"input\"\n                    rows=\"3\"\n                    required\n                  />\n                  {errors.address && <span className={styles.error}>{errors.address}</span>}\n                </div>\n\n                <div className={styles.formRow}>\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"city\">City *</label>\n                    <input\n                      type=\"text\"\n                      id=\"city\"\n                      name=\"city\"\n                      value={formData.city}\n                      onChange={handleChange}\n                      placeholder=\"Enter city\"\n                      className=\"input\"\n                      required\n                    />\n                    {errors.city && <span className={styles.error}>{errors.city}</span>}\n                  </div>\n\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"state\">State</label>\n                    <select\n                      id=\"state\"\n                      name=\"state\"\n                      value={formData.state}\n                      onChange={handleChange}\n                      className=\"input\"\n                    >\n                      <option value=\"\">Select State</option>\n                      <option value=\"delhi\">Delhi</option>\n                      <option value=\"maharashtra\">Maharashtra</option>\n                      <option value=\"karnataka\">Karnataka</option>\n                      <option value=\"tamil-nadu\">Tamil Nadu</option>\n                      <option value=\"gujarat\">Gujarat</option>\n                      <option value=\"rajasthan\">Rajasthan</option>\n                      <option value=\"uttar-pradesh\">Uttar Pradesh</option>\n                      <option value=\"west-bengal\">West Bengal</option>\n                    </select>\n                  </div>\n\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"pincode\">Pincode</label>\n                    <input\n                      type=\"text\"\n                      id=\"pincode\"\n                      name=\"pincode\"\n                      value={formData.pincode}\n                      onChange={handleChange}\n                      placeholder=\"Enter pincode\"\n                      className=\"input\"\n                    />\n                  </div>\n                </div>\n\n                <div className={styles.formRow}>\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"hospitalType\">Hospital Type</label>\n                    <select\n                      id=\"hospitalType\"\n                      name=\"hospitalType\"\n                      value={formData.hospitalType}\n                      onChange={handleChange}\n                      className=\"input\"\n                    >\n                      <option value=\"\">Select Type</option>\n                      <option value=\"government\">Government</option>\n                      <option value=\"private\">Private</option>\n                      <option value=\"trust\">Trust/NGO</option>\n                      <option value=\"corporate\">Corporate</option>\n                    </select>\n                  </div>\n\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"totalBeds\">Total Beds</label>\n                    <input\n                      type=\"number\"\n                      id=\"totalBeds\"\n                      name=\"totalBeds\"\n                      value={formData.totalBeds}\n                      onChange={handleChange}\n                      placeholder=\"Enter total beds\"\n                      className=\"input\"\n                      min=\"1\"\n                    />\n                  </div>\n\n                  <div className={styles.formGroup}>\n                    <label htmlFor=\"establishedYear\">Established Year</label>\n                    <input\n                      type=\"number\"\n                      id=\"establishedYear\"\n                      name=\"establishedYear\"\n                      value={formData.establishedYear}\n                      onChange={handleChange}\n                      placeholder=\"Enter year\"\n                      className=\"input\"\n                      min=\"1900\"\n                      max={new Date().getFullYear()}\n                    />\n                  </div>\n                </div>\n\n                <div className={styles.formGroup}>\n                  <label htmlFor=\"description\">Hospital Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleChange}\n                    placeholder=\"Brief description of your hospital, specialties, and services\"\n                    className=\"input\"\n                    rows=\"4\"\n                  />\n                </div>\n\n                <div className={styles.checkboxGroup}>\n                  <h4>Services Available</h4>\n                  <div className={styles.checkboxRow}>\n                    <label className={styles.checkboxLabel}>\n                      <input\n                        type=\"checkbox\"\n                        name=\"emergencyServices\"\n                        checked={formData.emergencyServices}\n                        onChange={handleChange}\n                        className={styles.checkbox}\n                      />\n                      🚨 24/7 Emergency Services\n                    </label>\n\n                    <label className={styles.checkboxLabel}>\n                      <input\n                        type=\"checkbox\"\n                        name=\"ambulanceService\"\n                        checked={formData.ambulanceService}\n                        onChange={handleChange}\n                        className={styles.checkbox}\n                      />\n                      🚑 Ambulance Service\n                    </label>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Password Section */}\n            <div className={styles.section}>\n              <h3>Security</h3>\n\n              <div className={styles.formRow}>\n                <div className={styles.formGroup}>\n                  <label htmlFor=\"password\">Password *</label>\n                  <input\n                    type=\"password\"\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleChange}\n                    placeholder=\"Enter password (min 6 characters)\"\n                    className=\"input\"\n                    required\n                  />\n                  {errors.password && <span className={styles.error}>{errors.password}</span>}\n                </div>\n\n                <div className={styles.formGroup}>\n                  <label htmlFor=\"confirmPassword\">Confirm Password *</label>\n                  <input\n                    type=\"password\"\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    value={formData.confirmPassword}\n                    onChange={handleChange}\n                    placeholder=\"Confirm your password\"\n                    className=\"input\"\n                    required\n                  />\n                  {errors.confirmPassword && <span className={styles.error}>{errors.confirmPassword}</span>}\n                </div>\n              </div>\n            </div>\n\n            {/* Terms and Submit */}\n            <div className={styles.section}>\n              <div className={styles.termsSection}>\n                <label className={styles.checkboxLabel}>\n                  <input\n                    type=\"checkbox\"\n                    name=\"terms\"\n                    required\n                    className={styles.checkbox}\n                  />\n                  I agree to the{' '}\n                  <Link to=\"#\" className={styles.link}>Terms and Conditions</Link>{' '}\n                  and{' '}\n                  <Link to=\"#\" className={styles.link}>Privacy Policy</Link>\n                </label>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"btn-primary w-full\"\n              >\n                {isLoading ? (\n                  <>\n                    <span className={styles.spinner}></span>\n                    {activeTab === 'hospital' ? 'Registering Hospital...' : 'Creating Account...'}\n                  </>\n                ) : (\n                  activeTab === 'hospital' ? '🏥 Register Hospital' : '👤 Create Account'\n                )}\n              </button>\n            </div>\n          </form>\n\n          {/* Login Link */}\n          <div className={styles.loginLink}>\n            <p>\n              Already have an account?{' '}\n              <Link to=\"/login\" className={styles.link}>\n                Sign in here\n              </Link>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACvC;IACAc,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,SAAS;IAEf;IACAC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IAEV;IACAC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,iBAAiB,EAAE,KAAK;IACxBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMsC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAE9B,MAAMqC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/C/B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAG4B,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC;IACF;IACA,IAAIL,MAAM,CAACtB,IAAI,CAAC,EAAE;MAChBuB,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACtB,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM+B,eAAe,GAAIC,GAAG,IAAK;IAC/BnC,YAAY,CAACmC,GAAG,CAAC;IACjBjC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXO,IAAI,EAAE2B;IACR,CAAC,CAAC;IACFT,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACpC,QAAQ,CAACE,IAAI,CAACmC,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAClC,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACkC,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACjC,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACmC,IAAI,CAACtC,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/CiC,SAAS,CAACjC,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBgC,SAAS,CAAChC,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACmC,MAAM,GAAG,CAAC,EAAE;MACvCH,SAAS,CAAChC,QAAQ,GAAG,wCAAwC;IAC/D;IAEA,IAAIJ,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClD+B,SAAS,CAAC/B,eAAe,GAAG,wBAAwB;IACtD;IAEA,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC9B,KAAK,GAAG,0BAA0B;IAC9C;;IAEA;IACA,IAAIR,SAAS,KAAK,UAAU,EAAE;MAC5B,IAAI,CAACE,QAAQ,CAACU,YAAY,CAAC2B,IAAI,CAAC,CAAC,EAAE;QACjCD,SAAS,CAAC1B,YAAY,GAAG,2BAA2B;MACtD;MACA,IAAI,CAACV,QAAQ,CAACW,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAE;QAC5BD,SAAS,CAACzB,OAAO,GAAG,qBAAqB;MAC3C;MACA,IAAI,CAACX,QAAQ,CAACY,IAAI,CAACyB,IAAI,CAAC,CAAC,EAAE;QACzBD,SAAS,CAACxB,IAAI,GAAG,kBAAkB;MACrC;MACA,IAAI,CAACZ,QAAQ,CAACe,aAAa,CAACsB,IAAI,CAAC,CAAC,EAAE;QAClCD,SAAS,CAACrB,aAAa,GAAG,4BAA4B;MACxD;IACF;IAEA,OAAOqB,SAAS;EAClB,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,MAAML,SAAS,GAAGD,YAAY,CAAC,CAAC;IAChC,IAAIO,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACrCd,SAAS,CAACW,SAAS,CAAC;MACpB;IACF;IAEAb,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEb;IACAmB,UAAU,CAAC,MAAM;MACfC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE9C,QAAQ,CAAC;;MAE3C;MACA+C,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;QAC1C/C,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBI,IAAI,EAAEP,QAAQ,CAACO,IAAI;QACnBL,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBQ,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI;MACzC,CAAC,CAAC,CAAC;MAEHa,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,IAAIzB,SAAS,KAAK,UAAU,EAAE;QAC5B4B,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,MAAM;QACLA,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEjC,OAAA;IAAK0D,SAAS,EAAE5D,MAAM,CAAC6D,SAAU;IAAAC,QAAA,gBAE/B5D,OAAA;MAAK0D,SAAS,EAAE5D,MAAM,CAAC+D,MAAO;MAAAD,QAAA,eAC5B5D,OAAA;QAAK0D,SAAS,EAAE5D,MAAM,CAACgE,aAAc;QAAAF,QAAA,gBACnC5D,OAAA;UAAK0D,SAAS,EAAE5D,MAAM,CAACiE,IAAK;UAAAH,QAAA,gBAC1B5D,OAAA;YAAM0D,SAAS,EAAE5D,MAAM,CAACkE,QAAS;YAAAJ,QAAA,EAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3CpE,OAAA;YAAM0D,SAAS,EAAE5D,MAAM,CAACuE,QAAS;YAAAT,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNpE,OAAA;UAAA4D,QAAA,EAAI;QAAyB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClCpE,OAAA;UAAA4D,QAAA,EAAG;QAAgD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpE,OAAA;MAAK0D,SAAS,EAAE5D,MAAM,CAACwE,WAAY;MAAAV,QAAA,gBAEjC5D,OAAA;QAAK0D,SAAS,EAAE5D,MAAM,CAACyE,aAAc;QAAAX,QAAA,gBACnC5D,OAAA;UACE0D,SAAS,EAAE,GAAG5D,MAAM,CAAC0E,SAAS,IAAInE,SAAS,KAAK,SAAS,GAAGP,MAAM,CAAC2E,SAAS,GAAG,EAAE,EAAG;UACpFC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,SAAS,CAAE;UAAAoB,QAAA,EAC3C;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpE,OAAA;UACE0D,SAAS,EAAE,GAAG5D,MAAM,CAAC0E,SAAS,IAAInE,SAAS,KAAK,UAAU,GAAGP,MAAM,CAAC2E,SAAS,GAAG,EAAE,EAAG;UACrFC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,UAAU,CAAE;UAAAoB,QAAA,EAC5C;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNpE,OAAA;QAAK0D,SAAS,EAAE5D,MAAM,CAAC6E,aAAc;QAAAf,QAAA,gBACnC5D,OAAA;UAAM4E,QAAQ,EAAE7B,YAAa;UAACW,SAAS,EAAE5D,MAAM,CAAC+E,IAAK;UAAAjB,QAAA,gBAEnD5D,OAAA;YAAK0D,SAAS,EAAE5D,MAAM,CAACgF,OAAQ;YAAAlB,QAAA,gBAC7B5D,OAAA;cAAA4D,QAAA,EAAI;YAAiB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE1BpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACiF,OAAQ;cAAAnB,QAAA,gBAC7B5D,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,MAAM;kBAAArB,QAAA,GAClBvD,SAAS,KAAK,UAAU,GAAG,qBAAqB,GAAG,WAAW,EAAC,IAClE;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRpE,OAAA;kBACEqC,IAAI,EAAC,MAAM;kBACX6C,EAAE,EAAC,MAAM;kBACTzE,IAAI,EAAC,MAAM;kBACX2B,KAAK,EAAE7B,QAAQ,CAACE,IAAK;kBACrB0E,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAE/E,SAAS,KAAK,UAAU,GAAG,2BAA2B,GAAG,sBAAuB;kBAC7FqD,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAACtB,IAAI,iBAAIT,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAACtB;gBAAI;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eAENpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,OAAO;kBAAArB,QAAA,EAAC;gBAAe;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CpE,OAAA;kBACEqC,IAAI,EAAC,OAAO;kBACZ6C,EAAE,EAAC,OAAO;kBACVzE,IAAI,EAAC,OAAO;kBACZ2B,KAAK,EAAE7B,QAAQ,CAACG,KAAM;kBACtByE,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,qBAAqB;kBACjC1B,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAACrB,KAAK,iBAAIV,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAACrB;gBAAK;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACiF,OAAQ;cAAAnB,QAAA,gBAC7B5D,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,OAAO;kBAAArB,QAAA,EAAC;gBAAc;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7CpE,OAAA;kBACEqC,IAAI,EAAC,KAAK;kBACV6C,EAAE,EAAC,OAAO;kBACVzE,IAAI,EAAC,OAAO;kBACZ2B,KAAK,EAAE7B,QAAQ,CAACM,KAAM;kBACtBsE,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,iBAAiB;kBAC7B1B,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAAClB,KAAK,iBAAIb,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAAClB;gBAAK;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,EAEL/D,SAAS,KAAK,SAAS,iBACtBL,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,QAAQ;kBAAArB,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtCpE,OAAA;kBACEkF,EAAE,EAAC,QAAQ;kBACXzE,IAAI,EAAC,QAAQ;kBACb2B,KAAK,EAAE7B,QAAQ,CAACS,MAAO;kBACvBmE,QAAQ,EAAEjD,YAAa;kBACvBwB,SAAS,EAAC,OAAO;kBAAAE,QAAA,gBAEjB5D,OAAA;oBAAQoC,KAAK,EAAC,EAAE;oBAAAwB,QAAA,EAAC;kBAAa;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvCpE,OAAA;oBAAQoC,KAAK,EAAC,MAAM;oBAAAwB,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCpE,OAAA;oBAAQoC,KAAK,EAAC,QAAQ;oBAAAwB,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCpE,OAAA;oBAAQoC,KAAK,EAAC,OAAO;oBAAAwB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEL/D,SAAS,KAAK,SAAS,iBACtBL,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACiF,OAAQ;cAAAnB,QAAA,eAC7B5D,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,aAAa;kBAAArB,QAAA,EAAC;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDpE,OAAA;kBACEqC,IAAI,EAAC,MAAM;kBACX6C,EAAE,EAAC,aAAa;kBAChBzE,IAAI,EAAC,aAAa;kBAClB2B,KAAK,EAAE7B,QAAQ,CAACQ,WAAY;kBAC5BoE,QAAQ,EAAEjD,YAAa;kBACvBwB,SAAS,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL/D,SAAS,KAAK,UAAU,iBACvBL,OAAA;YAAK0D,SAAS,EAAE5D,MAAM,CAACgF,OAAQ;YAAAlB,QAAA,gBAC7B5D,OAAA;cAAA4D,QAAA,EAAI;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE7BpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACiF,OAAQ;cAAAnB,QAAA,gBAC7B5D,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,cAAc;kBAAArB,QAAA,EAAC;gBAAe;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrDpE,OAAA;kBACEqC,IAAI,EAAC,MAAM;kBACX6C,EAAE,EAAC,cAAc;kBACjBzE,IAAI,EAAC,cAAc;kBACnB2B,KAAK,EAAE7B,QAAQ,CAACU,YAAa;kBAC7BkE,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,qBAAqB;kBACjC1B,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAACd,YAAY,iBAAIjB,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAACd;gBAAY;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eAENpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,eAAe;kBAAArB,QAAA,EAAC;gBAAgB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDpE,OAAA;kBACEqC,IAAI,EAAC,MAAM;kBACX6C,EAAE,EAAC,eAAe;kBAClBzE,IAAI,EAAC,eAAe;kBACpB2B,KAAK,EAAE7B,QAAQ,CAACe,aAAc;kBAC9B6D,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,sBAAsB;kBAClC1B,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAACT,aAAa,iBAAItB,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAACT;gBAAa;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;cAAApB,QAAA,gBAC/B5D,OAAA;gBAAOiF,OAAO,EAAC,SAAS;gBAAArB,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDpE,OAAA;gBACEkF,EAAE,EAAC,SAAS;gBACZzE,IAAI,EAAC,SAAS;gBACd2B,KAAK,EAAE7B,QAAQ,CAACW,OAAQ;gBACxBiE,QAAQ,EAAEjD,YAAa;gBACvBkD,WAAW,EAAC,iCAAiC;gBAC7C1B,SAAS,EAAC,OAAO;gBACjB6B,IAAI,EAAC,GAAG;gBACRF,QAAQ;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDrC,MAAM,CAACb,OAAO,iBAAIlB,OAAA;gBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;gBAAA1B,QAAA,EAAE7B,MAAM,CAACb;cAAO;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAENpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACiF,OAAQ;cAAAnB,QAAA,gBAC7B5D,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,MAAM;kBAAArB,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpCpE,OAAA;kBACEqC,IAAI,EAAC,MAAM;kBACX6C,EAAE,EAAC,MAAM;kBACTzE,IAAI,EAAC,MAAM;kBACX2B,KAAK,EAAE7B,QAAQ,CAACY,IAAK;kBACrBgE,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,YAAY;kBACxB1B,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAACZ,IAAI,iBAAInB,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAACZ;gBAAI;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eAENpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,OAAO;kBAAArB,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpCpE,OAAA;kBACEkF,EAAE,EAAC,OAAO;kBACVzE,IAAI,EAAC,OAAO;kBACZ2B,KAAK,EAAE7B,QAAQ,CAACa,KAAM;kBACtB+D,QAAQ,EAAEjD,YAAa;kBACvBwB,SAAS,EAAC,OAAO;kBAAAE,QAAA,gBAEjB5D,OAAA;oBAAQoC,KAAK,EAAC,EAAE;oBAAAwB,QAAA,EAAC;kBAAY;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCpE,OAAA;oBAAQoC,KAAK,EAAC,OAAO;oBAAAwB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCpE,OAAA;oBAAQoC,KAAK,EAAC,aAAa;oBAAAwB,QAAA,EAAC;kBAAW;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChDpE,OAAA;oBAAQoC,KAAK,EAAC,WAAW;oBAAAwB,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CpE,OAAA;oBAAQoC,KAAK,EAAC,YAAY;oBAAAwB,QAAA,EAAC;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CpE,OAAA;oBAAQoC,KAAK,EAAC,SAAS;oBAAAwB,QAAA,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCpE,OAAA;oBAAQoC,KAAK,EAAC,WAAW;oBAAAwB,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CpE,OAAA;oBAAQoC,KAAK,EAAC,eAAe;oBAAAwB,QAAA,EAAC;kBAAa;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpDpE,OAAA;oBAAQoC,KAAK,EAAC,aAAa;oBAAAwB,QAAA,EAAC;kBAAW;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,SAAS;kBAAArB,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxCpE,OAAA;kBACEqC,IAAI,EAAC,MAAM;kBACX6C,EAAE,EAAC,SAAS;kBACZzE,IAAI,EAAC,SAAS;kBACd2B,KAAK,EAAE7B,QAAQ,CAACc,OAAQ;kBACxB8D,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,eAAe;kBAC3B1B,SAAS,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACiF,OAAQ;cAAAnB,QAAA,gBAC7B5D,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,cAAc;kBAAArB,QAAA,EAAC;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDpE,OAAA;kBACEkF,EAAE,EAAC,cAAc;kBACjBzE,IAAI,EAAC,cAAc;kBACnB2B,KAAK,EAAE7B,QAAQ,CAACiB,YAAa;kBAC7B2D,QAAQ,EAAEjD,YAAa;kBACvBwB,SAAS,EAAC,OAAO;kBAAAE,QAAA,gBAEjB5D,OAAA;oBAAQoC,KAAK,EAAC,EAAE;oBAAAwB,QAAA,EAAC;kBAAW;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCpE,OAAA;oBAAQoC,KAAK,EAAC,YAAY;oBAAAwB,QAAA,EAAC;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CpE,OAAA;oBAAQoC,KAAK,EAAC,SAAS;oBAAAwB,QAAA,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCpE,OAAA;oBAAQoC,KAAK,EAAC,OAAO;oBAAAwB,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCpE,OAAA;oBAAQoC,KAAK,EAAC,WAAW;oBAAAwB,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,WAAW;kBAAArB,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7CpE,OAAA;kBACEqC,IAAI,EAAC,QAAQ;kBACb6C,EAAE,EAAC,WAAW;kBACdzE,IAAI,EAAC,WAAW;kBAChB2B,KAAK,EAAE7B,QAAQ,CAACkB,SAAU;kBAC1B0D,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,kBAAkB;kBAC9B1B,SAAS,EAAC,OAAO;kBACjB8B,GAAG,EAAC;gBAAG;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,iBAAiB;kBAAArB,QAAA,EAAC;gBAAgB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDpE,OAAA;kBACEqC,IAAI,EAAC,QAAQ;kBACb6C,EAAE,EAAC,iBAAiB;kBACpBzE,IAAI,EAAC,iBAAiB;kBACtB2B,KAAK,EAAE7B,QAAQ,CAACgB,eAAgB;kBAChC4D,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,YAAY;kBACxB1B,SAAS,EAAC,OAAO;kBACjB8B,GAAG,EAAC,MAAM;kBACVC,GAAG,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;cAAApB,QAAA,gBAC/B5D,OAAA;gBAAOiF,OAAO,EAAC,aAAa;gBAAArB,QAAA,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDpE,OAAA;gBACEkF,EAAE,EAAC,aAAa;gBAChBzE,IAAI,EAAC,aAAa;gBAClB2B,KAAK,EAAE7B,QAAQ,CAACqB,WAAY;gBAC5BuD,QAAQ,EAAEjD,YAAa;gBACvBkD,WAAW,EAAC,+DAA+D;gBAC3E1B,SAAS,EAAC,OAAO;gBACjB6B,IAAI,EAAC;cAAG;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAAC8F,aAAc;cAAAhC,QAAA,gBACnC5D,OAAA;gBAAA4D,QAAA,EAAI;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAAC+F,WAAY;gBAAAjC,QAAA,gBACjC5D,OAAA;kBAAO0D,SAAS,EAAE5D,MAAM,CAACgG,aAAc;kBAAAlC,QAAA,gBACrC5D,OAAA;oBACEqC,IAAI,EAAC,UAAU;oBACf5B,IAAI,EAAC,mBAAmB;oBACxB6B,OAAO,EAAE/B,QAAQ,CAACmB,iBAAkB;oBACpCyD,QAAQ,EAAEjD,YAAa;oBACvBwB,SAAS,EAAE5D,MAAM,CAACiG;kBAAS;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,wCAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAERpE,OAAA;kBAAO0D,SAAS,EAAE5D,MAAM,CAACgG,aAAc;kBAAAlC,QAAA,gBACrC5D,OAAA;oBACEqC,IAAI,EAAC,UAAU;oBACf5B,IAAI,EAAC,kBAAkB;oBACvB6B,OAAO,EAAE/B,QAAQ,CAACoB,gBAAiB;oBACnCwD,QAAQ,EAAEjD,YAAa;oBACvBwB,SAAS,EAAE5D,MAAM,CAACiG;kBAAS;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,kCAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDpE,OAAA;YAAK0D,SAAS,EAAE5D,MAAM,CAACgF,OAAQ;YAAAlB,QAAA,gBAC7B5D,OAAA;cAAA4D,QAAA,EAAI;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEjBpE,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACiF,OAAQ;cAAAnB,QAAA,gBAC7B5D,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5CpE,OAAA;kBACEqC,IAAI,EAAC,UAAU;kBACf6C,EAAE,EAAC,UAAU;kBACbzE,IAAI,EAAC,UAAU;kBACf2B,KAAK,EAAE7B,QAAQ,CAACI,QAAS;kBACzBwE,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,mCAAmC;kBAC/C1B,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAACpB,QAAQ,iBAAIX,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAACpB;gBAAQ;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eAENpE,OAAA;gBAAK0D,SAAS,EAAE5D,MAAM,CAACkF,SAAU;gBAAApB,QAAA,gBAC/B5D,OAAA;kBAAOiF,OAAO,EAAC,iBAAiB;kBAAArB,QAAA,EAAC;gBAAkB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3DpE,OAAA;kBACEqC,IAAI,EAAC,UAAU;kBACf6C,EAAE,EAAC,iBAAiB;kBACpBzE,IAAI,EAAC,iBAAiB;kBACtB2B,KAAK,EAAE7B,QAAQ,CAACK,eAAgB;kBAChCuE,QAAQ,EAAEjD,YAAa;kBACvBkD,WAAW,EAAC,uBAAuB;kBACnC1B,SAAS,EAAC,OAAO;kBACjB2B,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACDrC,MAAM,CAACnB,eAAe,iBAAIZ,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACwF,KAAM;kBAAA1B,QAAA,EAAE7B,MAAM,CAACnB;gBAAe;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpE,OAAA;YAAK0D,SAAS,EAAE5D,MAAM,CAACgF,OAAQ;YAAAlB,QAAA,gBAC7B5D,OAAA;cAAK0D,SAAS,EAAE5D,MAAM,CAACkG,YAAa;cAAApC,QAAA,eAClC5D,OAAA;gBAAO0D,SAAS,EAAE5D,MAAM,CAACgG,aAAc;gBAAAlC,QAAA,gBACrC5D,OAAA;kBACEqC,IAAI,EAAC,UAAU;kBACf5B,IAAI,EAAC,OAAO;kBACZ4E,QAAQ;kBACR3B,SAAS,EAAE5D,MAAM,CAACiG;gBAAS;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,kBACY,EAAC,GAAG,eAClBpE,OAAA,CAACJ,IAAI;kBAACqG,EAAE,EAAC,GAAG;kBAACvC,SAAS,EAAE5D,MAAM,CAACoG,IAAK;kBAAAtC,QAAA,EAAC;gBAAoB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KAClE,EAAC,GAAG,eACPpE,OAAA,CAACJ,IAAI;kBAACqG,EAAE,EAAC,GAAG;kBAACvC,SAAS,EAAE5D,MAAM,CAACoG,IAAK;kBAAAtC,QAAA,EAAC;gBAAc;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpE,OAAA;cACEqC,IAAI,EAAC,QAAQ;cACb8D,QAAQ,EAAEtE,SAAU;cACpB6B,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAE7B/B,SAAS,gBACR7B,OAAA,CAAAE,SAAA;gBAAA0D,QAAA,gBACE5D,OAAA;kBAAM0D,SAAS,EAAE5D,MAAM,CAACsG;gBAAQ;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvC/D,SAAS,KAAK,UAAU,GAAG,yBAAyB,GAAG,qBAAqB;cAAA,eAC7E,CAAC,GAEHA,SAAS,KAAK,UAAU,GAAG,sBAAsB,GAAG;YACrD;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPpE,OAAA;UAAK0D,SAAS,EAAE5D,MAAM,CAACuG,SAAU;UAAAzC,QAAA,eAC/B5D,OAAA;YAAA4D,QAAA,GAAG,0BACuB,EAAC,GAAG,eAC5B5D,OAAA,CAACJ,IAAI;cAACqG,EAAE,EAAC,QAAQ;cAACvC,SAAS,EAAE5D,MAAM,CAACoG,IAAK;cAAAtC,QAAA,EAAC;YAE1C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CA5hBID,QAAQ;EAAA,QA+BKN,WAAW;AAAA;AAAAyG,EAAA,GA/BxBnG,QAAQ;AA8hBd,eAAeA,QAAQ;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}