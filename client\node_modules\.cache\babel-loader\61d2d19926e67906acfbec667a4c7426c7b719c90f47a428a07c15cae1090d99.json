{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8\",\n  key: \"15492f\"\n}], [\"path\", {\n  d: \"m16 16 6-6\",\n  key: \"vzrcl6\"\n}], [\"path\", {\n  d: \"m8 8 6-6\",\n  key: \"18bi4p\"\n}], [\"path\", {\n  d: \"m9 7 8 8\",\n  key: \"5jnvq1\"\n}], [\"path\", {\n  d: \"m21 11-8-8\",\n  key: \"z4y7zo\"\n}]];\nconst Gavel = createLucideIcon(\"gavel\", __iconNode);\nexport { __iconNode, Gavel as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Gavel", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\gavel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm14.5 12.5-8 8a2.119 2.119 0 1 1-3-3l8-8', key: '15492f' }],\n  ['path', { d: 'm16 16 6-6', key: 'vzrcl6' }],\n  ['path', { d: 'm8 8 6-6', key: '18bi4p' }],\n  ['path', { d: 'm9 7 8 8', key: '5jnvq1' }],\n  ['path', { d: 'm21 11-8-8', key: 'z4y7zo' }],\n];\n\n/**\n * @component @name Gavel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTQuNSAxMi41LTggOGEyLjExOSAyLjExOSAwIDEgMS0zLTNsOC04IiAvPgogIDxwYXRoIGQ9Im0xNiAxNiA2LTYiIC8+CiAgPHBhdGggZD0ibTggOCA2LTYiIC8+CiAgPHBhdGggZD0ibTkgNyA4IDgiIC8+CiAgPHBhdGggZD0ibTIxIDExLTgtOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/gavel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Gavel = createLucideIcon('gavel', __iconNode);\n\nexport default Gavel;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7C;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}