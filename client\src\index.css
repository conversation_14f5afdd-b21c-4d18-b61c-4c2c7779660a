@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Hope Medics Color Scheme */
  --primary-color: #00796B;      /* Teal - health & trust */
  --primary-dark: #00695C;       /* Darker teal for hover */
  --accent-color: #FF9800;       /* Amber - action items */
  --accent-dark: #F57C00;        /* Darker amber for hover */
  --white: #ffffff;
  --light-grey: #f9fafb;
  --dark-grey: #333333;
  --border-color: #e5e7eb;
  --shadow-light: 0 1px 3px rgba(0,0,0,0.1);
  --shadow-medium: 0 4px 6px rgba(0,0,0,0.1);
  --shadow-heavy: 0 10px 25px rgba(0,0,0,0.15);
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --transition: all 0.2s ease-in-out;
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--dark-grey);
  background-color: var(--light-grey);
}

/* Global Button Styles */
.btn-primary {
  background: var(--primary-color);
  color: var(--white);
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-accent {
  background: var(--accent-color);
  color: var(--white);
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-accent:hover {
  background: var(--accent-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: var(--white);
  color: var(--primary-color);
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

/* Global Input Styles */
.input {
  border: 1px solid var(--border-color);
  padding: 0.75rem;
  border-radius: var(--border-radius);
  width: 100%;
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
}

.input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 121, 107, 0.1);
}

/* Global Card Styles */
.card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-heavy);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Responsive Grid */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }

@media (max-width: 768px) {
  .grid-cols-2, .grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) {
  .grid-cols-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-cols-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-between > * + * { margin-left: 1rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--border-radius); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: 50%; }

.shadow { box-shadow: var(--shadow-light); }
.shadow-md { box-shadow: var(--shadow-medium); }
.shadow-lg { box-shadow: var(--shadow-heavy); }
