{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\ReceptionDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styles from './ReceptionDashboard.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReceptionDashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('appointments');\n  const stats = [{\n    title: \"Today's Total\",\n    value: '24',\n    icon: '📅',\n    change: '+3 from yesterday'\n  }, {\n    title: 'Checked In',\n    value: '18',\n    icon: '✅',\n    change: '75% completion rate'\n  }, {\n    title: 'Currently Waiting',\n    value: '6',\n    icon: '⏳',\n    change: 'Avg wait: 15 min'\n  }, {\n    title: 'Completed',\n    value: '12',\n    icon: '👥',\n    change: '50% of total'\n  }];\n  const appointments = [{\n    id: 1,\n    time: '09:00',\n    patient: '<PERSON>',\n    doctor: 'Dr. <PERSON>',\n    status: 'checked-in'\n  }, {\n    id: 2,\n    time: '09:30',\n    patient: '<PERSON>',\n    doctor: 'Dr. <PERSON>',\n    status: 'waiting'\n  }, {\n    id: 3,\n    time: '10:00',\n    patient: '<PERSON> <PERSON>',\n    doctor: 'Dr. <PERSON>',\n    status: 'in-progress'\n  }, {\n    id: 4,\n    time: '10:30',\n    patient: '<PERSON> <PERSON>',\n    doctor: 'Dr. <PERSON> <PERSON>',\n    status: 'completed'\n  }];\n  const walkInQueue = [{\n    id: 5,\n    patient: 'David Lee',\n    reason: 'Emergency consultation',\n    priority: 'high',\n    waitTime: '15 min'\n  }, {\n    id: 6,\n    patient: 'Lisa Wang',\n    reason: 'General checkup',\n    priority: 'normal',\n    waitTime: '25 min'\n  }];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'checked-in':\n        return 'bg-blue-100 text-blue-800';\n      case 'waiting':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'in-progress':\n        return 'bg-green-100 text-green-800';\n      case 'completed':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'bg-red-100 text-red-800';\n      case 'normal':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold\",\n          children: \"Reception Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted-foreground\",\n          children: \"Manage appointments and walk-in patients\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), \"Add Walk-in Patient\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), \"Print Schedule\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: stats.map((stat, index) => {\n        const IconComponent = stat.icon;\n        return /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-muted-foreground\",\n                  children: stat.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: stat.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconComponent, {\n                className: `h-8 w-8 ${stat.color}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center justify-between\",\n              children: [\"Today's Appointments\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-muted-foreground\",\n                children: new Date().toLocaleDateString('en-US', {\n                  weekday: 'long',\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: appointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-lg\",\n                        children: appointment.time\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 108,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: appointment.patient\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 109,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        className: getStatusColor(appointment.status),\n                        children: appointment.status.replace('-', ' ').toUpperCase()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 110,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-muted-foreground\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\uD83D\\uDC69\\u200D\\u2695\\uFE0F \", appointment.doctor]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 115,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 114,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [appointment.status === 'waiting' && /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      children: \"Check In\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 27\n                    }, this), appointment.status === 'checked-in' && /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline\",\n                      children: [/*#__PURE__*/_jsxDEV(Phone, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 124,\n                        columnNumber: 29\n                      }, this), \"Call Patient\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this)\n              }, appointment.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center justify-between\",\n              children: [\"Walk-in Queue\", /*#__PURE__*/_jsxDEV(Badge, {\n                variant: \"outline\",\n                children: [walkInQueue.length, \" waiting\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: walkInQueue.map(patient => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border rounded-lg p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: patient.patient\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    className: getPriorityColor(patient.priority),\n                    children: patient.priority.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-muted-foreground space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\uD83D\\uDCDD \", patient.reason]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\u23F1\\uFE0F Waiting: \", patient.waitTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2 mt-3\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    className: \"flex-1\",\n                    children: \"Assign Doctor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this)]\n              }, patient.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), walkInQueue.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-muted-foreground py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-2\",\n                children: \"\\uD83D\\uDC65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No walk-in patients waiting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        children: /*#__PURE__*/_jsxDEV(CardTitle, {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(Phone, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Call Next Patient\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"View All Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Room Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Daily Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(ReceptionDashboard, \"tzf+8hdF0NkKuo0cOQ0Y/MooWiM=\");\n_c = ReceptionDashboard;\nexport default ReceptionDashboard;\nvar _c;\n$RefreshReg$(_c, \"ReceptionDashboard\");", "map": {"version": 3, "names": ["React", "useState", "styles", "jsxDEV", "_jsxDEV", "ReceptionDashboard", "_s", "activeTab", "setActiveTab", "stats", "title", "value", "icon", "change", "appointments", "id", "time", "patient", "doctor", "status", "walkInQueue", "reason", "priority", "waitTime", "getStatusColor", "getPriorityColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "Plus", "variant", "Calendar", "map", "stat", "index", "IconComponent", "Card", "<PERSON><PERSON><PERSON><PERSON>", "color", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "appointment", "Badge", "replace", "toUpperCase", "size", "Phone", "length", "UserPlus", "CheckCircle", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/ReceptionDashboard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styles from './ReceptionDashboard.module.css';\n\nconst ReceptionDashboard = () => {\n  const [activeTab, setActiveTab] = useState('appointments');\n\n  const stats = [\n    { title: \"Today's Total\", value: '24', icon: '📅', change: '+3 from yesterday' },\n    { title: 'Checked In', value: '18', icon: '✅', change: '75% completion rate' },\n    { title: 'Currently Waiting', value: '6', icon: '⏳', change: 'Avg wait: 15 min' },\n    { title: 'Completed', value: '12', icon: '👥', change: '50% of total' },\n  ];\n\n  const appointments = [\n    { id: 1, time: '09:00', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'checked-in' },\n    { id: 2, time: '09:30', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'waiting' },\n    { id: 3, time: '10:00', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'in-progress' },\n    { id: 4, time: '10:30', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'completed' },\n  ];\n\n  const walkInQueue = [\n    { id: 5, patient: '<PERSON>', reason: 'Emergency consultation', priority: 'high', waitTime: '15 min' },\n    { id: 6, patient: 'Lisa Wang', reason: 'General checkup', priority: 'normal', waitTime: '25 min' },\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'checked-in': return 'bg-blue-100 text-blue-800';\n      case 'waiting': return 'bg-yellow-100 text-yellow-800';\n      case 'in-progress': return 'bg-green-100 text-green-800';\n      case 'completed': return 'bg-gray-100 text-gray-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high': return 'bg-red-100 text-red-800';\n      case 'normal': return 'bg-green-100 text-green-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold\">Reception Dashboard</h1>\n          <p className=\"text-muted-foreground\">Manage appointments and walk-in patients</p>\n        </div>\n        <div className=\"flex space-x-4\">\n          <Button>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Add Walk-in Patient\n          </Button>\n          <Button variant=\"outline\">\n            <Calendar className=\"mr-2 h-4 w-4\" />\n            Print Schedule\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => {\n          const IconComponent = stat.icon;\n          return (\n            <Card key={index}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">{stat.title}</p>\n                    <p className=\"text-2xl font-bold\">{stat.value}</p>\n                  </div>\n                  <IconComponent className={`h-8 w-8 ${stat.color}`} />\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n\n      {/* Main Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Today's Appointments */}\n        <div className=\"lg:col-span-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                Today's Appointments\n                <span className=\"text-sm text-muted-foreground\">\n                  {new Date().toLocaleDateString('en-US', { \n                    weekday: 'long', \n                    year: 'numeric', \n                    month: 'long', \n                    day: 'numeric' \n                  })}\n                </span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {appointments.map((appointment) => (\n                  <div key={appointment.id} className=\"border rounded-lg p-4 hover:shadow-md transition-shadow\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-2\">\n                          <span className=\"font-semibold text-lg\">{appointment.time}</span>\n                          <span className=\"font-medium\">{appointment.patient}</span>\n                          <Badge className={getStatusColor(appointment.status)}>\n                            {appointment.status.replace('-', ' ').toUpperCase()}\n                          </Badge>\n                        </div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          <div>👩‍⚕️ {appointment.doctor}</div>\n                        </div>\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        {appointment.status === 'waiting' && (\n                          <Button size=\"sm\">Check In</Button>\n                        )}\n                        {appointment.status === 'checked-in' && (\n                          <Button size=\"sm\" variant=\"outline\">\n                            <Phone className=\"h-4 w-4 mr-1\" />\n                            Call Patient\n                          </Button>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Walk-in Queue */}\n        <div>\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                Walk-in Queue\n                <Badge variant=\"outline\">{walkInQueue.length} waiting</Badge>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {walkInQueue.map((patient) => (\n                  <div key={patient.id} className=\"border rounded-lg p-3\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <span className=\"font-medium\">{patient.patient}</span>\n                      <Badge className={getPriorityColor(patient.priority)}>\n                        {patient.priority.toUpperCase()}\n                      </Badge>\n                    </div>\n                    <div className=\"text-sm text-muted-foreground space-y-1\">\n                      <div>📝 {patient.reason}</div>\n                      <div>⏱️ Waiting: {patient.waitTime}</div>\n                    </div>\n                    <div className=\"flex space-x-2 mt-3\">\n                      <Button size=\"sm\" className=\"flex-1\">\n                        Assign Doctor\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {walkInQueue.length === 0 && (\n                <div className=\"text-center text-muted-foreground py-8\">\n                  <div className=\"text-4xl mb-2\">👥</div>\n                  <p>No walk-in patients waiting</p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Quick Actions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <Phone className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Call Next Patient</span>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <Calendar className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">View All Appointments</span>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <UserPlus className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Room Management</span>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <CheckCircle className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Daily Report</span>\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default ReceptionDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,cAAc,CAAC;EAE1D,MAAMQ,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAoB,CAAC,EAChF;IAAEH,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAsB,CAAC,EAC9E;IAAEH,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE,GAAG;IAAEC,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAmB,CAAC,EACjF;IAAEH,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAe,CAAC,CACxE;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,UAAU;IAAEC,MAAM,EAAE,mBAAmB;IAAEC,MAAM,EAAE;EAAa,CAAC,EAChG;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,YAAY;IAAEC,MAAM,EAAE,kBAAkB;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC9F;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,aAAa;IAAEC,MAAM,EAAE,mBAAmB;IAAEC,MAAM,EAAE;EAAc,CAAC,EACpG;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,aAAa;IAAEC,MAAM,EAAE,qBAAqB;IAAEC,MAAM,EAAE;EAAY,CAAC,CACrG;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEL,EAAE,EAAE,CAAC;IAAEE,OAAO,EAAE,WAAW;IAAEI,MAAM,EAAE,wBAAwB;IAAEC,QAAQ,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAS,CAAC,EACvG;IAAER,EAAE,EAAE,CAAC;IAAEE,OAAO,EAAE,WAAW;IAAEI,MAAM,EAAE,iBAAiB;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAS,CAAC,CACnG;EAED,MAAMC,cAAc,GAAIL,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,YAAY;QAAE,OAAO,2BAA2B;MACrD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,aAAa;QAAE,OAAO,6BAA6B;MACxD,KAAK,WAAW;QAAE,OAAO,2BAA2B;MACpD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAIH,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,yBAAyB;MAC7C,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,oBACElB,OAAA;IAAKsB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBvB,OAAA;MAAKsB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDvB,OAAA;QAAAuB,QAAA,gBACEvB,OAAA;UAAIsB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3D3B,OAAA;UAAGsB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eACN3B,OAAA;QAAKsB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvB,OAAA,CAAC4B,MAAM;UAAAL,QAAA,gBACLvB,OAAA,CAAC6B,IAAI;YAACP,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3B,OAAA,CAAC4B,MAAM;UAACE,OAAO,EAAC,SAAS;UAAAP,QAAA,gBACvBvB,OAAA,CAAC+B,QAAQ;YAACT,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClElB,KAAK,CAAC2B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC1B,MAAMC,aAAa,GAAGF,IAAI,CAACzB,IAAI;QAC/B,oBACER,OAAA,CAACoC,IAAI;UAAAb,QAAA,eACHvB,OAAA,CAACqC,WAAW;YAACf,SAAS,EAAC,KAAK;YAAAC,QAAA,eAC1BvB,OAAA;cAAKsB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDvB,OAAA;gBAAAuB,QAAA,gBACEvB,OAAA;kBAAGsB,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEU,IAAI,CAAC3B;gBAAK;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzE3B,OAAA;kBAAGsB,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEU,IAAI,CAAC1B;gBAAK;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACN3B,OAAA,CAACmC,aAAa;gBAACb,SAAS,EAAE,WAAWW,IAAI,CAACK,KAAK;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GATLO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDvB,OAAA;QAAKsB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BvB,OAAA,CAACoC,IAAI;UAAAb,QAAA,gBACHvB,OAAA,CAACuC,UAAU;YAAAhB,QAAA,eACTvB,OAAA,CAACwC,SAAS;cAAClB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,sBAEvD,eAAAvB,OAAA;gBAAMsB,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC5C,IAAIkB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;kBACtCC,OAAO,EAAE,MAAM;kBACfC,IAAI,EAAE,SAAS;kBACfC,KAAK,EAAE,MAAM;kBACbC,GAAG,EAAE;gBACP,CAAC;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACb3B,OAAA,CAACqC,WAAW;YAAAd,QAAA,eACVvB,OAAA;cAAKsB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBb,YAAY,CAACsB,GAAG,CAAEe,WAAW,iBAC5B/C,OAAA;gBAA0BsB,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,eAC3FvB,OAAA;kBAAKsB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CvB,OAAA;oBAAKsB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBvB,OAAA;sBAAKsB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/CvB,OAAA;wBAAMsB,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEwB,WAAW,CAACnC;sBAAI;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjE3B,OAAA;wBAAMsB,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAEwB,WAAW,CAAClC;sBAAO;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC1D3B,OAAA,CAACgD,KAAK;wBAAC1B,SAAS,EAAEF,cAAc,CAAC2B,WAAW,CAAChC,MAAM,CAAE;wBAAAQ,QAAA,EAClDwB,WAAW,CAAChC,MAAM,CAACkC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;sBAAC;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACN3B,OAAA;sBAAKsB,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,eAC5CvB,OAAA;wBAAAuB,QAAA,GAAK,iCAAM,EAACwB,WAAW,CAACjC,MAAM;sBAAA;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3B,OAAA;oBAAKsB,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAC5BwB,WAAW,CAAChC,MAAM,KAAK,SAAS,iBAC/Bf,OAAA,CAAC4B,MAAM;sBAACuB,IAAI,EAAC,IAAI;sBAAA5B,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACnC,EACAoB,WAAW,CAAChC,MAAM,KAAK,YAAY,iBAClCf,OAAA,CAAC4B,MAAM;sBAACuB,IAAI,EAAC,IAAI;sBAACrB,OAAO,EAAC,SAAS;sBAAAP,QAAA,gBACjCvB,OAAA,CAACoD,KAAK;wBAAC9B,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAzBEoB,WAAW,CAACpC,EAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3B,OAAA;QAAAuB,QAAA,eACEvB,OAAA,CAACoC,IAAI;UAAAb,QAAA,gBACHvB,OAAA,CAACuC,UAAU;YAAAhB,QAAA,eACTvB,OAAA,CAACwC,SAAS;cAAClB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,eAEvD,eAAAvB,OAAA,CAACgD,KAAK;gBAAClB,OAAO,EAAC,SAAS;gBAAAP,QAAA,GAAEP,WAAW,CAACqC,MAAM,EAAC,UAAQ;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACb3B,OAAA,CAACqC,WAAW;YAAAd,QAAA,gBACVvB,OAAA;cAAKsB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBP,WAAW,CAACgB,GAAG,CAAEnB,OAAO,iBACvBb,OAAA;gBAAsBsB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACrDvB,OAAA;kBAAKsB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDvB,OAAA;oBAAMsB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEV,OAAO,CAACA;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtD3B,OAAA,CAACgD,KAAK;oBAAC1B,SAAS,EAAED,gBAAgB,CAACR,OAAO,CAACK,QAAQ,CAAE;oBAAAK,QAAA,EAClDV,OAAO,CAACK,QAAQ,CAACgC,WAAW,CAAC;kBAAC;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN3B,OAAA;kBAAKsB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDvB,OAAA;oBAAAuB,QAAA,GAAK,eAAG,EAACV,OAAO,CAACI,MAAM;kBAAA;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9B3B,OAAA;oBAAAuB,QAAA,GAAK,wBAAY,EAACV,OAAO,CAACM,QAAQ;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACN3B,OAAA;kBAAKsB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAClCvB,OAAA,CAAC4B,MAAM;oBAACuB,IAAI,EAAC,IAAI;oBAAC7B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAfEd,OAAO,CAACF,EAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELX,WAAW,CAACqC,MAAM,KAAK,CAAC,iBACvBrD,OAAA;cAAKsB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDvB,OAAA;gBAAKsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvC3B,OAAA;gBAAAuB,QAAA,EAAG;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA,CAACoC,IAAI;MAAAb,QAAA,gBACHvB,OAAA,CAACuC,UAAU;QAAAhB,QAAA,eACTvB,OAAA,CAACwC,SAAS;UAAAjB,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACb3B,OAAA,CAACqC,WAAW;QAAAd,QAAA,eACVvB,OAAA;UAAKsB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDvB,OAAA,CAAC4B,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjDvB,OAAA,CAACoD,KAAK;cAAC9B,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClC3B,OAAA;cAAMsB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACT3B,OAAA,CAAC4B,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjDvB,OAAA,CAAC+B,QAAQ;cAACT,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrC3B,OAAA;cAAMsB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACT3B,OAAA,CAAC4B,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjDvB,OAAA,CAACsD,QAAQ;cAAChC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrC3B,OAAA;cAAMsB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACT3B,OAAA,CAAC4B,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjDvB,OAAA,CAACuD,WAAW;cAACjC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxC3B,OAAA;cAAMsB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzB,EAAA,CA5MID,kBAAkB;AAAAuD,EAAA,GAAlBvD,kBAAkB;AA8MxB,eAAeA,kBAAkB;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}