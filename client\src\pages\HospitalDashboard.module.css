.container {
  min-height: 100vh;
  background: var(--light-grey);
}

/* Header */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  padding: 2rem 0;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hospitalInfo h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 0.5rem 0;
}

.hospitalInfo p {
  color: #666;
  font-size: 1rem;
  margin: 0 0 0.5rem 0;
}

.rating {
  font-size: 0.875rem;
  color: var(--accent-color);
  font-weight: 500;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

/* Main Content */
.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.statIcon {
  font-size: 2.5rem;
  background: rgba(0, 121, 107, 0.1);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statContent h3 {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.statChange {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  margin-bottom: 2rem;
  overflow: hidden;
}

.tabButton {
  flex: 1;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: var(--transition);
  border-right: 1px solid var(--border-color);
}

.tabButton:last-child {
  border-right: none;
}

.tabButton:hover {
  background: rgba(0, 121, 107, 0.05);
  color: var(--primary-color);
}

.tabActive {
  background: var(--primary-color);
  color: var(--white);
}

.tabActive:hover {
  background: var(--primary-dark);
  color: var(--white);
}

/* Tab Content */
.tabContent {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 2rem;
}

.tabContent h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1.5rem 0;
}

/* Form Styles */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 500;
  color: var(--dark-grey);
  font-size: 0.875rem;
}

.checkboxGroup {
  margin-top: 1rem;
}

.checkboxGroup h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dark-grey);
  margin: 0 0 1rem 0;
}

.checkboxRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--dark-grey);
  cursor: pointer;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.checkboxLabel:hover {
  background: var(--light-grey);
  border-color: var(--primary-color);
}

.checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

/* Rooms Section */
.roomsSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.sectionHeader h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.roomsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.roomCard {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  transition: var(--transition);
}

.roomCard:hover {
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.roomHeader {
  margin-bottom: 1rem;
}

.roomTypeInput {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-color);
  background: none;
  border: none;
  border-bottom: 2px solid var(--border-color);
  padding: 0.5rem 0;
  width: 100%;
  transition: var(--transition);
}

.roomTypeInput:focus {
  outline: none;
  border-bottom-color: var(--primary-color);
}

.roomDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.roomField {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.roomField label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dark-grey);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 0 1rem;
  }
  
  .headerActions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .mainContent {
    padding: 1rem;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .tabNavigation {
    flex-direction: column;
  }
  
  .tabButton {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .tabButton:last-child {
    border-bottom: none;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .checkboxRow {
    grid-template-columns: 1fr;
  }
  
  .roomDetails {
    grid-template-columns: 1fr;
  }
  
  .sectionHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
