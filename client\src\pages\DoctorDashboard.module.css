.container {
  min-height: 100vh;
  background: var(--light-grey);
}

/* Header */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  padding: 2rem 0;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.greeting h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 0.5rem 0;
}

.greeting p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

/* Main Content */
.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.statIcon {
  font-size: 2.5rem;
  background: rgba(0, 121, 107, 0.1);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statContent h3 {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.statChange {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

/* Dashboard Grid */
.dashboardGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

/* Appointments Section */
.appointmentsSection {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.sectionHeader h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.appointmentsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.appointmentCard {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  transition: var(--transition);
}

.appointmentCard:hover {
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.appointmentHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.patientInfo h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-grey);
  margin: 0 0 0.25rem 0;
}

.patientInfo p {
  font-size: 0.875rem;
  color: #666;
  margin: 0;
}

.appointmentTime {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
  background: rgba(0, 121, 107, 0.1);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
}

.appointmentDetails {
  margin-top: 1rem;
}

.reason {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 1rem 0;
  font-style: italic;
}

.appointmentFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

.confirmed {
  background: #dcfce7;
  color: #166534;
}

.pending {
  background: #fef3c7;
  color: #92400e;
}

.completed {
  background: #dbeafe;
  color: #1e40af;
}

.appointmentActions {
  display: flex;
  gap: 0.5rem;
}

.appointmentActions button {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

/* Profile Section */
.profileSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profileCard {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 1.5rem;
}

.profileHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.profileAvatar {
  font-size: 3rem;
  background: rgba(0, 121, 107, 0.1);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profileInfo h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 0.25rem 0;
}

.profileInfo p {
  font-size: 0.875rem;
  color: #666;
  margin: 0.125rem 0;
}

.contactInfo {
  margin-bottom: 1.5rem;
}

.contactInfo div {
  font-size: 0.875rem;
  color: #666;
  margin: 0.5rem 0;
}

.availabilityStatus {
  background: var(--light-grey);
  padding: 1rem;
  border-radius: var(--border-radius);
}

.statusHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--dark-grey);
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #10b981;
}

.statusDot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
}

.scheduleInfo div {
  font-size: 0.75rem;
  color: #666;
  margin: 0.25rem 0;
}

.quickActions {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 1.5rem;
}

.quickActions h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1rem 0;
}

.actionButtons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 0 1rem;
  }
  
  .headerActions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .mainContent {
    padding: 1rem;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .dashboardGrid {
    grid-template-columns: 1fr;
  }
  
  .appointmentHeader {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .appointmentFooter {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .appointmentActions {
    width: 100%;
    justify-content: flex-start;
  }
}
