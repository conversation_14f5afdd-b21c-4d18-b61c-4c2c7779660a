.container {
  min-height: 100vh;
  display: flex;
}

/* Left Panel - Hero */
.heroPanel {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.heroPanel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.heroContent {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 500px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.logoIcon {
  font-size: 3rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logoText {
  font-size: 2rem;
  font-weight: 700;
}

.heroTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.heroSubtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.heroFeatures {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
}

.featureIcon {
  font-size: 1.5rem;
}

/* Right Panel - Form */
.formPanel {
  flex: 1;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.formContainer {
  width: 100%;
  max-width: 400px;
}

.formHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.formHeader h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.formHeader p {
  color: #666;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 500;
  color: var(--dark-grey);
  font-size: 0.875rem;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.checkboxLabel {
  font-size: 0.875rem;
  color: var(--dark-grey);
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.formFooter {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.demoCredentials {
  margin-top: 2rem;
  padding: 1rem;
  background: var(--light-grey);
  border-radius: var(--border-radius);
}

.demoCredentials h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.credentialsList {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.4;
}

.credentialsList div {
  margin-bottom: 0.25rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
  
  .heroPanel {
    min-height: 40vh;
    padding: 1.5rem;
  }
  
  .heroTitle {
    font-size: 1.875rem;
  }
  
  .heroSubtitle {
    font-size: 1rem;
  }
  
  .logoIcon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .logoText {
    font-size: 1.5rem;
  }
  
  .formPanel {
    padding: 1.5rem;
  }
  
  .formHeader h2 {
    font-size: 1.5rem;
  }
}
