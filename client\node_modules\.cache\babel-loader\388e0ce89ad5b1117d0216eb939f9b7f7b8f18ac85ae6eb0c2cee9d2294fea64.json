{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"18n3k1\"\n}], [\"path\", {\n  d: \"M6 8h4\",\n  key: \"utf9t1\"\n}], [\"path\", {\n  d: \"M14 8h.01\",\n  key: \"1primd\"\n}], [\"path\", {\n  d: \"M18 8h.01\",\n  key: \"emo2bl\"\n}], [\"path\", {\n  d: \"M2 12h20\",\n  key: \"9i4pu4\"\n}], [\"path\", {\n  d: \"M6 12v4\",\n  key: \"dy92yo\"\n}], [\"path\", {\n  d: \"M10 12v4\",\n  key: \"1fxnav\"\n}], [\"path\", {\n  d: \"M14 12v4\",\n  key: \"1hft58\"\n}], [\"path\", {\n  d: \"M18 12v4\",\n  key: \"tjjnbz\"\n}]];\nconst KeyboardMusic = createLucideIcon(\"keyboard-music\", __iconNode);\nexport { __iconNode, KeyboardMusic as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "KeyboardMusic", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\keyboard-music.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'M6 8h4', key: 'utf9t1' }],\n  ['path', { d: 'M14 8h.01', key: '1primd' }],\n  ['path', { d: 'M18 8h.01', key: 'emo2bl' }],\n  ['path', { d: 'M2 12h20', key: '9i4pu4' }],\n  ['path', { d: 'M6 12v4', key: 'dy92yo' }],\n  ['path', { d: 'M10 12v4', key: '1fxnav' }],\n  ['path', { d: 'M14 12v4', key: '1hft58' }],\n  ['path', { d: 'M18 12v4', key: 'tjjnbz' }],\n];\n\n/**\n * @component @name KeyboardMusic\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik02IDhoNCIgLz4KICA8cGF0aCBkPSJNMTQgOGguMDEiIC8+CiAgPHBhdGggZD0iTTE4IDhoLjAxIiAvPgogIDxwYXRoIGQ9Ik0yIDEyaDIwIiAvPgogIDxwYXRoIGQ9Ik02IDEydjQiIC8+CiAgPHBhdGggZD0iTTEwIDEydjQiIC8+CiAgPHBhdGggZD0iTTE0IDEydjQiIC8+CiAgPHBhdGggZD0iTTE4IDEydjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/keyboard-music\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst KeyboardMusic = createLucideIcon('keyboard-music', __iconNode);\n\nexport default KeyboardMusic;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaM,MAAAE,aAAA,GAAgBC,gBAAiB,mBAAkBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}