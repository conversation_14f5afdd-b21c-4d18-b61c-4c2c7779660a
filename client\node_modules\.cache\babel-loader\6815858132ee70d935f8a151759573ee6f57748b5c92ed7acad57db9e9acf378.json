{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n    // Clear specific error when user types\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n    setFormData({\n      ...formData,\n      role: tab\n    });\n    setErrors({});\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Common validations\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n\n    // Hospital specific validations\n    if (activeTab === 'hospital') {\n      if (!formData.hospitalName.trim()) {\n        newErrors.hospitalName = 'Hospital name is required';\n      }\n      if (!formData.address.trim()) {\n        newErrors.address = 'Address is required';\n      }\n      if (!formData.city.trim()) {\n        newErrors.city = 'City is required';\n      }\n      if (!formData.licenseNumber.trim()) {\n        newErrors.licenseNumber = 'License number is required';\n      }\n    }\n    return newErrors;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n\n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name\n      }));\n      setIsLoading(false);\n      navigate('/login');\n    }, 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground\",\n            children: /*#__PURE__*/_jsxDEV(Building2, {\n              className: \"h-8 w-8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-muted-foreground\",\n          children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-primary hover:text-primary/80\",\n            children: \"sign in to your existing account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"Get started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardDescription, {\n            children: \"Create your account to book appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"text-sm font-medium\",\n                children: \"Full Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"name\",\n                name: \"name\",\n                type: \"text\",\n                required: true,\n                value: formData.name,\n                onChange: handleChange,\n                placeholder: \"Enter your full name\",\n                className: errors.name ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"text-sm font-medium\",\n                children: \"Email address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                required: true,\n                value: formData.email,\n                onChange: handleChange,\n                placeholder: \"Enter your email\",\n                className: errors.email ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"phone\",\n                className: \"text-sm font-medium\",\n                children: \"Phone Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"phone\",\n                name: \"phone\",\n                type: \"tel\",\n                required: true,\n                value: formData.phone,\n                onChange: handleChange,\n                placeholder: \"+****************\",\n                className: errors.phone ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"dateOfBirth\",\n                  className: \"text-sm font-medium\",\n                  children: \"Date of Birth\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"dateOfBirth\",\n                  name: \"dateOfBirth\",\n                  type: \"date\",\n                  value: formData.dateOfBirth,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"gender\",\n                  className: \"text-sm font-medium\",\n                  children: \"Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"gender\",\n                  name: \"gender\",\n                  value: formData.gender,\n                  onChange: handleChange,\n                  className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"male\",\n                    children: \"Male\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"female\",\n                    children: \"Female\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"other\",\n                    children: \"Other\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"prefer-not-to-say\",\n                    children: \"Prefer not to say\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"text-sm font-medium\",\n                children: \"Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.password,\n                onChange: handleChange,\n                placeholder: \"Enter your password\",\n                className: errors.password ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"confirmPassword\",\n                className: \"text-sm font-medium\",\n                children: \"Confirm Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                placeholder: \"Confirm your password\",\n                className: errors.confirmPassword ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"role\",\n                className: \"text-sm font-medium\",\n                children: \"Register as\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"role\",\n                name: \"role\",\n                value: formData.role,\n                onChange: handleChange,\n                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"patient\",\n                  children: \"Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"doctor\",\n                  children: \"Doctor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"receptionist\",\n                  children: \"Receptionist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"terms\",\n                name: \"terms\",\n                type: \"checkbox\",\n                required: true,\n                className: \"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"terms\",\n                className: \"text-sm\",\n                children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: \"text-primary hover:text-primary/80\",\n                  children: \"Terms and Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: \"text-primary hover:text-primary/80\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"w-full\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this), \"Creating account...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this) : 'Create account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"PW8Qs9IORXp2FcZbLYvkiJt4RU4=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "styles", "jsxDEV", "_jsxDEV", "Register", "_s", "activeTab", "setActiveTab", "formData", "setFormData", "name", "email", "password", "confirmPassword", "phone", "role", "dateOfBirth", "gender", "hospitalName", "address", "city", "state", "pincode", "licenseNumber", "establishedYear", "hospitalType", "totalBeds", "emergencyServices", "ambulanceService", "description", "isLoading", "setIsLoading", "errors", "setErrors", "navigate", "handleChange", "e", "value", "type", "checked", "target", "handleTabChange", "tab", "validateForm", "newErrors", "trim", "test", "length", "handleSubmit", "preventDefault", "Object", "keys", "setTimeout", "console", "log", "localStorage", "setItem", "JSON", "stringify", "className", "children", "Building2", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "htmlFor", "Input", "id", "required", "onChange", "placeholder", "autoComplete", "<PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\n\nconst Register = () => {\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n    // Clear specific error when user types\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n    setFormData({\n      ...formData,\n      role: tab\n    });\n    setErrors({});\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Common validations\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n\n    // Hospital specific validations\n    if (activeTab === 'hospital') {\n      if (!formData.hospitalName.trim()) {\n        newErrors.hospitalName = 'Hospital name is required';\n      }\n      if (!formData.address.trim()) {\n        newErrors.address = 'Address is required';\n      }\n      if (!formData.city.trim()) {\n        newErrors.city = 'City is required';\n      }\n      if (!formData.licenseNumber.trim()) {\n        newErrors.licenseNumber = 'License number is required';\n      }\n    }\n\n    return newErrors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n      \n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name\n      }));\n\n      setIsLoading(false);\n      navigate('/login');\n    }, 2000);\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4\">\n      <div className=\"w-full max-w-md space-y-8\">\n        <div className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <div className=\"flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground\">\n              <Building2 className=\"h-8 w-8\" />\n            </div>\n          </div>\n          <h2 className=\"text-3xl font-bold\">Create your account</h2>\n          <p className=\"mt-2 text-muted-foreground\">\n            Or{' '}\n            <Link\n              to=\"/login\"\n              className=\"font-medium text-primary hover:text-primary/80\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Get started</CardTitle>\n            <CardDescription>\n              Create your account to book appointments\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label htmlFor=\"name\" className=\"text-sm font-medium\">\n                  Full Name *\n                </label>\n                <Input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={handleChange}\n                  placeholder=\"Enter your full name\"\n                  className={errors.name ? 'border-destructive' : ''}\n                />\n                {errors.name && <p className=\"text-sm text-destructive\">{errors.name}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"email\" className=\"text-sm font-medium\">\n                  Email address *\n                </label>\n                <Input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleChange}\n                  placeholder=\"Enter your email\"\n                  className={errors.email ? 'border-destructive' : ''}\n                />\n                {errors.email && <p className=\"text-sm text-destructive\">{errors.email}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"phone\" className=\"text-sm font-medium\">\n                  Phone Number *\n                </label>\n                <Input\n                  id=\"phone\"\n                  name=\"phone\"\n                  type=\"tel\"\n                  required\n                  value={formData.phone}\n                  onChange={handleChange}\n                  placeholder=\"+****************\"\n                  className={errors.phone ? 'border-destructive' : ''}\n                />\n                {errors.phone && <p className=\"text-sm text-destructive\">{errors.phone}</p>}\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"dateOfBirth\" className=\"text-sm font-medium\">\n                    Date of Birth\n                  </label>\n                  <Input\n                    id=\"dateOfBirth\"\n                    name=\"dateOfBirth\"\n                    type=\"date\"\n                    value={formData.dateOfBirth}\n                    onChange={handleChange}\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"gender\" className=\"text-sm font-medium\">\n                    Gender\n                  </label>\n                  <select\n                    id=\"gender\"\n                    name=\"gender\"\n                    value={formData.gender}\n                    onChange={handleChange}\n                    className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                  >\n                    <option value=\"\">Select</option>\n                    <option value=\"male\">Male</option>\n                    <option value=\"female\">Female</option>\n                    <option value=\"other\">Other</option>\n                    <option value=\"prefer-not-to-say\">Prefer not to say</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"password\" className=\"text-sm font-medium\">\n                  Password *\n                </label>\n                <Input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  placeholder=\"Enter your password\"\n                  className={errors.password ? 'border-destructive' : ''}\n                />\n                {errors.password && <p className=\"text-sm text-destructive\">{errors.password}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"confirmPassword\" className=\"text-sm font-medium\">\n                  Confirm Password *\n                </label>\n                <Input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  placeholder=\"Confirm your password\"\n                  className={errors.confirmPassword ? 'border-destructive' : ''}\n                />\n                {errors.confirmPassword && <p className=\"text-sm text-destructive\">{errors.confirmPassword}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"role\" className=\"text-sm font-medium\">\n                  Register as\n                </label>\n                <select\n                  id=\"role\"\n                  name=\"role\"\n                  value={formData.role}\n                  onChange={handleChange}\n                  className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                >\n                  <option value=\"patient\">Patient</option>\n                  <option value=\"doctor\">Doctor</option>\n                  <option value=\"receptionist\">Receptionist</option>\n                </select>\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  id=\"terms\"\n                  name=\"terms\"\n                  type=\"checkbox\"\n                  required\n                  className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\n                />\n                <label htmlFor=\"terms\" className=\"text-sm\">\n                  I agree to the{' '}\n                  <Link to=\"#\" className=\"text-primary hover:text-primary/80\">\n                    Terms and Conditions\n                  </Link>{' '}\n                  and{' '}\n                  <Link to=\"#\" className=\"text-primary hover:text-primary/80\">\n                    Privacy Policy\n                  </Link>\n                </label>\n              </div>\n\n              <Button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Creating account...\n                  </div>\n                ) : (\n                  'Create account'\n                )}\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvC;IACAY,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,SAAS;IAEf;IACAC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IAEV;IACAC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,iBAAiB,EAAE,KAAK;IACxBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMoC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAE9B,MAAMmC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/C/B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAG4B,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC;IACF;IACA,IAAIL,MAAM,CAACtB,IAAI,CAAC,EAAE;MAChBuB,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACtB,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM+B,eAAe,GAAIC,GAAG,IAAK;IAC/BnC,YAAY,CAACmC,GAAG,CAAC;IACjBjC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXO,IAAI,EAAE2B;IACR,CAAC,CAAC;IACFT,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACpC,QAAQ,CAACE,IAAI,CAACmC,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAClC,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACkC,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACjC,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACmC,IAAI,CAACtC,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/CiC,SAAS,CAACjC,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBgC,SAAS,CAAChC,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACmC,MAAM,GAAG,CAAC,EAAE;MACvCH,SAAS,CAAChC,QAAQ,GAAG,wCAAwC;IAC/D;IAEA,IAAIJ,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClD+B,SAAS,CAAC/B,eAAe,GAAG,wBAAwB;IACtD;IAEA,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC9B,KAAK,GAAG,0BAA0B;IAC9C;;IAEA;IACA,IAAIR,SAAS,KAAK,UAAU,EAAE;MAC5B,IAAI,CAACE,QAAQ,CAACU,YAAY,CAAC2B,IAAI,CAAC,CAAC,EAAE;QACjCD,SAAS,CAAC1B,YAAY,GAAG,2BAA2B;MACtD;MACA,IAAI,CAACV,QAAQ,CAACW,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAE;QAC5BD,SAAS,CAACzB,OAAO,GAAG,qBAAqB;MAC3C;MACA,IAAI,CAACX,QAAQ,CAACY,IAAI,CAACyB,IAAI,CAAC,CAAC,EAAE;QACzBD,SAAS,CAACxB,IAAI,GAAG,kBAAkB;MACrC;MACA,IAAI,CAACZ,QAAQ,CAACe,aAAa,CAACsB,IAAI,CAAC,CAAC,EAAE;QAClCD,SAAS,CAACrB,aAAa,GAAG,4BAA4B;MACxD;IACF;IAEA,OAAOqB,SAAS;EAClB,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,MAAML,SAAS,GAAGD,YAAY,CAAC,CAAC;IAChC,IAAIO,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACrCd,SAAS,CAACW,SAAS,CAAC;MACpB;IACF;IAEAb,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEb;IACAmB,UAAU,CAAC,MAAM;MACfC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE9C,QAAQ,CAAC;;MAE3C;MACA+C,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;QAC1C/C,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBI,IAAI,EAAEP,QAAQ,CAACO,IAAI;QACnBL,IAAI,EAAEF,QAAQ,CAACE;MACjB,CAAC,CAAC,CAAC;MAEHqB,YAAY,CAAC,KAAK,CAAC;MACnBG,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACE/B,OAAA;IAAKwD,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnFzD,OAAA;MAAKwD,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCzD,OAAA;QAAKwD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzD,OAAA;UAAKwD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCzD,OAAA;YAAKwD,SAAS,EAAC,0FAA0F;YAAAC,QAAA,eACvGzD,OAAA,CAAC0D,SAAS;cAACF,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9D,OAAA;UAAIwD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3D9D,OAAA;UAAGwD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,IACtC,EAAC,GAAG,eACNzD,OAAA,CAACJ,IAAI;YACHmE,EAAE,EAAC,QAAQ;YACXP,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAC3D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN9D,OAAA,CAACgE,IAAI;QAAAP,QAAA,gBACHzD,OAAA,CAACiE,UAAU;UAAAR,QAAA,gBACTzD,OAAA,CAACkE,SAAS;YAAAT,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAClC9D,OAAA,CAACmE,eAAe;YAAAV,QAAA,EAAC;UAEjB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACb9D,OAAA,CAACoE,WAAW;UAAAX,QAAA,eACVzD,OAAA;YAAMqE,QAAQ,EAAExB,YAAa;YAACW,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDzD,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzD,OAAA;gBAAOsE,OAAO,EAAC,MAAM;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9D,OAAA,CAACuE,KAAK;gBACJC,EAAE,EAAC,MAAM;gBACTjE,IAAI,EAAC,MAAM;gBACX4B,IAAI,EAAC,MAAM;gBACXsC,QAAQ;gBACRvC,KAAK,EAAE7B,QAAQ,CAACE,IAAK;gBACrBmE,QAAQ,EAAE1C,YAAa;gBACvB2C,WAAW,EAAC,sBAAsB;gBAClCnB,SAAS,EAAE3B,MAAM,CAACtB,IAAI,GAAG,oBAAoB,GAAG;cAAG;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EACDjC,MAAM,CAACtB,IAAI,iBAAIP,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE5B,MAAM,CAACtB;cAAI;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEN9D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzD,OAAA;gBAAOsE,OAAO,EAAC,OAAO;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEvD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9D,OAAA,CAACuE,KAAK;gBACJC,EAAE,EAAC,OAAO;gBACVjE,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,OAAO;gBACZyC,YAAY,EAAC,OAAO;gBACpBH,QAAQ;gBACRvC,KAAK,EAAE7B,QAAQ,CAACG,KAAM;gBACtBkE,QAAQ,EAAE1C,YAAa;gBACvB2C,WAAW,EAAC,kBAAkB;gBAC9BnB,SAAS,EAAE3B,MAAM,CAACrB,KAAK,GAAG,oBAAoB,GAAG;cAAG;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACDjC,MAAM,CAACrB,KAAK,iBAAIR,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE5B,MAAM,CAACrB;cAAK;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAEN9D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzD,OAAA;gBAAOsE,OAAO,EAAC,OAAO;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEvD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9D,OAAA,CAACuE,KAAK;gBACJC,EAAE,EAAC,OAAO;gBACVjE,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,KAAK;gBACVsC,QAAQ;gBACRvC,KAAK,EAAE7B,QAAQ,CAACM,KAAM;gBACtB+D,QAAQ,EAAE1C,YAAa;gBACvB2C,WAAW,EAAC,mBAAmB;gBAC/BnB,SAAS,EAAE3B,MAAM,CAAClB,KAAK,GAAG,oBAAoB,GAAG;cAAG;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACDjC,MAAM,CAAClB,KAAK,iBAAIX,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE5B,MAAM,CAAClB;cAAK;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAEN9D,OAAA;cAAKwD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCzD,OAAA;gBAAKwD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzD,OAAA;kBAAOsE,OAAO,EAAC,aAAa;kBAACd,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAE7D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9D,OAAA,CAACuE,KAAK;kBACJC,EAAE,EAAC,aAAa;kBAChBjE,IAAI,EAAC,aAAa;kBAClB4B,IAAI,EAAC,MAAM;kBACXD,KAAK,EAAE7B,QAAQ,CAACQ,WAAY;kBAC5B6D,QAAQ,EAAE1C;gBAAa;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9D,OAAA;gBAAKwD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzD,OAAA;kBAAOsE,OAAO,EAAC,QAAQ;kBAACd,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAExD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9D,OAAA;kBACEwE,EAAE,EAAC,QAAQ;kBACXjE,IAAI,EAAC,QAAQ;kBACb2B,KAAK,EAAE7B,QAAQ,CAACS,MAAO;kBACvB4D,QAAQ,EAAE1C,YAAa;kBACvBwB,SAAS,EAAC,4MAA4M;kBAAAC,QAAA,gBAEtNzD,OAAA;oBAAQkC,KAAK,EAAC,EAAE;oBAAAuB,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChC9D,OAAA;oBAAQkC,KAAK,EAAC,MAAM;oBAAAuB,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC9D,OAAA;oBAAQkC,KAAK,EAAC,QAAQ;oBAAAuB,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC9D,OAAA;oBAAQkC,KAAK,EAAC,OAAO;oBAAAuB,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC9D,OAAA;oBAAQkC,KAAK,EAAC,mBAAmB;oBAAAuB,QAAA,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzD,OAAA;gBAAOsE,OAAO,EAAC,UAAU;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9D,OAAA,CAACuE,KAAK;gBACJC,EAAE,EAAC,UAAU;gBACbjE,IAAI,EAAC,UAAU;gBACf4B,IAAI,EAAC,UAAU;gBACfyC,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRvC,KAAK,EAAE7B,QAAQ,CAACI,QAAS;gBACzBiE,QAAQ,EAAE1C,YAAa;gBACvB2C,WAAW,EAAC,qBAAqB;gBACjCnB,SAAS,EAAE3B,MAAM,CAACpB,QAAQ,GAAG,oBAAoB,GAAG;cAAG;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,EACDjC,MAAM,CAACpB,QAAQ,iBAAIT,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE5B,MAAM,CAACpB;cAAQ;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eAEN9D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzD,OAAA;gBAAOsE,OAAO,EAAC,iBAAiB;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEjE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9D,OAAA,CAACuE,KAAK;gBACJC,EAAE,EAAC,iBAAiB;gBACpBjE,IAAI,EAAC,iBAAiB;gBACtB4B,IAAI,EAAC,UAAU;gBACfyC,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRvC,KAAK,EAAE7B,QAAQ,CAACK,eAAgB;gBAChCgE,QAAQ,EAAE1C,YAAa;gBACvB2C,WAAW,EAAC,uBAAuB;gBACnCnB,SAAS,EAAE3B,MAAM,CAACnB,eAAe,GAAG,oBAAoB,GAAG;cAAG;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EACDjC,MAAM,CAACnB,eAAe,iBAAIV,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE5B,MAAM,CAACnB;cAAe;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC,eAEN9D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzD,OAAA;gBAAOsE,OAAO,EAAC,MAAM;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9D,OAAA;gBACEwE,EAAE,EAAC,MAAM;gBACTjE,IAAI,EAAC,MAAM;gBACX2B,KAAK,EAAE7B,QAAQ,CAACO,IAAK;gBACrB8D,QAAQ,EAAE1C,YAAa;gBACvBwB,SAAS,EAAC,4MAA4M;gBAAAC,QAAA,gBAEtNzD,OAAA;kBAAQkC,KAAK,EAAC,SAAS;kBAAAuB,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC9D,OAAA;kBAAQkC,KAAK,EAAC,QAAQ;kBAAAuB,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC9D,OAAA;kBAAQkC,KAAK,EAAC,cAAc;kBAAAuB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN9D,OAAA;cAAKwD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CzD,OAAA;gBACEwE,EAAE,EAAC,OAAO;gBACVjE,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,UAAU;gBACfsC,QAAQ;gBACRjB,SAAS,EAAC;cAAiE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACF9D,OAAA;gBAAOsE,OAAO,EAAC,OAAO;gBAACd,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAC,gBAC3B,EAAC,GAAG,eAClBzD,OAAA,CAACJ,IAAI;kBAACmE,EAAE,EAAC,GAAG;kBAACP,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAE5D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACP9D,OAAA,CAACJ,IAAI;kBAACmE,EAAE,EAAC,GAAG;kBAACP,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAE5D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9D,OAAA,CAAC6E,MAAM;cACL1C,IAAI,EAAC,QAAQ;cACb2C,QAAQ,EAAEnD,SAAU;cACpB6B,SAAS,EAAC,QAAQ;cAAAC,QAAA,EAEjB9B,SAAS,gBACR3B,OAAA;gBAAKwD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCzD,OAAA;kBAAKwD,SAAS,EAAC;gBAAgE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uBAExF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CArVID,QAAQ;EAAA,QA+BKJ,WAAW;AAAA;AAAAkF,EAAA,GA/BxB9E,QAAQ;AAuVd,eAAeA,QAAQ;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}