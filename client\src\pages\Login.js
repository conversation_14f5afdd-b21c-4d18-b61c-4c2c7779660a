import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styles from './Login.module.css';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'patient',
    rememberMe: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    // Mock authentication - simulate API call
    setTimeout(() => {
      console.log('Login attempt:', formData);

      // Mock successful login
      localStorage.setItem('user', JSON.stringify({
        email: formData.email,
        role: formData.role,
        name: '<PERSON>'
      }));

      setIsLoading(false);

      // Redirect based on role
      switch (formData.role) {
        case 'admin':
          navigate('/admin');
          break;
        case 'doctor':
          navigate('/doctor');
          break;
        case 'receptionist':
          navigate('/reception');
          break;
        default:
          navigate('/');
      }
    }, 1500);
  };

  return (
    <div className={styles.container}>
      {/* Left Panel - Hero */}
      <div className={styles.heroPanel}>
        <div className={styles.heroContent}>
          <div className={styles.logo}>
            <span className={styles.logoIcon}>🏥</span>
            <span className={styles.logoText}>Hope Medics</span>
          </div>
          <h1 className={styles.heroTitle}>
            Welcome Back to Hope Medics
          </h1>
          <p className={styles.heroSubtitle}>
            Your trusted healthcare partner. Access your account to manage appointments,
            view medical records, and connect with our medical professionals.
          </p>
          <div className={styles.heroFeatures}>
            <div className={styles.feature}>
              <span className={styles.featureIcon}>📅</span>
              <span>Easy Appointment Booking</span>
            </div>
            <div className={styles.feature}>
              <span className={styles.featureIcon}>👨‍⚕️</span>
              <span>Expert Medical Care</span>
            </div>
            <div className={styles.feature}>
              <span className={styles.featureIcon}>🏥</span>
              <span>Top-rated Hospitals</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Login Form */}
      <div className={styles.formPanel}>
        <div className={styles.formContainer}>
          <div className={styles.formHeader}>
            <h2>Sign In</h2>
            <p>Enter your credentials to access your account</p>
          </div>

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email"
                className="input"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                className="input"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="role">Login as</label>
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                className="input"
                required
              >
                <option value="patient">Patient</option>
                <option value="doctor">Doctor</option>
                <option value="receptionist">Receptionist</option>
                <option value="admin">Admin</option>
              </select>
            </div>

            <div className={styles.checkboxGroup}>
              <input
                type="checkbox"
                id="rememberMe"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                className={styles.checkbox}
              />
              <label htmlFor="rememberMe" className={styles.checkboxLabel}>
                Remember me
              </label>
            </div>

            <button type="submit" disabled={isLoading} className="btn-primary w-full">
              {isLoading ? (
                <>
                  <span className={styles.spinner}></span>
                  Signing in...
                </>
              ) : (
                '🔐 Sign In'
              )}
            </button>
          </form>

          <div className={styles.formFooter}>
            <p>
              Don't have an account?{' '}
              <Link to="/register" className={styles.link}>
                Register here
              </Link>
            </p>
          </div>

          {/* Demo Credentials */}
          <div className={styles.demoCredentials}>
            <h4>Demo Credentials:</h4>
            <div className={styles.credentialsList}>
              <div><strong>Patient:</strong> <EMAIL> / password</div>
              <div><strong>Doctor:</strong> <EMAIL> / password</div>
              <div><strong>Receptionist:</strong> <EMAIL> / password</div>
              <div><strong>Admin:</strong> <EMAIL> / password</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
