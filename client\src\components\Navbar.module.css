.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.navContainer {
  padding: 0 1rem;
}

.navContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--dark-grey);
  transition: var(--transition);
}

.logo:hover {
  transform: scale(1.05);
}

.logoIcon {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white);
}

.logoText {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.desktopNav {
  display: none;
  align-items: center;
  gap: 0.5rem;
}

@media (min-width: 768px) {
  .desktopNav {
    display: flex;
  }
}

.navLink {
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--dark-grey);
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
}

.navLink:hover {
  background: rgba(0, 121, 107, 0.1);
  color: var(--primary-color);
}

.navLinkActive {
  background: var(--primary-color);
  color: var(--white);
}

.navLinkActive:hover {
  background: var(--primary-dark);
  color: var(--white);
}

.mobileMenuButton {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--dark-grey);
  transition: var(--transition);
}

.mobileMenuButton:hover {
  color: var(--primary-color);
}

@media (min-width: 768px) {
  .mobileMenuButton {
    display: none;
  }
}

.hamburgerIcon {
  width: 24px;
  height: 24px;
}

.mobileNav {
  display: block;
  border-top: 1px solid var(--border-color);
  padding: 1rem 0;
  background: var(--white);
}

@media (min-width: 768px) {
  .mobileNav {
    display: none;
  }
}

.mobileNavLink {
  display: block;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--dark-grey);
  font-weight: 500;
  border-radius: var(--border-radius);
  margin: 0.25rem 0;
  transition: var(--transition);
}

.mobileNavLink:hover {
  background: rgba(0, 121, 107, 0.1);
  color: var(--primary-color);
}

.mobileNavLinkActive {
  background: var(--primary-color);
  color: var(--white);
}

.mobileNavLinkActive:hover {
  background: var(--primary-dark);
  color: var(--white);
}
