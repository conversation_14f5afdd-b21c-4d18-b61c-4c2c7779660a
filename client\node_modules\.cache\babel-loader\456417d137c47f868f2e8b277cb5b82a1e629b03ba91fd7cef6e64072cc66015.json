{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 19V7a2 2 0 0 0-2-2H9\",\n  key: \"15peso\"\n}], [\"path\", {\n  d: \"M15 19H9\",\n  key: \"18q6dt\"\n}], [\"path\", {\n  d: \"M19 19h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.62L18.3 9.38a1 1 0 0 0-.78-.38H14\",\n  key: \"1dkp3j\"\n}], [\"path\", {\n  d: \"M2 13v5a1 1 0 0 0 1 1h2\",\n  key: \"pkmmzz\"\n}], [\"path\", {\n  d: \"M4 3 2.15 5.15a.495.495 0 0 0 .35.86h2.15a.47.47 0 0 1 .35.86L3 9.02\",\n  key: \"1n26pd\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"1nxcgd\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"gzo7y7\"\n}]];\nconst TruckElectric = createLucideIcon(\"truck-electric\", __iconNode);\nexport { __iconNode, TruckElectric as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "TruckElectric", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\truck-electric.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 19V7a2 2 0 0 0-2-2H9', key: '15peso' }],\n  ['path', { d: 'M15 19H9', key: '18q6dt' }],\n  [\n    'path',\n    {\n      d: 'M19 19h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.62L18.3 9.38a1 1 0 0 0-.78-.38H14',\n      key: '1dkp3j',\n    },\n  ],\n  ['path', { d: 'M2 13v5a1 1 0 0 0 1 1h2', key: 'pkmmzz' }],\n  [\n    'path',\n    { d: 'M4 3 2.15 5.15a.495.495 0 0 0 .35.86h2.15a.47.47 0 0 1 .35.86L3 9.02', key: '1n26pd' },\n  ],\n  ['circle', { cx: '17', cy: '19', r: '2', key: '1nxcgd' }],\n  ['circle', { cx: '7', cy: '19', r: '2', key: 'gzo7y7' }],\n];\n\n/**\n * @component @name TruckElectric\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTlWN2EyIDIgMCAwIDAtMi0ySDkiIC8+CiAgPHBhdGggZD0iTTE1IDE5SDkiIC8+CiAgPHBhdGggZD0iTTE5IDE5aDJhMSAxIDAgMCAwIDEtMXYtMy42NWExIDEgMCAwIDAtLjIyLS42MkwxOC4zIDkuMzhhMSAxIDAgMCAwLS43OC0uMzhIMTQiIC8+CiAgPHBhdGggZD0iTTIgMTN2NWExIDEgMCAwIDAgMSAxaDIiIC8+CiAgPHBhdGggZD0iTTQgMyAyLjE1IDUuMTVhLjQ5NS40OTUgMCAwIDAgLjM1Ljg2aDIuMTVhLjQ3LjQ3IDAgMCAxIC4zNS44NkwzIDkuMDIiIC8+CiAgPGNpcmNsZSBjeD0iMTciIGN5PSIxOSIgcj0iMiIgLz4KICA8Y2lyY2xlIGN4PSI3IiBjeT0iMTkiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/truck-electric\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TruckElectric = createLucideIcon('truck-electric', __iconNode);\n\nexport default TruckElectric;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CACE,QACA;EAAED,CAAA,EAAG,sEAAwE;EAAAC,GAAA,EAAK;AAAS,EAC7F,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD;AAaM,MAAAI,aAAA,GAAgBC,gBAAiB,mBAAkBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}