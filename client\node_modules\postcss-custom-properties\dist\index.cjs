"use strict";var e=require("postcss-value-parser"),t=require("path"),r=require("url"),o=require("postcss"),s=require("fs");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=n(e),c=n(t);const u=/(!\s*)?postcss-custom-properties:\s*off\b/i,l=new WeakMap;function p(e){if(!e||!e.nodes)return!1;if(l.has(e))return l.get(e);const t=e.some((e=>d(e,u)));return l.set(e,t),t}const f=/(!\s*)?postcss-custom-properties:\s*ignore\s+next\b/i;function m(e){return!!e&&(!!p(e.parent)||d(e.prev(),f))}function d(e,t){return e&&"comment"===e.type&&t.test(e.text)}function w(e,t){const r=new Map,o=new Map;e.nodes.slice().forEach((e=>{if(p(e))return;const s=h(e)?r:g(e)?o:null;s&&(e.nodes.slice().forEach((e=>{if(e.variable&&!m(e)){const{prop:r}=e;s.set(r,e.value),t.preserve||e.remove()}})),!t.preserve&&b(e)&&e.remove())}));const s=new Map;for(const[e,t]of r.entries())s.set(e,a.default(t));for(const[e,t]of o.entries())s.set(e,a.default(t));return s}const v=/^html$/i,y=/^:root$/i,h=e=>"rule"===e.type&&e.selector.split(",").some((e=>v.test(e)))&&Object(e.nodes).length,g=e=>"rule"===e.type&&e.selector.split(",").some((e=>y.test(e)))&&Object(e.nodes).length,b=e=>0===Object(e.nodes).length;function j(e){const t=new Map;if("customProperties"in e)for(const[r,o]of Object.entries(e.customProperties))t.set(r,a.default(o.toString()));if("custom-properties"in e)for(const[r,o]of Object.entries(e["custom-properties"]))t.set(r,a.default(o.toString()));return t}async function O(e){let t;try{t=await(o=e,Promise.resolve().then((function(){return i(require(o))})))}catch(o){t=await function(e){return Promise.resolve().then((function(){return i(require(e))}))}(r.pathToFileURL(e).href)}var o;return j("default"in t?t.default:t)}async function $(e){const t=(await Promise.all(e.map((async e=>{if(e instanceof Promise?e=await e:e instanceof Function&&(e=await e()),"string"==typeof e){const t=c.default.resolve(e);return{type:c.default.extname(t).slice(1).toLowerCase(),from:t}}if("customProperties"in e&&Object(e.customProperties)===e.customProperties)return e;if("custom-properties"in e&&Object(e["custom-properties"])===e["custom-properties"])return e;if("from"in e){const t=c.default.resolve(e.from);let r=e.type;return r||(r=c.default.extname(t).slice(1).toLowerCase()),{type:r,from:t}}return Object.keys(e).length,null})))).filter((e=>!!e)),r=await Promise.all(t.map((async e=>{if("type"in e&&"from"in e){if("css"===e.type||"pcss"===e.type)return await async function(e){const t=await s.promises.readFile(e);return w(o.parse(t,{from:e.toString()}),{preserve:!0})}(e.from);if("js"===e.type||"cjs"===e.type)return await O(e.from);if("mjs"===e.type)return await O(e.from);if("json"===e.type)return await async function(e){return j(await x(e))}(e.from);throw new Error("Invalid source type: "+e.type)}return j(e)}))),n=new Map;return r.forEach((e=>{for(const[t,r]of e.entries())n.set(t,r)})),n}const x=async e=>JSON.parse((await s.promises.readFile(e)).toString());function P(e,t){return e.nodes&&e.nodes.length&&e.nodes.slice().forEach((r=>{if(S(r)){const[o,...s]=r.nodes.filter((e=>"div"!==e.type)),{value:n}=o,i=e.nodes.indexOf(r);if(t.has(n)){const r=t.get(n).nodes;!function(e,t,r){const o=new Map(t);o.delete(r),P(e,o)}({nodes:r},t,n),i>-1&&e.nodes.splice(i,1,...r)}else s.length&&(i>-1&&e.nodes.splice(i,1,...r.nodes.slice(r.nodes.indexOf(s[0]))),P(e,t))}else P(r,t)})),e.toString()}const F=/^var$/i,S=e=>"function"===e.type&&F.test(e.value)&&Object(e.nodes).length>0;var k=(e,t,r)=>{if(M(e)&&!m(e)){const o=e.value;let s=P(a.default(o),t);const n=new Set;for(;s.includes("--")&&s.includes("var(")&&!n.has(s);){n.add(s);s=P(a.default(s),t)}if(s!==o){if(function(e,t){if(!e||!e.parent)return!1;let r=!1;const o=e.parent.index(e);return e.parent.each(((s,n)=>s!==e&&(!(n>=o)&&void("decl"===s.type&&s.prop.toLowerCase()===e.prop.toLowerCase()&&s.value===t&&(r=!0))))),r}(e,s))return void(r.preserve||e.remove());if(r.preserve){const t=e.cloneBefore({value:s});E(t)&&(t.raws.value.value=t.value.replace(q,"$1"),t.raws.value.raw=t.raws.value.value+t.raws.value.raw.replace(q,"$2"))}else e.value=s,E(e)&&(e.raws.value.value=e.value.replace(q,"$1"),e.raws.value.raw=e.raws.value.value+e.raws.value.raw.replace(q,"$2"))}}};const M=e=>!e.variable&&e.value.includes("--")&&e.value.includes("var("),E=e=>"value"in Object(Object(e.raws).value)&&"raw"in e.raws.value&&q.test(e.raws.value.raw),q=/^([\W\w]+)(\s*\/\*[\W\w]+?\*\/)$/;async function L(e,t,r){"css"===t&&await async function(e,t){const r=`:root {\n${Object.keys(t).reduce(((e,r)=>(e.push(`\t${r}: ${t[r]};`),e)),[]).join("\n")}\n}\n`;await s.promises.writeFile(e,r)}(e,r),"scss"===t&&await async function(e,t){const r=`${Object.keys(t).reduce(((e,r)=>{const o=r.replace("--","$");return e.push(`${o}: ${t[r]};`),e}),[]).join("\n")}\n`;await s.promises.writeFile(e,r)}(e,r),"js"===t&&await async function(e,t){const r=`module.exports = {\n\tcustomProperties: {\n${Object.keys(t).reduce(((e,r)=>(e.push(`\t\t'${N(r)}': '${N(t[r])}'`),e)),[]).join(",\n")}\n\t}\n};\n`;await s.promises.writeFile(e,r)}(e,r),"json"===t&&await async function(e,t){const r=`${JSON.stringify({"custom-properties":t},null,"  ")}\n`;await s.promises.writeFile(e,r)}(e,r),"mjs"===t&&await async function(e,t){const r=`export const customProperties = {\n${Object.keys(t).reduce(((e,r)=>(e.push(`\t'${N(r)}': '${N(t[r])}'`),e)),[]).join(",\n")}\n};\n`;await s.promises.writeFile(e,r)}(e,r)}function C(e){const t={};for(const[r,o]of e.entries())t[r]=o.toString();return t}const N=e=>e.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r"),T=e=>{const t=!("preserve"in Object(e))||Boolean(e.preserve),r="overrideImportFromWithRoot"in Object(e)&&Boolean(e.overrideImportFromWithRoot),o="disableDeprecationNotice"in Object(e)&&Boolean(e.disableDeprecationNotice);let s=[];Array.isArray(null==e?void 0:e.importFrom)?s=e.importFrom:null!=e&&e.importFrom&&(s=[e.importFrom]);let n=[];Array.isArray(null==e?void 0:e.exportTo)?n=e.exportTo:null!=e&&e.exportTo&&(n=[e.exportTo]);const i=$(s),a=0===s.length&&0===n.length;return{postcssPlugin:"postcss-custom-properties",prepare(){let e=new Map;return a?{Once:r=>{e=w(r,{preserve:t})},Declaration:r=>{k(r,e,{preserve:t})},OnceExit:()=>{e.clear()}}:{Once:async o=>{const s=(await i).entries(),a=w(o,{preserve:t}).entries();if(r)for(const[t,r]of[...s,...a])e.set(t,r);else for(const[t,r]of[...a,...s])e.set(t,r);await function(e,t){return Promise.all(t.map((async t=>{if(t instanceof Function)return void await t(C(e));if("string"==typeof t){const r=c.default.resolve(t),o=c.default.extname(r).slice(1).toLowerCase();return void await L(r,o,C(e))}let r={};if(r="toJSON"in t?t.toJSON(C(e)):C(e),"to"in t){const e=c.default.resolve(t.to);let o=t.type;return o||(o=c.default.extname(e).slice(1).toLowerCase()),void await L(e,o,r)}"customProperties"in t?t.customProperties=r:"custom-properties"in t&&(t["custom-properties"]=r)})))}(e,n)},Declaration:r=>{k(r,e,{preserve:t})},OnceExit:(t,{result:r})=>{!o&&(s.length>0||n.length>0)&&t.warn(r,'"importFrom" and "exportTo" will be removed in a future version of postcss-custom-properties.\nWe are looking for insights and anecdotes on how these features are used so that we can design the best alternative.\nPlease let us know if our proposal will work for you.\nVisit the discussion on github for more details. https://github.com/csstools/postcss-plugins/discussions/192'),e.clear()}}}}};T.postcss=!0,module.exports=T;
