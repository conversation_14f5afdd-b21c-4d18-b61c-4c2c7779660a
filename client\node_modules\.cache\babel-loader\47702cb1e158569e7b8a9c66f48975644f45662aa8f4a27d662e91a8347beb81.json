{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 12h6\",\n  key: \"15xry1\"\n}], [\"path\", {\n  d: \"M8 12H2\",\n  key: \"1jqql6\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M12 8v2\",\n  key: \"1woqiv\"\n}], [\"path\", {\n  d: \"M12 14v2\",\n  key: \"8jcxud\"\n}], [\"path\", {\n  d: \"M12 20v2\",\n  key: \"1lh1kg\"\n}], [\"path\", {\n  d: \"m19 15 3-3-3-3\",\n  key: \"wjy7rq\"\n}], [\"path\", {\n  d: \"m5 9-3 3 3 3\",\n  key: \"j64kie\"\n}]];\nconst UnfoldHorizontal = createLucideIcon(\"unfold-horizontal\", __iconNode);\nexport { __iconNode, UnfoldHorizontal as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "UnfoldHorizontal", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\unfold-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 12h6', key: '15xry1' }],\n  ['path', { d: 'M8 12H2', key: '1jqql6' }],\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  ['path', { d: 'M12 8v2', key: '1woqiv' }],\n  ['path', { d: 'M12 14v2', key: '8jcxud' }],\n  ['path', { d: 'M12 20v2', key: '1lh1kg' }],\n  ['path', { d: 'm19 15 3-3-3-3', key: 'wjy7rq' }],\n  ['path', { d: 'm5 9-3 3 3 3', key: 'j64kie' }],\n];\n\n/**\n * @component @name UnfoldHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTJoNiIgLz4KICA8cGF0aCBkPSJNOCAxMkgyIiAvPgogIDxwYXRoIGQ9Ik0xMiAydjIiIC8+CiAgPHBhdGggZD0iTTEyIDh2MiIgLz4KICA8cGF0aCBkPSJNMTIgMTR2MiIgLz4KICA8cGF0aCBkPSJNMTIgMjB2MiIgLz4KICA8cGF0aCBkPSJtMTkgMTUgMy0zLTMtMyIgLz4KICA8cGF0aCBkPSJtNSA5LTMgMyAzIDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/unfold-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UnfoldHorizontal = createLucideIcon('unfold-horizontal', __iconNode);\n\nexport default UnfoldHorizontal;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/C;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,sBAAqBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}