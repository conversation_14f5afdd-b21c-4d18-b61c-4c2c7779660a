{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 2v6\",\n  key: \"17ngun\"\n}], [\"path\", {\n  d: \"M15 2v6\",\n  key: \"s7yy2p\"\n}], [\"path\", {\n  d: \"M12 17v5\",\n  key: \"bb1du9\"\n}], [\"path\", {\n  d: \"M5 8h14\",\n  key: \"pcz4l3\"\n}], [\"path\", {\n  d: \"M6 11V8h12v3a6 6 0 1 1-12 0Z\",\n  key: \"wtfw2c\"\n}]];\nconst Plug2 = createLucideIcon(\"plug-2\", __iconNode);\nexport { __iconNode, Plug2 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Plug2", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\plug-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M9 2v6', key: '17ngun' }],\n  ['path', { d: 'M15 2v6', key: 's7yy2p' }],\n  ['path', { d: 'M12 17v5', key: 'bb1du9' }],\n  ['path', { d: 'M5 8h14', key: 'pcz4l3' }],\n  ['path', { d: 'M6 11V8h12v3a6 6 0 1 1-12 0Z', key: 'wtfw2c' }],\n];\n\n/**\n * @component @name Plug2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAydjYiIC8+CiAgPHBhdGggZD0iTTE1IDJ2NiIgLz4KICA8cGF0aCBkPSJNMTIgMTd2NSIgLz4KICA8cGF0aCBkPSJNNSA4aDE0IiAvPgogIDxwYXRoIGQ9Ik02IDExVjhoMTJ2M2E2IDYgMCAxIDEtMTIgMFoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/plug-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plug2 = createLucideIcon('plug-2', __iconNode);\n\nexport default Plug2;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/D;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}