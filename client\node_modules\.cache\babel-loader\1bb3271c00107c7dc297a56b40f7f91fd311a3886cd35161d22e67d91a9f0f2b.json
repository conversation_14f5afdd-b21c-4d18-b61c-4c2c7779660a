{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\components\\\\HospitalCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './HospitalCard.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HospitalCard = ({\n  hospital\n}) => {\n  const {\n    id,\n    name,\n    city,\n    image,\n    pricePerDay,\n    rating,\n    totalBeds,\n    availableBeds,\n    services\n  } = hospital;\n  const availabilityPercentage = (availableBeds / totalBeds * 100).toFixed(0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.card,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.imageContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: image || '/api/placeholder/400/200',\n        alt: name,\n        className: styles.image\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.rating,\n        children: [\"\\u2B50 \", rating]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.content,\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: styles.hospitalName,\n        children: name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: styles.city,\n        children: city\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.services,\n        children: [services.slice(0, 3).map((service, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: styles.serviceTag,\n          children: service\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)), services.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: styles.moreServices,\n          children: [\"+\", services.length - 3, \" more\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.availability,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.availabilityHeader,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Bed Availability\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.availabilityCount,\n            children: [availableBeds, \"/\", totalBeds]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.progressBar,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.progressFill,\n            style: {\n              width: `${availabilityPercentage}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.pricing,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: styles.priceLabel,\n          children: \"Starting from\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: styles.price,\n          children: [\"\\u20B9\", pricePerDay.toLocaleString(), \"/day\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.actions,\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: `/hospital/${id}`,\n          className: `btn-secondary ${styles.detailsBtn}`,\n          children: \"View Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/book?hospital=${id}`,\n          className: `btn-accent ${styles.bookBtn}`,\n          children: \"Book Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = HospitalCard;\nexport default HospitalCard;\nvar _c;\n$RefreshReg$(_c, \"HospitalCard\");", "map": {"version": 3, "names": ["React", "Link", "styles", "jsxDEV", "_jsxDEV", "HospitalCard", "hospital", "id", "name", "city", "image", "pricePerDay", "rating", "totalBeds", "availableBeds", "services", "availabilityPercentage", "toFixed", "className", "card", "children", "imageContainer", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "hospitalName", "slice", "map", "service", "index", "serviceTag", "length", "moreServices", "availability", "availabilityHeader", "availabilityCount", "progressBar", "progressFill", "style", "width", "pricing", "priceLabel", "price", "toLocaleString", "actions", "to", "detailsBtn", "bookBtn", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/components/HospitalCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './HospitalCard.module.css';\n\nconst HospitalCard = ({ hospital }) => {\n  const {\n    id,\n    name,\n    city,\n    image,\n    pricePerDay,\n    rating,\n    totalBeds,\n    availableBeds,\n    services\n  } = hospital;\n\n  const availabilityPercentage = ((availableBeds / totalBeds) * 100).toFixed(0);\n\n  return (\n    <div className={styles.card}>\n      <div className={styles.imageContainer}>\n        <img\n          src={image || '/api/placeholder/400/200'}\n          alt={name}\n          className={styles.image}\n        />\n        <div className={styles.rating}>\n          ⭐ {rating}\n        </div>\n      </div>\n\n      <div className={styles.content}>\n        <h3 className={styles.hospitalName}>{name}</h3>\n        <p className={styles.city}>{city}</p>\n        \n        <div className={styles.services}>\n          {services.slice(0, 3).map((service, index) => (\n            <span key={index} className={styles.serviceTag}>\n              {service}\n            </span>\n          ))}\n          {services.length > 3 && (\n            <span className={styles.moreServices}>+{services.length - 3} more</span>\n          )}\n        </div>\n\n        <div className={styles.availability}>\n          <div className={styles.availabilityHeader}>\n            <span>Bed Availability</span>\n            <span className={styles.availabilityCount}>\n              {availableBeds}/{totalBeds}\n            </span>\n          </div>\n          <div className={styles.progressBar}>\n            <div \n              className={styles.progressFill}\n              style={{ width: `${availabilityPercentage}%` }}\n            ></div>\n          </div>\n        </div>\n\n        <div className={styles.pricing}>\n          <span className={styles.priceLabel}>Starting from</span>\n          <span className={styles.price}>₹{pricePerDay.toLocaleString()}/day</span>\n        </div>\n\n        <div className={styles.actions}>\n          <Link to={`/hospital/${id}`} className={`btn-secondary ${styles.detailsBtn}`}>\n            View Details\n          </Link>\n          <Link to={`/book?hospital=${id}`} className={`btn-accent ${styles.bookBtn}`}>\n            Book Now\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HospitalCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACrC,MAAM;IACJC,EAAE;IACFC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC,aAAa;IACbC;EACF,CAAC,GAAGT,QAAQ;EAEZ,MAAMU,sBAAsB,GAAG,CAAEF,aAAa,GAAGD,SAAS,GAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC;EAE7E,oBACEb,OAAA;IAAKc,SAAS,EAAEhB,MAAM,CAACiB,IAAK;IAAAC,QAAA,gBAC1BhB,OAAA;MAAKc,SAAS,EAAEhB,MAAM,CAACmB,cAAe;MAAAD,QAAA,gBACpChB,OAAA;QACEkB,GAAG,EAAEZ,KAAK,IAAI,0BAA2B;QACzCa,GAAG,EAAEf,IAAK;QACVU,SAAS,EAAEhB,MAAM,CAACQ;MAAM;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACFvB,OAAA;QAAKc,SAAS,EAAEhB,MAAM,CAACU,MAAO;QAAAQ,QAAA,GAAC,SAC3B,EAACR,MAAM;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAKc,SAAS,EAAEhB,MAAM,CAAC0B,OAAQ;MAAAR,QAAA,gBAC7BhB,OAAA;QAAIc,SAAS,EAAEhB,MAAM,CAAC2B,YAAa;QAAAT,QAAA,EAAEZ;MAAI;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/CvB,OAAA;QAAGc,SAAS,EAAEhB,MAAM,CAACO,IAAK;QAAAW,QAAA,EAAEX;MAAI;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAErCvB,OAAA;QAAKc,SAAS,EAAEhB,MAAM,CAACa,QAAS;QAAAK,QAAA,GAC7BL,QAAQ,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACvC7B,OAAA;UAAkBc,SAAS,EAAEhB,MAAM,CAACgC,UAAW;UAAAd,QAAA,EAC5CY;QAAO,GADCC,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACP,CAAC,EACDZ,QAAQ,CAACoB,MAAM,GAAG,CAAC,iBAClB/B,OAAA;UAAMc,SAAS,EAAEhB,MAAM,CAACkC,YAAa;UAAAhB,QAAA,GAAC,GAAC,EAACL,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAC,OAAK;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENvB,OAAA;QAAKc,SAAS,EAAEhB,MAAM,CAACmC,YAAa;QAAAjB,QAAA,gBAClChB,OAAA;UAAKc,SAAS,EAAEhB,MAAM,CAACoC,kBAAmB;UAAAlB,QAAA,gBACxChB,OAAA;YAAAgB,QAAA,EAAM;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BvB,OAAA;YAAMc,SAAS,EAAEhB,MAAM,CAACqC,iBAAkB;YAAAnB,QAAA,GACvCN,aAAa,EAAC,GAAC,EAACD,SAAS;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvB,OAAA;UAAKc,SAAS,EAAEhB,MAAM,CAACsC,WAAY;UAAApB,QAAA,eACjChB,OAAA;YACEc,SAAS,EAAEhB,MAAM,CAACuC,YAAa;YAC/BC,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAG3B,sBAAsB;YAAI;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvB,OAAA;QAAKc,SAAS,EAAEhB,MAAM,CAAC0C,OAAQ;QAAAxB,QAAA,gBAC7BhB,OAAA;UAAMc,SAAS,EAAEhB,MAAM,CAAC2C,UAAW;UAAAzB,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDvB,OAAA;UAAMc,SAAS,EAAEhB,MAAM,CAAC4C,KAAM;UAAA1B,QAAA,GAAC,QAAC,EAACT,WAAW,CAACoC,cAAc,CAAC,CAAC,EAAC,MAAI;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eAENvB,OAAA;QAAKc,SAAS,EAAEhB,MAAM,CAAC8C,OAAQ;QAAA5B,QAAA,gBAC7BhB,OAAA,CAACH,IAAI;UAACgD,EAAE,EAAE,aAAa1C,EAAE,EAAG;UAACW,SAAS,EAAE,iBAAiBhB,MAAM,CAACgD,UAAU,EAAG;UAAA9B,QAAA,EAAC;QAE9E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPvB,OAAA,CAACH,IAAI;UAACgD,EAAE,EAAE,kBAAkB1C,EAAE,EAAG;UAACW,SAAS,EAAE,cAAchB,MAAM,CAACiD,OAAO,EAAG;UAAA/B,QAAA,EAAC;QAE7E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACyB,EAAA,GA1EI/C,YAAY;AA4ElB,eAAeA,YAAY;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}