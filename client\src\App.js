import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Home from './pages/Home';
import HospitalDetails from './pages/HospitalDetails';
import BookAppointment from './pages/BookAppointment';
import Login from './pages/Login';
import Register from './pages/Register';
import AdminDashboard from './pages/AdminDashboard';
import DoctorDashboard from './pages/DoctorDashboard';
import ReceptionDashboard from './pages/ReceptionDashboard';
import HospitalDashboard from './pages/HospitalDashboard';
import HospitalListing from './pages/HospitalListing';
import styles from './App.module.css';

function App() {
  return (
    <Router>
      <div className={styles.app}>
        <Navbar />
        <main className={styles.main}>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/hospitals" element={<HospitalListing />} />
            <Route path="/hospital/:id" element={<HospitalDetails />} />
            <Route path="/book" element={<BookAppointment />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/admin" element={<AdminDashboard />} />
            <Route path="/doctor" element={<DoctorDashboard />} />
            <Route path="/reception" element={<ReceptionDashboard />} />
            <Route path="/hospital-dashboard" element={<HospitalDashboard />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
