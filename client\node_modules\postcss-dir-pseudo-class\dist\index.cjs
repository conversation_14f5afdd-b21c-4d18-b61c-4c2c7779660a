"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-selector-parser"));function r(e){const r=Object(e).dir,o=Boolean(Object(e).preserve),s=Boolean(Object(e).shadow);return{postcssPlugin:"postcss-dir-pseudo-class",Rule(e,{result:a}){let n,l=!1;if(e.selector.toLowerCase().includes(":dir(")){try{n=t.default((o=>{o.nodes.forEach((o=>{o.walk((o=>{if("pseudo"!==o.type)return;if(":dir"!==o.value.toLowerCase())return;if(!o.nodes||!o.nodes.length)return;const n=o.nodes.toString().toLowerCase();if("rtl"!==n&&"ltr"!==n)return;const u=o.parent;u.nodes.filter((e=>"pseudo"===e.type&&":dir"===e.value.toLowerCase())).length>1&&!l&&(l=!0,e.warn(a,`Hierarchical :dir pseudo class usage can't be transformed correctly to [dir] attributes. This will lead to incorrect selectors for "${e.selector}"`));const c=o.prev(),d=o.next(),i=c&&c.type&&"combinator"!==c.type,p=d&&d.type&&"combinator"!==d.type,f=d&&d.type&&("combinator"!==d.type||"combinator"===d.type&&" "===d.value);i||p||0===u.nodes.indexOf(o)&&f||1===u.nodes.length?o.remove():o.replaceWith(t.default.universal());const v=u.nodes[0],b=v&&"combinator"===v.type&&" "===v.value,y=v&&"tag"===v.type&&"html"===v.value.toLowerCase(),h=v&&"pseudo"===v.type&&":root"===v.value.toLowerCase();!v||y||h||b||u.prepend(t.default.combinator({value:" "}));const m=r===n,w=t.default.attribute({attribute:"dir",operator:"=",quoteMark:'"',value:`"${n}"`}),g=t.default.pseudo({value:":host-context"});g.append(w);const C=t.default.pseudo({value:(y||h?"":"html")+":not"});C.append(t.default.attribute({attribute:"dir",operator:"=",quoteMark:'"',value:`"${"ltr"===n?"rtl":"ltr"}"`})),m?y?u.insertAfter(v,C):u.prepend(C):y?u.insertAfter(v,w):s&&!h?u.prepend(g):u.prepend(w)}))}))})).processSync(e.selector)}catch(t){return void e.warn(a,`Failed to parse selector : ${e.selector}`)}void 0!==n&&n!==e.selector&&(e.cloneBefore({selector:n}),o||e.remove())}}}}r.postcss=!0,module.exports=r;
