{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styles from './AdminDashboard.module.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('overview');\n  const stats = [{\n    title: 'Total Hospitals',\n    value: '12',\n    change: '+2 from last month',\n    icon: '🏥'\n  }, {\n    title: 'Total Users',\n    value: '1,234',\n    change: '+180 from last month',\n    icon: '👥'\n  }, {\n    title: 'Total Appointments',\n    value: '5,678',\n    change: '+12% from last month',\n    icon: '📅'\n  }, {\n    title: 'Revenue',\n    value: '₹45,231',\n    change: '+20.1% from last month',\n    icon: '💰'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.sidebar,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.sidebarHeader,\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83C\\uDFE5 Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: styles.sidebarNav,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'overview' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('overview'),\n          children: \"\\uD83D\\uDCCA Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'hospitals' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('hospitals'),\n          children: \"\\uD83C\\uDFE5 Hospitals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'users' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('users'),\n          children: \"\\uD83D\\uDC65 Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'appointments' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('appointments'),\n          children: \"\\uD83D\\uDCC5 Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'settings' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('settings'),\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.mainContent,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.header,\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.headerActions,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-accent\",\n            children: \"\\u2795 Add Hospital\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"\\uD83D\\uDC64 Add User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.statsGrid,\n          children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statIcon,\n              children: stat.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statContent,\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: stat.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.statValue,\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.statChange,\n                children: stat.change\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.recentActivities,\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Recent Activities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.activitiesList,\n            children: [{\n              action: 'New appointment booked',\n              user: 'John Doe',\n              time: '2 minutes ago',\n              type: 'appointment'\n            }, {\n              action: 'Doctor updated availability',\n              user: 'Dr. Sarah Johnson',\n              time: '15 minutes ago',\n              type: 'doctor'\n            }, {\n              action: 'New hospital added',\n              user: 'Admin',\n              time: '1 hour ago',\n              type: 'hospital'\n            }, {\n              action: 'Patient registration',\n              user: 'Jane Smith',\n              time: '2 hours ago',\n              type: 'user'\n            }].map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.activityItem,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.activityIcon,\n                children: [activity.type === 'appointment' && '📅', activity.type === 'doctor' && '👩‍⚕️', activity.type === 'hospital' && '🏥', activity.type === 'user' && '👤']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.activityContent,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: styles.activityAction,\n                  children: activity.action\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: styles.activityMeta,\n                  children: [\"by \", activity.user, \" \\u2022 \", activity.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), activeTab === 'hospitals' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.tableContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Manage Hospitals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.table,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableHeader,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Hospital Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"City\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Beds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this), [{\n            name: 'Apollo Hospital',\n            city: 'Delhi',\n            beds: 500,\n            status: 'Active'\n          }, {\n            name: 'Fortis Hospital',\n            city: 'Bangalore',\n            beds: 400,\n            status: 'Active'\n          }, {\n            name: 'Max Healthcare',\n            city: 'Mumbai',\n            beds: 350,\n            status: 'Pending'\n          }, {\n            name: 'AIIMS',\n            city: 'New Delhi',\n            beds: 2500,\n            status: 'Active'\n          }].map((hospital, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableRow,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: hospital.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: hospital.city\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: hospital.beds\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `${styles.statusBadge} ${hospital.status === 'Active' ? styles.statusActive : styles.statusPending}`,\n                children: hospital.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.tableActions,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary\",\n                children: \"Edit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-accent\",\n                children: \"View\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.tableContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Manage Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.table,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableHeader,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Hospital\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), [{\n            name: 'Dr. Meena Sharma',\n            role: 'Doctor',\n            hospital: 'Apollo Hospital',\n            status: 'Active'\n          }, {\n            name: 'John Doe',\n            role: 'Patient',\n            hospital: '-',\n            status: 'Active'\n          }, {\n            name: 'Sarah Wilson',\n            role: 'Receptionist',\n            hospital: 'Fortis Hospital',\n            status: 'Active'\n          }, {\n            name: 'Dr. Arjun Rao',\n            role: 'Doctor',\n            hospital: 'Max Healthcare',\n            status: 'Pending'\n          }].map((user, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableRow,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: user.hospital\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `${styles.statusBadge} ${user.status === 'Active' ? styles.statusActive : styles.statusPending}`,\n                children: user.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.tableActions,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary\",\n                children: \"Edit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-accent\",\n                children: \"View\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "_s", "activeTab", "setActiveTab", "stats", "title", "value", "change", "icon", "className", "container", "children", "sidebar", "sidebarHeader", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sidebarNav", "navItem", "navItemActive", "onClick", "mainContent", "header", "headerActions", "statsGrid", "map", "stat", "index", "statCard", "statIcon", "statContent", "statValue", "statChange", "recentActivities", "activitiesList", "action", "user", "time", "type", "activity", "activityItem", "activityIcon", "activityContent", "activityAction", "activityMeta", "tableContainer", "table", "tableHeader", "name", "city", "beds", "status", "hospital", "tableRow", "statusBadge", "statusActive", "statusPending", "tableActions", "role", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styles from './AdminDashboard.module.css';\n\nconst AdminDashboard = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const stats = [\n    { title: 'Total Hospitals', value: '12', change: '+2 from last month', icon: '🏥' },\n    { title: 'Total Users', value: '1,234', change: '+180 from last month', icon: '👥' },\n    { title: 'Total Appointments', value: '5,678', change: '+12% from last month', icon: '📅' },\n    { title: 'Revenue', value: '₹45,231', change: '+20.1% from last month', icon: '💰' }\n  ];\n\n  return (\n    <div className={styles.container}>\n      {/* Sidebar */}\n      <div className={styles.sidebar}>\n        <div className={styles.sidebarHeader}>\n          <h2>🏥 Admin Panel</h2>\n        </div>\n        <nav className={styles.sidebarNav}>\n          <button\n            className={`${styles.navItem} ${activeTab === 'overview' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('overview')}\n          >\n            📊 Overview\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'hospitals' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('hospitals')}\n          >\n            🏥 Hospitals\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'users' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('users')}\n          >\n            👥 Users\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'appointments' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('appointments')}\n          >\n            📅 Appointments\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'settings' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('settings')}\n          >\n            ⚙️ Settings\n          </button>\n        </nav>\n      </div>\n\n      {/* Main Content */}\n      <div className={styles.mainContent}>\n        <div className={styles.header}>\n          <h1>Admin Dashboard</h1>\n          <div className={styles.headerActions}>\n            <button className=\"btn-accent\">\n              ➕ Add Hospital\n            </button>\n            <button className=\"btn-secondary\">\n              👤 Add User\n            </button>\n          </div>\n        </div>\n\n        {activeTab === 'overview' && (\n          <>\n            {/* Stats Cards */}\n            <div className={styles.statsGrid}>\n              {stats.map((stat, index) => (\n                <div key={index} className={styles.statCard}>\n                  <div className={styles.statIcon}>{stat.icon}</div>\n                  <div className={styles.statContent}>\n                    <h3>{stat.title}</h3>\n                    <div className={styles.statValue}>{stat.value}</div>\n                    <div className={styles.statChange}>{stat.change}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Recent Activities */}\n            <div className={styles.recentActivities}>\n              <h2>Recent Activities</h2>\n              <div className={styles.activitiesList}>\n                {[\n                  { action: 'New appointment booked', user: 'John Doe', time: '2 minutes ago', type: 'appointment' },\n                  { action: 'Doctor updated availability', user: 'Dr. Sarah Johnson', time: '15 minutes ago', type: 'doctor' },\n                  { action: 'New hospital added', user: 'Admin', time: '1 hour ago', type: 'hospital' },\n                  { action: 'Patient registration', user: 'Jane Smith', time: '2 hours ago', type: 'user' },\n                ].map((activity, index) => (\n                  <div key={index} className={styles.activityItem}>\n                    <div className={styles.activityIcon}>\n                      {activity.type === 'appointment' && '📅'}\n                      {activity.type === 'doctor' && '👩‍⚕️'}\n                      {activity.type === 'hospital' && '🏥'}\n                      {activity.type === 'user' && '👤'}\n                    </div>\n                    <div className={styles.activityContent}>\n                      <p className={styles.activityAction}>{activity.action}</p>\n                      <p className={styles.activityMeta}>by {activity.user} • {activity.time}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </>\n        )}\n\n        {activeTab === 'hospitals' && (\n          <div className={styles.tableContainer}>\n            <h2>Manage Hospitals</h2>\n            <div className={styles.table}>\n              <div className={styles.tableHeader}>\n                <div>Hospital Name</div>\n                <div>City</div>\n                <div>Beds</div>\n                <div>Status</div>\n                <div>Actions</div>\n              </div>\n              {[\n                { name: 'Apollo Hospital', city: 'Delhi', beds: 500, status: 'Active' },\n                { name: 'Fortis Hospital', city: 'Bangalore', beds: 400, status: 'Active' },\n                { name: 'Max Healthcare', city: 'Mumbai', beds: 350, status: 'Pending' },\n                { name: 'AIIMS', city: 'New Delhi', beds: 2500, status: 'Active' }\n              ].map((hospital, index) => (\n                <div key={index} className={styles.tableRow}>\n                  <div>{hospital.name}</div>\n                  <div>{hospital.city}</div>\n                  <div>{hospital.beds}</div>\n                  <div>\n                    <span className={`${styles.statusBadge} ${hospital.status === 'Active' ? styles.statusActive : styles.statusPending}`}>\n                      {hospital.status}\n                    </span>\n                  </div>\n                  <div className={styles.tableActions}>\n                    <button className=\"btn-secondary\">Edit</button>\n                    <button className=\"btn-accent\">View</button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'users' && (\n          <div className={styles.tableContainer}>\n            <h2>Manage Users</h2>\n            <div className={styles.table}>\n              <div className={styles.tableHeader}>\n                <div>Name</div>\n                <div>Role</div>\n                <div>Hospital</div>\n                <div>Status</div>\n                <div>Actions</div>\n              </div>\n              {[\n                { name: 'Dr. Meena Sharma', role: 'Doctor', hospital: 'Apollo Hospital', status: 'Active' },\n                { name: 'John Doe', role: 'Patient', hospital: '-', status: 'Active' },\n                { name: 'Sarah Wilson', role: 'Receptionist', hospital: 'Fortis Hospital', status: 'Active' },\n                { name: 'Dr. Arjun Rao', role: 'Doctor', hospital: 'Max Healthcare', status: 'Pending' }\n              ].map((user, index) => (\n                <div key={index} className={styles.tableRow}>\n                  <div>{user.name}</div>\n                  <div>{user.role}</div>\n                  <div>{user.hospital}</div>\n                  <div>\n                    <span className={`${styles.statusBadge} ${user.status === 'Active' ? styles.statusActive : styles.statusPending}`}>\n                      {user.status}\n                    </span>\n                  </div>\n                  <div className={styles.tableActions}>\n                    <button className=\"btn-secondary\">Edit</button>\n                    <button className=\"btn-accent\">View</button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,UAAU,CAAC;EAEtD,MAAMU,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE,oBAAoB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnF;IAAEH,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE,OAAO;IAAEC,MAAM,EAAE,sBAAsB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpF;IAAEH,KAAK,EAAE,oBAAoB;IAAEC,KAAK,EAAE,OAAO;IAAEC,MAAM,EAAE,sBAAsB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC3F;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,MAAM,EAAE,wBAAwB;IAAEC,IAAI,EAAE;EAAK,CAAC,CACrF;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAEd,MAAM,CAACe,SAAU;IAAAC,QAAA,gBAE/Bd,OAAA;MAAKY,SAAS,EAAEd,MAAM,CAACiB,OAAQ;MAAAD,QAAA,gBAC7Bd,OAAA;QAAKY,SAAS,EAAEd,MAAM,CAACkB,aAAc;QAAAF,QAAA,eACnCd,OAAA;UAAAc,QAAA,EAAI;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACNpB,OAAA;QAAKY,SAAS,EAAEd,MAAM,CAACuB,UAAW;QAAAP,QAAA,gBAChCd,OAAA;UACEY,SAAS,EAAE,GAAGd,MAAM,CAACwB,OAAO,IAAIjB,SAAS,KAAK,UAAU,GAAGP,MAAM,CAACyB,aAAa,GAAG,EAAE,EAAG;UACvFC,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAAC,UAAU,CAAE;UAAAQ,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpB,OAAA;UACEY,SAAS,EAAE,GAAGd,MAAM,CAACwB,OAAO,IAAIjB,SAAS,KAAK,WAAW,GAAGP,MAAM,CAACyB,aAAa,GAAG,EAAE,EAAG;UACxFC,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAAC,WAAW,CAAE;UAAAQ,QAAA,EAC1C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpB,OAAA;UACEY,SAAS,EAAE,GAAGd,MAAM,CAACwB,OAAO,IAAIjB,SAAS,KAAK,OAAO,GAAGP,MAAM,CAACyB,aAAa,GAAG,EAAE,EAAG;UACpFC,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAAC,OAAO,CAAE;UAAAQ,QAAA,EACtC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpB,OAAA;UACEY,SAAS,EAAE,GAAGd,MAAM,CAACwB,OAAO,IAAIjB,SAAS,KAAK,cAAc,GAAGP,MAAM,CAACyB,aAAa,GAAG,EAAE,EAAG;UAC3FC,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAAC,cAAc,CAAE;UAAAQ,QAAA,EAC7C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpB,OAAA;UACEY,SAAS,EAAE,GAAGd,MAAM,CAACwB,OAAO,IAAIjB,SAAS,KAAK,UAAU,GAAGP,MAAM,CAACyB,aAAa,GAAG,EAAE,EAAG;UACvFC,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAAC,UAAU,CAAE;UAAAQ,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpB,OAAA;MAAKY,SAAS,EAAEd,MAAM,CAAC2B,WAAY;MAAAX,QAAA,gBACjCd,OAAA;QAAKY,SAAS,EAAEd,MAAM,CAAC4B,MAAO;QAAAZ,QAAA,gBAC5Bd,OAAA;UAAAc,QAAA,EAAI;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBpB,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAAC6B,aAAc;UAAAb,QAAA,gBACnCd,OAAA;YAAQY,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAE/B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA;YAAQY,SAAS,EAAC,eAAe;YAAAE,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELf,SAAS,KAAK,UAAU,iBACvBL,OAAA,CAAAE,SAAA;QAAAY,QAAA,gBAEEd,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAAC8B,SAAU;UAAAd,QAAA,EAC9BP,KAAK,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB/B,OAAA;YAAiBY,SAAS,EAAEd,MAAM,CAACkC,QAAS;YAAAlB,QAAA,gBAC1Cd,OAAA;cAAKY,SAAS,EAAEd,MAAM,CAACmC,QAAS;cAAAnB,QAAA,EAAEgB,IAAI,CAACnB;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDpB,OAAA;cAAKY,SAAS,EAAEd,MAAM,CAACoC,WAAY;cAAApB,QAAA,gBACjCd,OAAA;gBAAAc,QAAA,EAAKgB,IAAI,CAACtB;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBpB,OAAA;gBAAKY,SAAS,EAAEd,MAAM,CAACqC,SAAU;gBAAArB,QAAA,EAAEgB,IAAI,CAACrB;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDpB,OAAA;gBAAKY,SAAS,EAAEd,MAAM,CAACsC,UAAW;gBAAAtB,QAAA,EAAEgB,IAAI,CAACpB;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA,GANEW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpB,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAACuC,gBAAiB;UAAAvB,QAAA,gBACtCd,OAAA;YAAAc,QAAA,EAAI;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BpB,OAAA;YAAKY,SAAS,EAAEd,MAAM,CAACwC,cAAe;YAAAxB,QAAA,EACnC,CACC;cAAEyB,MAAM,EAAE,wBAAwB;cAAEC,IAAI,EAAE,UAAU;cAAEC,IAAI,EAAE,eAAe;cAAEC,IAAI,EAAE;YAAc,CAAC,EAClG;cAAEH,MAAM,EAAE,6BAA6B;cAAEC,IAAI,EAAE,mBAAmB;cAAEC,IAAI,EAAE,gBAAgB;cAAEC,IAAI,EAAE;YAAS,CAAC,EAC5G;cAAEH,MAAM,EAAE,oBAAoB;cAAEC,IAAI,EAAE,OAAO;cAAEC,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE;YAAW,CAAC,EACrF;cAAEH,MAAM,EAAE,sBAAsB;cAAEC,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE,aAAa;cAAEC,IAAI,EAAE;YAAO,CAAC,CAC1F,CAACb,GAAG,CAAC,CAACc,QAAQ,EAAEZ,KAAK,kBACpB/B,OAAA;cAAiBY,SAAS,EAAEd,MAAM,CAAC8C,YAAa;cAAA9B,QAAA,gBAC9Cd,OAAA;gBAAKY,SAAS,EAAEd,MAAM,CAAC+C,YAAa;gBAAA/B,QAAA,GACjC6B,QAAQ,CAACD,IAAI,KAAK,aAAa,IAAI,IAAI,EACvCC,QAAQ,CAACD,IAAI,KAAK,QAAQ,IAAI,OAAO,EACrCC,QAAQ,CAACD,IAAI,KAAK,UAAU,IAAI,IAAI,EACpCC,QAAQ,CAACD,IAAI,KAAK,MAAM,IAAI,IAAI;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNpB,OAAA;gBAAKY,SAAS,EAAEd,MAAM,CAACgD,eAAgB;gBAAAhC,QAAA,gBACrCd,OAAA;kBAAGY,SAAS,EAAEd,MAAM,CAACiD,cAAe;kBAAAjC,QAAA,EAAE6B,QAAQ,CAACJ;gBAAM;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1DpB,OAAA;kBAAGY,SAAS,EAAEd,MAAM,CAACkD,YAAa;kBAAAlC,QAAA,GAAC,KAAG,EAAC6B,QAAQ,CAACH,IAAI,EAAC,UAAG,EAACG,QAAQ,CAACF,IAAI;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC;YAAA,GAVEW,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH,EAEAf,SAAS,KAAK,WAAW,iBACxBL,OAAA;QAAKY,SAAS,EAAEd,MAAM,CAACmD,cAAe;QAAAnC,QAAA,gBACpCd,OAAA;UAAAc,QAAA,EAAI;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBpB,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAACoD,KAAM;UAAApC,QAAA,gBAC3Bd,OAAA;YAAKY,SAAS,EAAEd,MAAM,CAACqD,WAAY;YAAArC,QAAA,gBACjCd,OAAA;cAAAc,QAAA,EAAK;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBpB,OAAA;cAAAc,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfpB,OAAA;cAAAc,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfpB,OAAA;cAAAc,QAAA,EAAK;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjBpB,OAAA;cAAAc,QAAA,EAAK;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACL,CACC;YAAEgC,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAE,OAAO;YAAEC,IAAI,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAS,CAAC,EACvE;YAAEH,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAE,WAAW;YAAEC,IAAI,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAS,CAAC,EAC3E;YAAEH,IAAI,EAAE,gBAAgB;YAAEC,IAAI,EAAE,QAAQ;YAAEC,IAAI,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAU,CAAC,EACxE;YAAEH,IAAI,EAAE,OAAO;YAAEC,IAAI,EAAE,WAAW;YAAEC,IAAI,EAAE,IAAI;YAAEC,MAAM,EAAE;UAAS,CAAC,CACnE,CAAC1B,GAAG,CAAC,CAAC2B,QAAQ,EAAEzB,KAAK,kBACpB/B,OAAA;YAAiBY,SAAS,EAAEd,MAAM,CAAC2D,QAAS;YAAA3C,QAAA,gBAC1Cd,OAAA;cAAAc,QAAA,EAAM0C,QAAQ,CAACJ;YAAI;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BpB,OAAA;cAAAc,QAAA,EAAM0C,QAAQ,CAACH;YAAI;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BpB,OAAA;cAAAc,QAAA,EAAM0C,QAAQ,CAACF;YAAI;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BpB,OAAA;cAAAc,QAAA,eACEd,OAAA;gBAAMY,SAAS,EAAE,GAAGd,MAAM,CAAC4D,WAAW,IAAIF,QAAQ,CAACD,MAAM,KAAK,QAAQ,GAAGzD,MAAM,CAAC6D,YAAY,GAAG7D,MAAM,CAAC8D,aAAa,EAAG;gBAAA9C,QAAA,EACnH0C,QAAQ,CAACD;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpB,OAAA;cAAKY,SAAS,EAAEd,MAAM,CAAC+D,YAAa;cAAA/C,QAAA,gBAClCd,OAAA;gBAAQY,SAAS,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CpB,OAAA;gBAAQY,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA,GAZEW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAf,SAAS,KAAK,OAAO,iBACpBL,OAAA;QAAKY,SAAS,EAAEd,MAAM,CAACmD,cAAe;QAAAnC,QAAA,gBACpCd,OAAA;UAAAc,QAAA,EAAI;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBpB,OAAA;UAAKY,SAAS,EAAEd,MAAM,CAACoD,KAAM;UAAApC,QAAA,gBAC3Bd,OAAA;YAAKY,SAAS,EAAEd,MAAM,CAACqD,WAAY;YAAArC,QAAA,gBACjCd,OAAA;cAAAc,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfpB,OAAA;cAAAc,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfpB,OAAA;cAAAc,QAAA,EAAK;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnBpB,OAAA;cAAAc,QAAA,EAAK;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjBpB,OAAA;cAAAc,QAAA,EAAK;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACL,CACC;YAAEgC,IAAI,EAAE,kBAAkB;YAAEU,IAAI,EAAE,QAAQ;YAAEN,QAAQ,EAAE,iBAAiB;YAAED,MAAM,EAAE;UAAS,CAAC,EAC3F;YAAEH,IAAI,EAAE,UAAU;YAAEU,IAAI,EAAE,SAAS;YAAEN,QAAQ,EAAE,GAAG;YAAED,MAAM,EAAE;UAAS,CAAC,EACtE;YAAEH,IAAI,EAAE,cAAc;YAAEU,IAAI,EAAE,cAAc;YAAEN,QAAQ,EAAE,iBAAiB;YAAED,MAAM,EAAE;UAAS,CAAC,EAC7F;YAAEH,IAAI,EAAE,eAAe;YAAEU,IAAI,EAAE,QAAQ;YAAEN,QAAQ,EAAE,gBAAgB;YAAED,MAAM,EAAE;UAAU,CAAC,CACzF,CAAC1B,GAAG,CAAC,CAACW,IAAI,EAAET,KAAK,kBAChB/B,OAAA;YAAiBY,SAAS,EAAEd,MAAM,CAAC2D,QAAS;YAAA3C,QAAA,gBAC1Cd,OAAA;cAAAc,QAAA,EAAM0B,IAAI,CAACY;YAAI;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtBpB,OAAA;cAAAc,QAAA,EAAM0B,IAAI,CAACsB;YAAI;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtBpB,OAAA;cAAAc,QAAA,EAAM0B,IAAI,CAACgB;YAAQ;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BpB,OAAA;cAAAc,QAAA,eACEd,OAAA;gBAAMY,SAAS,EAAE,GAAGd,MAAM,CAAC4D,WAAW,IAAIlB,IAAI,CAACe,MAAM,KAAK,QAAQ,GAAGzD,MAAM,CAAC6D,YAAY,GAAG7D,MAAM,CAAC8D,aAAa,EAAG;gBAAA9C,QAAA,EAC/G0B,IAAI,CAACe;cAAM;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpB,OAAA;cAAKY,SAAS,EAAEd,MAAM,CAAC+D,YAAa;cAAA/C,QAAA,gBAClCd,OAAA;gBAAQY,SAAS,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CpB,OAAA;gBAAQY,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA,GAZEW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAvLID,cAAc;AAAA4D,EAAA,GAAd5D,cAAc;AAyLpB,eAAeA,cAAc;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}