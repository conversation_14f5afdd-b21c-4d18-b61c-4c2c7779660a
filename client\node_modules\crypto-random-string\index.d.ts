/**
Generate a [cryptographically strong](https://en.m.wikipedia.org/wiki/Strong_cryptography) random string.

@param length - Length of the returned string.
@returns A [`hex`](https://en.wikipedia.org/wiki/Hexadecimal) string.

@example
```
import cryptoRandomString = require('crypto-random-string');

cryptoRandomString(10);
//=> '2cf05d94db'
```
*/
declare function cryptoRandomString(length: number): string;

export = cryptoRandomString;
