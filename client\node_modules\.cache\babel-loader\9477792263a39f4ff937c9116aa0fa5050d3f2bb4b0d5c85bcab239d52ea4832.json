{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 19V5\",\n  key: \"1r845m\"\n}], [\"path\", {\n  d: \"M10 19V6.8\",\n  key: \"9j2tfs\"\n}], [\"path\", {\n  d: \"M14 19v-7.8\",\n  key: \"10s8qv\"\n}], [\"path\", {\n  d: \"M18 5v4\",\n  key: \"1tajlv\"\n}], [\"path\", {\n  d: \"M18 19v-6\",\n  key: \"ielfq3\"\n}], [\"path\", {\n  d: \"M22 19V9\",\n  key: \"158nzp\"\n}], [\"path\", {\n  d: \"M2 19V9a4 4 0 0 1 4-4c2 0 4 1.33 6 4s4 4 6 4a4 4 0 1 0-3-6.65\",\n  key: \"1930oh\"\n}]];\nconst RollerCoaster = createLucideIcon(\"roller-coaster\", __iconNode);\nexport { __iconNode, RollerCoaster as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "RollerCoaster", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\roller-coaster.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 19V5', key: '1r845m' }],\n  ['path', { d: 'M10 19V6.8', key: '9j2tfs' }],\n  ['path', { d: 'M14 19v-7.8', key: '10s8qv' }],\n  ['path', { d: 'M18 5v4', key: '1tajlv' }],\n  ['path', { d: 'M18 19v-6', key: 'ielfq3' }],\n  ['path', { d: 'M22 19V9', key: '158nzp' }],\n  ['path', { d: 'M2 19V9a4 4 0 0 1 4-4c2 0 4 1.33 6 4s4 4 6 4a4 4 0 1 0-3-6.65', key: '1930oh' }],\n];\n\n/**\n * @component @name RollerCoaster\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAxOVY1IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOVY2LjgiIC8+CiAgPHBhdGggZD0iTTE0IDE5di03LjgiIC8+CiAgPHBhdGggZD0iTTE4IDV2NCIgLz4KICA8cGF0aCBkPSJNMTggMTl2LTYiIC8+CiAgPHBhdGggZD0iTTIyIDE5VjkiIC8+CiAgPHBhdGggZD0iTTIgMTlWOWE0IDQgMCAwIDEgNC00YzIgMCA0IDEuMzMgNiA0czQgNCA2IDRhNCA0IDAgMSAwLTMtNi42NSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/roller-coaster\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RollerCoaster = createLucideIcon('roller-coaster', __iconNode);\n\nexport default RollerCoaster;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,+DAAiE;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChG;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,mBAAkBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}