import React from 'react';
import { Link } from 'react-router-dom';
import styles from './HospitalCard.module.css';

const HospitalCard = ({ hospital }) => {
  const {
    id,
    name,
    city,
    image,
    pricePerDay,
    rating,
    totalBeds,
    availableBeds,
    services
  } = hospital;

  const availabilityPercentage = ((availableBeds / totalBeds) * 100).toFixed(0);

  return (
    <div className={styles.card}>
      <div className={styles.imageContainer}>
        <img
          src={image || '/api/placeholder/400/200'}
          alt={name}
          className={styles.image}
        />
        <div className={styles.rating}>
          ⭐ {rating}
        </div>
      </div>

      <div className={styles.content}>
        <h3 className={styles.hospitalName}>{name}</h3>
        <p className={styles.city}>{city}</p>
        
        <div className={styles.services}>
          {services.slice(0, 3).map((service, index) => (
            <span key={index} className={styles.serviceTag}>
              {service}
            </span>
          ))}
          {services.length > 3 && (
            <span className={styles.moreServices}>+{services.length - 3} more</span>
          )}
        </div>

        <div className={styles.availability}>
          <div className={styles.availabilityHeader}>
            <span>Bed Availability</span>
            <span className={styles.availabilityCount}>
              {availableBeds}/{totalBeds}
            </span>
          </div>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${availabilityPercentage}%` }}
            ></div>
          </div>
        </div>

        <div className={styles.pricing}>
          <span className={styles.priceLabel}>Starting from</span>
          <span className={styles.price}>₹{pricePerDay.toLocaleString()}/day</span>
        </div>

        <div className={styles.actions}>
          <Link to={`/hospital/${id}`} className={`btn-secondary ${styles.detailsBtn}`}>
            View Details
          </Link>
          <Link to={`/book?hospital=${id}`} className={`btn-accent ${styles.bookBtn}`}>
            Book Now
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HospitalCard;
