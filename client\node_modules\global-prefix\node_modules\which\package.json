{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "which", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "version": "1.3.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-which.git"}, "main": "which.js", "bin": "./bin/which", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.6.2", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "files": ["which.js", "bin/which"]}