{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear specific error when user types\n    if (errors[e.target.name]) {\n      setErrors({\n        ...errors,\n        [e.target.name]: ''\n      });\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n    return newErrors;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n\n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name\n      }));\n      setIsLoading(false);\n      navigate('/login');\n    }, 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-md space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground\",\n            children: /*#__PURE__*/_jsxDEV(Building2, {\n              className: \"h-8 w-8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-muted-foreground\",\n          children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-primary hover:text-primary/80\",\n            children: \"sign in to your existing account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"Get started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardDescription, {\n            children: \"Create your account to book appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"text-sm font-medium\",\n                children: \"Full Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"name\",\n                name: \"name\",\n                type: \"text\",\n                required: true,\n                value: formData.name,\n                onChange: handleChange,\n                placeholder: \"Enter your full name\",\n                className: errors.name ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"text-sm font-medium\",\n                children: \"Email address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                required: true,\n                value: formData.email,\n                onChange: handleChange,\n                placeholder: \"Enter your email\",\n                className: errors.email ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"phone\",\n                className: \"text-sm font-medium\",\n                children: \"Phone Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"phone\",\n                name: \"phone\",\n                type: \"tel\",\n                required: true,\n                value: formData.phone,\n                onChange: handleChange,\n                placeholder: \"+****************\",\n                className: errors.phone ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"dateOfBirth\",\n                  className: \"text-sm font-medium\",\n                  children: \"Date of Birth\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"dateOfBirth\",\n                  name: \"dateOfBirth\",\n                  type: \"date\",\n                  value: formData.dateOfBirth,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"gender\",\n                  className: \"text-sm font-medium\",\n                  children: \"Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"gender\",\n                  name: \"gender\",\n                  value: formData.gender,\n                  onChange: handleChange,\n                  className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"male\",\n                    children: \"Male\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"female\",\n                    children: \"Female\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"other\",\n                    children: \"Other\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"prefer-not-to-say\",\n                    children: \"Prefer not to say\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"text-sm font-medium\",\n                children: \"Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.password,\n                onChange: handleChange,\n                placeholder: \"Enter your password\",\n                className: errors.password ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"confirmPassword\",\n                className: \"text-sm font-medium\",\n                children: \"Confirm Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                placeholder: \"Confirm your password\",\n                className: errors.confirmPassword ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"role\",\n                className: \"text-sm font-medium\",\n                children: \"Register as\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"role\",\n                name: \"role\",\n                value: formData.role,\n                onChange: handleChange,\n                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"patient\",\n                  children: \"Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"doctor\",\n                  children: \"Doctor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"receptionist\",\n                  children: \"Receptionist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"terms\",\n                name: \"terms\",\n                type: \"checkbox\",\n                required: true,\n                className: \"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"terms\",\n                className: \"text-sm\",\n                children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: \"text-primary hover:text-primary/80\",\n                  children: \"Terms and Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: \"text-primary hover:text-primary/80\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"w-full\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), \"Creating account...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this) : 'Create account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"PW8Qs9IORXp2FcZbLYvkiJt4RU4=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "styles", "jsxDEV", "_jsxDEV", "Register", "_s", "activeTab", "setActiveTab", "formData", "setFormData", "name", "email", "password", "confirmPassword", "phone", "role", "dateOfBirth", "gender", "hospitalName", "address", "city", "state", "pincode", "licenseNumber", "establishedYear", "hospitalType", "totalBeds", "emergencyServices", "ambulanceService", "description", "isLoading", "setIsLoading", "errors", "setErrors", "navigate", "handleChange", "e", "target", "value", "validateForm", "newErrors", "trim", "test", "length", "handleSubmit", "preventDefault", "Object", "keys", "setTimeout", "console", "log", "localStorage", "setItem", "JSON", "stringify", "className", "children", "Building2", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "htmlFor", "Input", "id", "type", "required", "onChange", "placeholder", "autoComplete", "<PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\n\nconst Register = () => {\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear specific error when user types\n    if (errors[e.target.name]) {\n      setErrors({\n        ...errors,\n        [e.target.name]: ''\n      });\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n\n    return newErrors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n      \n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name\n      }));\n\n      setIsLoading(false);\n      navigate('/login');\n    }, 2000);\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4\">\n      <div className=\"w-full max-w-md space-y-8\">\n        <div className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <div className=\"flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground\">\n              <Building2 className=\"h-8 w-8\" />\n            </div>\n          </div>\n          <h2 className=\"text-3xl font-bold\">Create your account</h2>\n          <p className=\"mt-2 text-muted-foreground\">\n            Or{' '}\n            <Link\n              to=\"/login\"\n              className=\"font-medium text-primary hover:text-primary/80\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Get started</CardTitle>\n            <CardDescription>\n              Create your account to book appointments\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label htmlFor=\"name\" className=\"text-sm font-medium\">\n                  Full Name *\n                </label>\n                <Input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={handleChange}\n                  placeholder=\"Enter your full name\"\n                  className={errors.name ? 'border-destructive' : ''}\n                />\n                {errors.name && <p className=\"text-sm text-destructive\">{errors.name}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"email\" className=\"text-sm font-medium\">\n                  Email address *\n                </label>\n                <Input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleChange}\n                  placeholder=\"Enter your email\"\n                  className={errors.email ? 'border-destructive' : ''}\n                />\n                {errors.email && <p className=\"text-sm text-destructive\">{errors.email}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"phone\" className=\"text-sm font-medium\">\n                  Phone Number *\n                </label>\n                <Input\n                  id=\"phone\"\n                  name=\"phone\"\n                  type=\"tel\"\n                  required\n                  value={formData.phone}\n                  onChange={handleChange}\n                  placeholder=\"+****************\"\n                  className={errors.phone ? 'border-destructive' : ''}\n                />\n                {errors.phone && <p className=\"text-sm text-destructive\">{errors.phone}</p>}\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"dateOfBirth\" className=\"text-sm font-medium\">\n                    Date of Birth\n                  </label>\n                  <Input\n                    id=\"dateOfBirth\"\n                    name=\"dateOfBirth\"\n                    type=\"date\"\n                    value={formData.dateOfBirth}\n                    onChange={handleChange}\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"gender\" className=\"text-sm font-medium\">\n                    Gender\n                  </label>\n                  <select\n                    id=\"gender\"\n                    name=\"gender\"\n                    value={formData.gender}\n                    onChange={handleChange}\n                    className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                  >\n                    <option value=\"\">Select</option>\n                    <option value=\"male\">Male</option>\n                    <option value=\"female\">Female</option>\n                    <option value=\"other\">Other</option>\n                    <option value=\"prefer-not-to-say\">Prefer not to say</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"password\" className=\"text-sm font-medium\">\n                  Password *\n                </label>\n                <Input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  placeholder=\"Enter your password\"\n                  className={errors.password ? 'border-destructive' : ''}\n                />\n                {errors.password && <p className=\"text-sm text-destructive\">{errors.password}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"confirmPassword\" className=\"text-sm font-medium\">\n                  Confirm Password *\n                </label>\n                <Input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  placeholder=\"Confirm your password\"\n                  className={errors.confirmPassword ? 'border-destructive' : ''}\n                />\n                {errors.confirmPassword && <p className=\"text-sm text-destructive\">{errors.confirmPassword}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"role\" className=\"text-sm font-medium\">\n                  Register as\n                </label>\n                <select\n                  id=\"role\"\n                  name=\"role\"\n                  value={formData.role}\n                  onChange={handleChange}\n                  className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                >\n                  <option value=\"patient\">Patient</option>\n                  <option value=\"doctor\">Doctor</option>\n                  <option value=\"receptionist\">Receptionist</option>\n                </select>\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  id=\"terms\"\n                  name=\"terms\"\n                  type=\"checkbox\"\n                  required\n                  className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\n                />\n                <label htmlFor=\"terms\" className=\"text-sm\">\n                  I agree to the{' '}\n                  <Link to=\"#\" className=\"text-primary hover:text-primary/80\">\n                    Terms and Conditions\n                  </Link>{' '}\n                  and{' '}\n                  <Link to=\"#\" className=\"text-primary hover:text-primary/80\">\n                    Privacy Policy\n                  </Link>\n                </label>\n              </div>\n\n              <Button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Creating account...\n                  </div>\n                ) : (\n                  'Create account'\n                )}\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvC;IACAY,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,SAAS;IAEf;IACAC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IAEV;IACAC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,iBAAiB,EAAE,KAAK;IACxBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMoC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAE9B,MAAMmC,YAAY,GAAIC,CAAC,IAAK;IAC1B3B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC4B,CAAC,CAACC,MAAM,CAAC3B,IAAI,GAAG0B,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;IACF;IACA,IAAIN,MAAM,CAACI,CAAC,CAACC,MAAM,CAAC3B,IAAI,CAAC,EAAE;MACzBuB,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACI,CAAC,CAACC,MAAM,CAAC3B,IAAI,GAAG;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAChC,QAAQ,CAACE,IAAI,CAAC+B,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAC9B,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAAC8B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC7B,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC+B,IAAI,CAAClC,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/C6B,SAAS,CAAC7B,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtB4B,SAAS,CAAC5B,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAAC+B,MAAM,GAAG,CAAC,EAAE;MACvCH,SAAS,CAAC5B,QAAQ,GAAG,wCAAwC;IAC/D;IAEA,IAAIJ,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClD2B,SAAS,CAAC3B,eAAe,GAAG,wBAAwB;IACtD;IAEA,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC2B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC1B,KAAK,GAAG,0BAA0B;IAC9C;IAEA,OAAO0B,SAAS;EAClB,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAElB,MAAML,SAAS,GAAGD,YAAY,CAAC,CAAC;IAChC,IAAIO,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACrCV,SAAS,CAACO,SAAS,CAAC;MACpB;IACF;IAEAT,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEb;IACAe,UAAU,CAAC,MAAM;MACfC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE1C,QAAQ,CAAC;;MAE3C;MACA2C,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;QAC1C3C,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBI,IAAI,EAAEP,QAAQ,CAACO,IAAI;QACnBL,IAAI,EAAEF,QAAQ,CAACE;MACjB,CAAC,CAAC,CAAC;MAEHqB,YAAY,CAAC,KAAK,CAAC;MACnBG,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACE/B,OAAA;IAAKoD,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnFrD,OAAA;MAAKoD,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCrD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAKoD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCrD,OAAA;YAAKoD,SAAS,EAAC,0FAA0F;YAAAC,QAAA,eACvGrD,OAAA,CAACsD,SAAS;cAACF,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1D,OAAA;UAAIoD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3D1D,OAAA;UAAGoD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,IACtC,EAAC,GAAG,eACNrD,OAAA,CAACJ,IAAI;YACH+D,EAAE,EAAC,QAAQ;YACXP,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAC3D;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN1D,OAAA,CAAC4D,IAAI;QAAAP,QAAA,gBACHrD,OAAA,CAAC6D,UAAU;UAAAR,QAAA,gBACTrD,OAAA,CAAC8D,SAAS;YAAAT,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAClC1D,OAAA,CAAC+D,eAAe;YAAAV,QAAA,EAAC;UAEjB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACb1D,OAAA,CAACgE,WAAW;UAAAX,QAAA,eACVrD,OAAA;YAAMiE,QAAQ,EAAExB,YAAa;YAACW,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDrD,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBAAOkE,OAAO,EAAC,MAAM;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1D,OAAA,CAACmE,KAAK;gBACJC,EAAE,EAAC,MAAM;gBACT7D,IAAI,EAAC,MAAM;gBACX8D,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACRnC,KAAK,EAAE9B,QAAQ,CAACE,IAAK;gBACrBgE,QAAQ,EAAEvC,YAAa;gBACvBwC,WAAW,EAAC,sBAAsB;gBAClCpB,SAAS,EAAEvB,MAAM,CAACtB,IAAI,GAAG,oBAAoB,GAAG;cAAG;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EACD7B,MAAM,CAACtB,IAAI,iBAAIP,OAAA;gBAAGoD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExB,MAAM,CAACtB;cAAI;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEN1D,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBAAOkE,OAAO,EAAC,OAAO;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEvD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1D,OAAA,CAACmE,KAAK;gBACJC,EAAE,EAAC,OAAO;gBACV7D,IAAI,EAAC,OAAO;gBACZ8D,IAAI,EAAC,OAAO;gBACZI,YAAY,EAAC,OAAO;gBACpBH,QAAQ;gBACRnC,KAAK,EAAE9B,QAAQ,CAACG,KAAM;gBACtB+D,QAAQ,EAAEvC,YAAa;gBACvBwC,WAAW,EAAC,kBAAkB;gBAC9BpB,SAAS,EAAEvB,MAAM,CAACrB,KAAK,GAAG,oBAAoB,GAAG;cAAG;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACD7B,MAAM,CAACrB,KAAK,iBAAIR,OAAA;gBAAGoD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExB,MAAM,CAACrB;cAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAEN1D,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBAAOkE,OAAO,EAAC,OAAO;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEvD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1D,OAAA,CAACmE,KAAK;gBACJC,EAAE,EAAC,OAAO;gBACV7D,IAAI,EAAC,OAAO;gBACZ8D,IAAI,EAAC,KAAK;gBACVC,QAAQ;gBACRnC,KAAK,EAAE9B,QAAQ,CAACM,KAAM;gBACtB4D,QAAQ,EAAEvC,YAAa;gBACvBwC,WAAW,EAAC,mBAAmB;gBAC/BpB,SAAS,EAAEvB,MAAM,CAAClB,KAAK,GAAG,oBAAoB,GAAG;cAAG;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACD7B,MAAM,CAAClB,KAAK,iBAAIX,OAAA;gBAAGoD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExB,MAAM,CAAClB;cAAK;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAEN1D,OAAA;cAAKoD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCrD,OAAA;gBAAKoD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBrD,OAAA;kBAAOkE,OAAO,EAAC,aAAa;kBAACd,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAE7D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1D,OAAA,CAACmE,KAAK;kBACJC,EAAE,EAAC,aAAa;kBAChB7D,IAAI,EAAC,aAAa;kBAClB8D,IAAI,EAAC,MAAM;kBACXlC,KAAK,EAAE9B,QAAQ,CAACQ,WAAY;kBAC5B0D,QAAQ,EAAEvC;gBAAa;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1D,OAAA;gBAAKoD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBrD,OAAA;kBAAOkE,OAAO,EAAC,QAAQ;kBAACd,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAExD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR1D,OAAA;kBACEoE,EAAE,EAAC,QAAQ;kBACX7D,IAAI,EAAC,QAAQ;kBACb4B,KAAK,EAAE9B,QAAQ,CAACS,MAAO;kBACvByD,QAAQ,EAAEvC,YAAa;kBACvBoB,SAAS,EAAC,4MAA4M;kBAAAC,QAAA,gBAEtNrD,OAAA;oBAAQmC,KAAK,EAAC,EAAE;oBAAAkB,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChC1D,OAAA;oBAAQmC,KAAK,EAAC,MAAM;oBAAAkB,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC1D,OAAA;oBAAQmC,KAAK,EAAC,QAAQ;oBAAAkB,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC1D,OAAA;oBAAQmC,KAAK,EAAC,OAAO;oBAAAkB,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC1D,OAAA;oBAAQmC,KAAK,EAAC,mBAAmB;oBAAAkB,QAAA,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1D,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBAAOkE,OAAO,EAAC,UAAU;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1D,OAAA,CAACmE,KAAK;gBACJC,EAAE,EAAC,UAAU;gBACb7D,IAAI,EAAC,UAAU;gBACf8D,IAAI,EAAC,UAAU;gBACfI,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRnC,KAAK,EAAE9B,QAAQ,CAACI,QAAS;gBACzB8D,QAAQ,EAAEvC,YAAa;gBACvBwC,WAAW,EAAC,qBAAqB;gBACjCpB,SAAS,EAAEvB,MAAM,CAACpB,QAAQ,GAAG,oBAAoB,GAAG;cAAG;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,EACD7B,MAAM,CAACpB,QAAQ,iBAAIT,OAAA;gBAAGoD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExB,MAAM,CAACpB;cAAQ;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eAEN1D,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBAAOkE,OAAO,EAAC,iBAAiB;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEjE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1D,OAAA,CAACmE,KAAK;gBACJC,EAAE,EAAC,iBAAiB;gBACpB7D,IAAI,EAAC,iBAAiB;gBACtB8D,IAAI,EAAC,UAAU;gBACfI,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRnC,KAAK,EAAE9B,QAAQ,CAACK,eAAgB;gBAChC6D,QAAQ,EAAEvC,YAAa;gBACvBwC,WAAW,EAAC,uBAAuB;gBACnCpB,SAAS,EAAEvB,MAAM,CAACnB,eAAe,GAAG,oBAAoB,GAAG;cAAG;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EACD7B,MAAM,CAACnB,eAAe,iBAAIV,OAAA;gBAAGoD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExB,MAAM,CAACnB;cAAe;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC,eAEN1D,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBAAOkE,OAAO,EAAC,MAAM;gBAACd,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1D,OAAA;gBACEoE,EAAE,EAAC,MAAM;gBACT7D,IAAI,EAAC,MAAM;gBACX4B,KAAK,EAAE9B,QAAQ,CAACO,IAAK;gBACrB2D,QAAQ,EAAEvC,YAAa;gBACvBoB,SAAS,EAAC,4MAA4M;gBAAAC,QAAA,gBAEtNrD,OAAA;kBAAQmC,KAAK,EAAC,SAAS;kBAAAkB,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC1D,OAAA;kBAAQmC,KAAK,EAAC,QAAQ;kBAAAkB,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC1D,OAAA;kBAAQmC,KAAK,EAAC,cAAc;kBAAAkB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1D,OAAA;cAAKoD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CrD,OAAA;gBACEoE,EAAE,EAAC,OAAO;gBACV7D,IAAI,EAAC,OAAO;gBACZ8D,IAAI,EAAC,UAAU;gBACfC,QAAQ;gBACRlB,SAAS,EAAC;cAAiE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACF1D,OAAA;gBAAOkE,OAAO,EAAC,OAAO;gBAACd,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAC,gBAC3B,EAAC,GAAG,eAClBrD,OAAA,CAACJ,IAAI;kBAAC+D,EAAE,EAAC,GAAG;kBAACP,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAE5D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACP1D,OAAA,CAACJ,IAAI;kBAAC+D,EAAE,EAAC,GAAG;kBAACP,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAE5D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN1D,OAAA,CAAC0E,MAAM;cACLL,IAAI,EAAC,QAAQ;cACbM,QAAQ,EAAEhD,SAAU;cACpByB,SAAS,EAAC,QAAQ;cAAAC,QAAA,EAEjB1B,SAAS,gBACR3B,OAAA;gBAAKoD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCrD,OAAA;kBAAKoD,SAAS,EAAC;gBAAgE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uBAExF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxD,EAAA,CA1TID,QAAQ;EAAA,QA+BKJ,WAAW;AAAA;AAAA+E,EAAA,GA/BxB3E,QAAQ;AA4Td,eAAeA,QAAQ;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}