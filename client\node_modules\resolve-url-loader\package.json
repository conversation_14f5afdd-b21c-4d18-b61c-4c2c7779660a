{"name": "resolve-url-loader", "version": "4.0.0", "description": "Webpack loader that resolves relative paths in url() statements based on the original source file", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/bholloway/resolve-url-loader.git", "directory": "packages/resolve-url-loader"}, "keywords": ["webpack", "loader", "css", "normalize", "rewrite", "resolve", "url", "sass", "relative", "file"], "author": "bholloway", "license": "MIT", "bugs": {"url": "https://github.com/bholloway/resolve-url-loader/issues"}, "homepage": "https://github.com/bholloway/resolve-url-loader/tree/v4-maintenance/packages/resolve-url-loader", "engines": {"node": ">=8.9"}, "files": ["index.js", "lib/**/+([a-z-]).js", "docs/**/*.*"], "dependencies": {"adjust-sourcemap-loader": "^4.0.0", "convert-source-map": "^1.7.0", "loader-utils": "^2.0.0", "postcss": "^7.0.35", "source-map": "0.6.1"}, "peerDependencies": {"rework": "1.0.1", "rework-visit": "1.0.0"}, "peerDependenciesMeta": {"rework": {"optional": true}, "rework-visit": {"optional": true}}}