[{"D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js": "1", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js": "2", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js": "3", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.jsx": "4", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.jsx": "5", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx": "6", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx": "7", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.jsx": "8", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.jsx": "9", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx": "10", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.jsx": "11", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx": "12", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx": "13", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx": "14", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx": "15", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx": "16", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js": "17"}, {"size": 535, "mtime": 1748889134847, "results": "18", "hashOfConfig": "19"}, {"size": 362, "mtime": 1748889135091, "results": "20", "hashOfConfig": "19"}, {"size": 1320, "mtime": 1748889334299, "results": "21", "hashOfConfig": "19"}, {"size": 7489, "mtime": 1748889407559, "results": "22", "hashOfConfig": "19"}, {"size": 5748, "mtime": 1748889461899, "results": "23", "hashOfConfig": "19"}, {"size": 3654, "mtime": 1748889476752, "results": "24", "hashOfConfig": "19"}, {"size": 6913, "mtime": 1748889519476, "results": "25", "hashOfConfig": "19"}, {"size": 10694, "mtime": 1748889438305, "results": "26", "hashOfConfig": "19"}, {"size": 9708, "mtime": 1748889384424, "results": "27", "hashOfConfig": "19"}, {"size": 4962, "mtime": 1748889496416, "results": "28", "hashOfConfig": "19"}, {"size": 8634, "mtime": 1748889548950, "results": "29", "hashOfConfig": "19"}, {"size": 3331, "mtime": 1748889353238, "results": "30", "hashOfConfig": "19"}, {"size": 1615, "mtime": 1748889298317, "results": "31", "hashOfConfig": "19"}, {"size": 982, "mtime": 1748889322848, "results": "32", "hashOfConfig": "19"}, {"size": 1499, "mtime": 1748889307774, "results": "33", "hashOfConfig": "19"}, {"size": 684, "mtime": 1748889315423, "results": "34", "hashOfConfig": "19"}, {"size": 135, "mtime": 1748889288129, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6uui9y", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.jsx", ["87", "88"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx", ["89"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.jsx", ["90", "91"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx", ["92", "93"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx", ["94"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js", [], [], {"ruleId": "95", "severity": 1, "message": "96", "line": 6, "column": 31, "nodeType": "97", "messageId": "98", "endLine": 6, "endColumn": 34}, {"ruleId": "95", "severity": 1, "message": "99", "line": 9, "column": 11, "nodeType": "97", "messageId": "98", "endLine": 9, "endColumn": 13}, {"ruleId": "95", "severity": 1, "message": "100", "line": 5, "column": 20, "nodeType": "97", "messageId": "98", "endLine": 5, "endColumn": 25}, {"ruleId": "101", "severity": 1, "message": "102", "line": 70, "column": 6, "nodeType": "103", "endLine": 70, "endColumn": 8, "suggestions": "104"}, {"ruleId": "105", "severity": 2, "message": "106", "line": 232, "column": 18, "nodeType": "107", "messageId": "108", "endLine": 232, "endColumn": 27}, {"ruleId": "95", "severity": 1, "message": "109", "line": 4, "column": 10, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 15}, {"ruleId": "95", "severity": 1, "message": "110", "line": 9, "column": 3, "nodeType": "97", "messageId": "98", "endLine": 9, "endColumn": 13}, {"ruleId": "111", "severity": 1, "message": "112", "line": 27, "column": 3, "nodeType": "113", "endLine": 34, "endColumn": 5}, "no-unused-vars", "'Bed' is defined but never used.", "Identifier", "unusedVar", "'id' is assigned a value but never used.", "'Clock' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockHospitals'. Either include it or remove the dependency array.", "ArrayExpression", ["114"], "react/jsx-no-undef", "'Building2' is not defined.", "JSXIdentifier", "undefined", "'Badge' is defined but never used.", "'TrendingUp' is defined but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", {"desc": "115", "fix": "116"}, "Update the dependencies array to be: [mockHospitals]", {"range": "117", "text": "118"}, [1902, 1904], "[mockHospitals]"]