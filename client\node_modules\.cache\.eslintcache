[{"D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js": "1", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js": "2", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js": "3", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.js": "4", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.js": "5", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.js": "6", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.js": "7", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.js": "8", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx": "9", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx": "10", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx": "11", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx": "12", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\HospitalCard.js": "13", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx": "14", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx": "15", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx": "16", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx": "17", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js": "18", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDashboard.jsx": "19", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalListing.jsx": "20"}, {"size": 535, "mtime": 1748889134847, "results": "21", "hashOfConfig": "22"}, {"size": 1592, "mtime": 1750710112320, "results": "23", "hashOfConfig": "22"}, {"size": 362, "mtime": 1748889135091, "results": "24", "hashOfConfig": "22"}, {"size": 7862, "mtime": 1750710430534, "results": "25", "hashOfConfig": "22"}, {"size": 19161, "mtime": 1750710046887, "results": "26", "hashOfConfig": "22"}, {"size": 5789, "mtime": 1750708173173, "results": "27", "hashOfConfig": "22"}, {"size": 6172, "mtime": 1750708464121, "results": "28", "hashOfConfig": "22"}, {"size": 10170, "mtime": 1750709218231, "results": "29", "hashOfConfig": "22"}, {"size": 7842, "mtime": 1750708623004, "results": "30", "hashOfConfig": "22"}, {"size": 5876, "mtime": 1750708352216, "results": "31", "hashOfConfig": "22"}, {"size": 7353, "mtime": 1750709029190, "results": "32", "hashOfConfig": "22"}, {"size": 2577, "mtime": 1750710379063, "results": "33", "hashOfConfig": "22"}, {"size": 2306, "mtime": 1750706937197, "results": "34", "hashOfConfig": "22"}, {"size": 1615, "mtime": 1748889298317, "results": "35", "hashOfConfig": "22"}, {"size": 684, "mtime": 1748889315423, "results": "36", "hashOfConfig": "22"}, {"size": 1499, "mtime": 1748889307774, "results": "37", "hashOfConfig": "22"}, {"size": 982, "mtime": 1748889322848, "results": "38", "hashOfConfig": "22"}, {"size": 135, "mtime": 1748889288129, "results": "39", "hashOfConfig": "22"}, {"size": 14373, "mtime": 1750710159802, "results": "40", "hashOfConfig": "22"}, {"size": 10215, "mtime": 1750710293556, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6uui9y", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.js", ["102"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\HospitalCard.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx", ["103"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDashboard.jsx", ["104", "105"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalListing.jsx", [], [], {"ruleId": "106", "severity": 1, "message": "107", "line": 2, "column": 10, "nodeType": "108", "messageId": "109", "endLine": 2, "endColumn": 19}, {"ruleId": "110", "severity": 1, "message": "111", "line": 27, "column": 3, "nodeType": "112", "endLine": 34, "endColumn": 5}, {"ruleId": "106", "severity": 1, "message": "113", "line": 79, "column": 9, "nodeType": "108", "messageId": "109", "endLine": 79, "endColumn": 18}, {"ruleId": "106", "severity": 1, "message": "114", "line": 91, "column": 9, "nodeType": "108", "messageId": "109", "endLine": 91, "endColumn": 21}, "no-unused-vars", "'useParams' is defined but never used.", "Identifier", "unusedVar", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", "'addDoctor' is assigned a value but never used.", "'updateDoctor' is assigned a value but never used."]