[{"D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js": "1", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js": "2", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js": "3", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.js": "4", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.js": "5", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.js": "6", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.js": "7", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.js": "8", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx": "9", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx": "10", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx": "11", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx": "12", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\HospitalCard.js": "13", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx": "14", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx": "15", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx": "16", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx": "17", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js": "18"}, {"size": 535, "mtime": 1748889134847, "results": "19", "hashOfConfig": "20"}, {"size": 1327, "mtime": 1750706851941, "results": "21", "hashOfConfig": "20"}, {"size": 362, "mtime": 1748889135091, "results": "22", "hashOfConfig": "20"}, {"size": 7568, "mtime": 1750707338969, "results": "23", "hashOfConfig": "20"}, {"size": 10694, "mtime": 1748889438305, "results": "24", "hashOfConfig": "20"}, {"size": 5748, "mtime": 1748889461899, "results": "25", "hashOfConfig": "20"}, {"size": 7489, "mtime": 1748889407559, "results": "26", "hashOfConfig": "20"}, {"size": 8634, "mtime": 1748889548950, "results": "27", "hashOfConfig": "20"}, {"size": 4895, "mtime": 1750707914764, "results": "28", "hashOfConfig": "20"}, {"size": 3647, "mtime": 1750707933449, "results": "29", "hashOfConfig": "20"}, {"size": 6913, "mtime": 1748889519476, "results": "30", "hashOfConfig": "20"}, {"size": 2463, "mtime": 1750706892375, "results": "31", "hashOfConfig": "20"}, {"size": 2306, "mtime": 1750706937197, "results": "32", "hashOfConfig": "20"}, {"size": 1615, "mtime": 1748889298317, "results": "33", "hashOfConfig": "20"}, {"size": 684, "mtime": 1748889315423, "results": "34", "hashOfConfig": "20"}, {"size": 1499, "mtime": 1748889307774, "results": "35", "hashOfConfig": "20"}, {"size": 982, "mtime": 1748889322848, "results": "36", "hashOfConfig": "20"}, {"size": 135, "mtime": 1748889288129, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6uui9y", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.js", ["92", "93"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\HospitalCard.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx", ["94"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js", [], [], {"ruleId": "95", "severity": 1, "message": "96", "line": 6, "column": 31, "nodeType": "97", "messageId": "98", "endLine": 6, "endColumn": 34}, {"ruleId": "95", "severity": 1, "message": "99", "line": 9, "column": 11, "nodeType": "97", "messageId": "98", "endLine": 9, "endColumn": 13}, {"ruleId": "100", "severity": 1, "message": "101", "line": 27, "column": 3, "nodeType": "102", "endLine": 34, "endColumn": 5}, "no-unused-vars", "'Bed' is defined but never used.", "Identifier", "unusedVar", "'id' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement"]