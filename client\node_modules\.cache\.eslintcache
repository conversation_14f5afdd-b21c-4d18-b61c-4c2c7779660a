[{"D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js": "1", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js": "2", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js": "3", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.js": "4", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.js": "5", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.js": "6", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.js": "7", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.js": "8", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx": "9", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx": "10", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx": "11", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx": "12", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\HospitalCard.js": "13", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx": "14", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx": "15", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx": "16", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx": "17", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js": "18", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDashboard.jsx": "19", "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalListing.jsx": "20"}, {"size": 535, "mtime": 1748889134847, "results": "21", "hashOfConfig": "22"}, {"size": 1592, "mtime": 1750710112320, "results": "23", "hashOfConfig": "22"}, {"size": 362, "mtime": 1748889135091, "results": "24", "hashOfConfig": "22"}, {"size": 7862, "mtime": 1750710430534, "results": "25", "hashOfConfig": "22"}, {"size": 19161, "mtime": 1750710046887, "results": "26", "hashOfConfig": "22"}, {"size": 5789, "mtime": 1750708173173, "results": "27", "hashOfConfig": "22"}, {"size": 6172, "mtime": 1750708464121, "results": "28", "hashOfConfig": "22"}, {"size": 26507, "mtime": 1750711644477, "results": "29", "hashOfConfig": "22"}, {"size": 12765, "mtime": 1750712108084, "results": "30", "hashOfConfig": "22"}, {"size": 5876, "mtime": 1750708352216, "results": "31", "hashOfConfig": "22"}, {"size": 24913, "mtime": 1750711865597, "results": "32", "hashOfConfig": "22"}, {"size": 2577, "mtime": 1750710379063, "results": "33", "hashOfConfig": "22"}, {"size": 2306, "mtime": 1750706937197, "results": "34", "hashOfConfig": "22"}, {"size": 1615, "mtime": 1748889298317, "results": "35", "hashOfConfig": "22"}, {"size": 684, "mtime": 1748889315423, "results": "36", "hashOfConfig": "22"}, {"size": 1499, "mtime": 1748889307774, "results": "37", "hashOfConfig": "22"}, {"size": 982, "mtime": 1748889322848, "results": "38", "hashOfConfig": "22"}, {"size": 135, "mtime": 1748889288129, "results": "39", "hashOfConfig": "22"}, {"size": 30288, "mtime": 1750711379743, "results": "40", "hashOfConfig": "22"}, {"size": 10215, "mtime": 1750710293556, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6uui9y", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WebProject\\HOPE-MEDICS\\client\\src\\index.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\App.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\reportWebVitals.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Home.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Register.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDetails.js", ["102"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\Login.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\ReceptionDashboard.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\AdminDashboard.jsx", ["103", "104", "105", "106", "107", "108", "109"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\BookAppointment.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\DoctorDashboard.jsx", ["110"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\Navbar.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\HospitalCard.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\button.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\input.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\card.jsx", ["111"], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\components\\ui\\badge.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\lib\\utils.js", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalDashboard.jsx", [], [], "D:\\WebProject\\HOPE-MEDICS\\client\\src\\pages\\HospitalListing.jsx", [], [], {"ruleId": "112", "severity": 1, "message": "113", "line": 2, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 2, "endColumn": 19}, {"ruleId": "112", "severity": 1, "message": "116", "line": 6, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 6, "endColumn": 19}, {"ruleId": "112", "severity": 1, "message": "117", "line": 7, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 7, "endColumn": 19}, {"ruleId": "112", "severity": 1, "message": "118", "line": 8, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 8, "endColumn": 22}, {"ruleId": "112", "severity": 1, "message": "119", "line": 51, "column": 9, "nodeType": "114", "messageId": "115", "endLine": 51, "endColumn": 26}, {"ruleId": "112", "severity": 1, "message": "120", "line": 80, "column": 9, "nodeType": "114", "messageId": "115", "endLine": 80, "endColumn": 22}, {"ruleId": "112", "severity": 1, "message": "121", "line": 119, "column": 9, "nodeType": "114", "messageId": "115", "endLine": 119, "endColumn": 25}, {"ruleId": "112", "severity": 1, "message": "122", "line": 126, "column": 9, "nodeType": "114", "messageId": "115", "endLine": 126, "endColumn": 19}, {"ruleId": "112", "severity": 1, "message": "123", "line": 40, "column": 23, "nodeType": "114", "messageId": "115", "endLine": 40, "endColumn": 37}, {"ruleId": "124", "severity": 1, "message": "125", "line": 27, "column": 3, "nodeType": "126", "endLine": 34, "endColumn": 5}, "no-unused-vars", "'useParams' is defined but never used.", "Identifier", "unusedVar", "'showModal' is assigned a value but never used.", "'modalType' is assigned a value but never used.", "'selectedItem' is assigned a value but never used.", "'handleAddHospital' is assigned a value but never used.", "'handleAddUser' is assigned a value but never used.", "'toggleUserStatus' is assigned a value but never used.", "'deleteUser' is assigned a value but never used.", "'setAllPatients' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement"]