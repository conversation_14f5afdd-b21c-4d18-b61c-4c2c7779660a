{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 10h2\",\n  key: \"8sgtl7\"\n}], [\"path\", {\n  d: \"M16 14h2\",\n  key: \"epxaof\"\n}], [\"path\", {\n  d: \"M6.17 15a3 3 0 0 1 5.66 0\",\n  key: \"n6f512\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"yxgjnd\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"5\",\n  width: \"20\",\n  height: \"14\",\n  rx: \"2\",\n  key: \"qneu4z\"\n}]];\nconst IdCard = createLucideIcon(\"id-card\", __iconNode);\nexport { __iconNode, IdCard as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "x", "y", "width", "height", "rx", "IdCard", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\id-card.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 10h2', key: '8sgtl7' }],\n  ['path', { d: 'M16 14h2', key: 'epxaof' }],\n  ['path', { d: 'M6.17 15a3 3 0 0 1 5.66 0', key: 'n6f512' }],\n  ['circle', { cx: '9', cy: '11', r: '2', key: 'yxgjnd' }],\n  ['rect', { x: '2', y: '5', width: '20', height: '14', rx: '2', key: 'qneu4z' }],\n];\n\n/**\n * @component @name IdCard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTBoMiIgLz4KICA8cGF0aCBkPSJNMTYgMTRoMiIgLz4KICA8cGF0aCBkPSJNNi4xNyAxNWEzIDMgMCAwIDEgNS42NiAwIiAvPgogIDxjaXJjbGUgY3g9IjkiIGN5PSIxMSIgcj0iMiIgLz4KICA8cmVjdCB4PSIyIiB5PSI1IiB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/id-card\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst IdCard = createLucideIcon('id-card', __iconNode);\n\nexport default IdCard;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEI,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAR,GAAA,EAAK;AAAA,CAAU,EAChF;AAaM,MAAAS,MAAA,GAASC,gBAAiB,YAAWZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}