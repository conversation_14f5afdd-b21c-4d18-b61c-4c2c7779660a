{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 7V5c0-1.1.9-2 2-2h2\",\n  key: \"adw53z\"\n}], [\"path\", {\n  d: \"M17 3h2c1.1 0 2 .9 2 2v2\",\n  key: \"an4l38\"\n}], [\"path\", {\n  d: \"M21 17v2c0 1.1-.9 2-2 2h-2\",\n  key: \"144t0e\"\n}], [\"path\", {\n  d: \"M7 21H5c-1.1 0-2-.9-2-2v-2\",\n  key: \"rtnfgi\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"5\",\n  x: \"7\",\n  y: \"7\",\n  rx: \"1\",\n  key: \"1eyiv7\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"5\",\n  x: \"10\",\n  y: \"12\",\n  rx: \"1\",\n  key: \"1qlmkx\"\n}]];\nconst Group = createLucideIcon(\"group\", __iconNode);\nexport { __iconNode, Group as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "Group", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\group.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 7V5c0-1.1.9-2 2-2h2', key: 'adw53z' }],\n  ['path', { d: 'M17 3h2c1.1 0 2 .9 2 2v2', key: 'an4l38' }],\n  ['path', { d: 'M21 17v2c0 1.1-.9 2-2 2h-2', key: '144t0e' }],\n  ['path', { d: 'M7 21H5c-1.1 0-2-.9-2-2v-2', key: 'rtnfgi' }],\n  ['rect', { width: '7', height: '5', x: '7', y: '7', rx: '1', key: '1eyiv7' }],\n  ['rect', { width: '7', height: '5', x: '10', y: '12', rx: '1', key: '1qlmkx' }],\n];\n\n/**\n * @component @name Group\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA3VjVjMC0xLjEuOS0yIDItMmgyIiAvPgogIDxwYXRoIGQ9Ik0xNyAzaDJjMS4xIDAgMiAuOSAyIDJ2MiIgLz4KICA8cGF0aCBkPSJNMjEgMTd2MmMwIDEuMS0uOSAyLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNNyAyMUg1Yy0xLjEgMC0yLS45LTItMnYtMiIgLz4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSI1IiB4PSI3IiB5PSI3IiByeD0iMSIgLz4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSI1IiB4PSIxMCIgeT0iMTIiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/group\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Group = createLucideIcon('group', __iconNode);\n\nexport default Group;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAMC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAChF;AAaM,MAAAM,KAAA,GAAQC,gBAAiB,UAAST,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}