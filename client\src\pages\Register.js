import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styles from './Register.module.css';

const Register = () => {
  const [activeTab, setActiveTab] = useState('patient');
  const [formData, setFormData] = useState({
    // Common fields
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    role: 'patient',

    // Patient specific
    dateOfBirth: '',
    gender: '',

    // Hospital specific
    hospitalName: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    licenseNumber: '',
    establishedYear: '',
    hospitalType: '',
    totalBeds: '',
    emergencyServices: false,
    ambulanceService: false,
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
    // Clear specific error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setFormData({
      ...formData,
      role: tab
    });
    setErrors({});
  };

  const validateForm = () => {
    const newErrors = {};

    // Common validations
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    // Hospital specific validations
    if (activeTab === 'hospital') {
      if (!formData.hospitalName.trim()) {
        newErrors.hospitalName = 'Hospital name is required';
      }
      if (!formData.address.trim()) {
        newErrors.address = 'Address is required';
      }
      if (!formData.city.trim()) {
        newErrors.city = 'City is required';
      }
      if (!formData.licenseNumber.trim()) {
        newErrors.licenseNumber = 'License number is required';
      }
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const newErrors = validateForm();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    setErrors({});

    // Mock registration - simulate API call
    setTimeout(() => {
      console.log('Registration data:', formData);

      // Mock successful registration
      localStorage.setItem('user', JSON.stringify({
        email: formData.email,
        role: formData.role,
        name: formData.name,
        hospitalName: formData.hospitalName || null
      }));

      setIsLoading(false);

      // Redirect based on role
      if (activeTab === 'hospital') {
        navigate('/hospital-dashboard');
      } else {
        navigate('/login');
      }
    }, 2000);
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.logo}>
            <span className={styles.logoIcon}>🏥</span>
            <span className={styles.logoText}>Hope Medics</span>
          </div>
          <h1>Join Hope Medics Platform</h1>
          <p>Register as a patient or hospital to get started</p>
        </div>
      </div>

      <div className={styles.mainContent}>
        {/* Tab Navigation */}
        <div className={styles.tabNavigation}>
          <button
            className={`${styles.tabButton} ${activeTab === 'patient' ? styles.tabActive : ''}`}
            onClick={() => handleTabChange('patient')}
          >
            👤 Patient Registration
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'hospital' ? styles.tabActive : ''}`}
            onClick={() => handleTabChange('hospital')}
          >
            🏥 Hospital Registration
          </button>
        </div>

        {/* Registration Form */}
        <div className={styles.formContainer}>
          <form onSubmit={handleSubmit} className={styles.form}>
            {/* Common Fields */}
            <div className={styles.section}>
              <h3>Basic Information</h3>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="name">
                    {activeTab === 'hospital' ? 'Contact Person Name' : 'Full Name'} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder={activeTab === 'hospital' ? 'Enter contact person name' : 'Enter your full name'}
                    className="input"
                    required
                  />
                  {errors.name && <span className={styles.error}>{errors.name}</span>}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter email address"
                    className="input"
                    required
                  />
                  {errors.email && <span className={styles.error}>{errors.email}</span>}
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="phone">Phone Number *</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="+91 98765 43210"
                    className="input"
                    required
                  />
                  {errors.phone && <span className={styles.error}>{errors.phone}</span>}
                </div>

                {activeTab === 'patient' && (
                  <div className={styles.formGroup}>
                    <label htmlFor="gender">Gender</label>
                    <select
                      id="gender"
                      name="gender"
                      value={formData.gender}
                      onChange={handleChange}
                      className="input"
                    >
                      <option value="">Select Gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                )}
              </div>

              {activeTab === 'patient' && (
                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="dateOfBirth">Date of Birth</label>
                    <input
                      type="date"
                      id="dateOfBirth"
                      name="dateOfBirth"
                      value={formData.dateOfBirth}
                      onChange={handleChange}
                      className="input"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Hospital Specific Fields */}
            {activeTab === 'hospital' && (
              <div className={styles.section}>
                <h3>Hospital Information</h3>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="hospitalName">Hospital Name *</label>
                    <input
                      type="text"
                      id="hospitalName"
                      name="hospitalName"
                      value={formData.hospitalName}
                      onChange={handleChange}
                      placeholder="Enter hospital name"
                      className="input"
                      required
                    />
                    {errors.hospitalName && <span className={styles.error}>{errors.hospitalName}</span>}
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="licenseNumber">License Number *</label>
                    <input
                      type="text"
                      id="licenseNumber"
                      name="licenseNumber"
                      value={formData.licenseNumber}
                      onChange={handleChange}
                      placeholder="Enter license number"
                      className="input"
                      required
                    />
                    {errors.licenseNumber && <span className={styles.error}>{errors.licenseNumber}</span>}
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="address">Hospital Address *</label>
                  <textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    placeholder="Enter complete hospital address"
                    className="input"
                    rows="3"
                    required
                  />
                  {errors.address && <span className={styles.error}>{errors.address}</span>}
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="city">City *</label>
                    <input
                      type="text"
                      id="city"
                      name="city"
                      value={formData.city}
                      onChange={handleChange}
                      placeholder="Enter city"
                      className="input"
                      required
                    />
                    {errors.city && <span className={styles.error}>{errors.city}</span>}
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="state">State</label>
                    <select
                      id="state"
                      name="state"
                      value={formData.state}
                      onChange={handleChange}
                      className="input"
                    >
                      <option value="">Select State</option>
                      <option value="delhi">Delhi</option>
                      <option value="maharashtra">Maharashtra</option>
                      <option value="karnataka">Karnataka</option>
                      <option value="tamil-nadu">Tamil Nadu</option>
                      <option value="gujarat">Gujarat</option>
                      <option value="rajasthan">Rajasthan</option>
                      <option value="uttar-pradesh">Uttar Pradesh</option>
                      <option value="west-bengal">West Bengal</option>
                    </select>
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="pincode">Pincode</label>
                    <input
                      type="text"
                      id="pincode"
                      name="pincode"
                      value={formData.pincode}
                      onChange={handleChange}
                      placeholder="Enter pincode"
                      className="input"
                    />
                  </div>
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="hospitalType">Hospital Type</label>
                    <select
                      id="hospitalType"
                      name="hospitalType"
                      value={formData.hospitalType}
                      onChange={handleChange}
                      className="input"
                    >
                      <option value="">Select Type</option>
                      <option value="government">Government</option>
                      <option value="private">Private</option>
                      <option value="trust">Trust/NGO</option>
                      <option value="corporate">Corporate</option>
                    </select>
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="totalBeds">Total Beds</label>
                    <input
                      type="number"
                      id="totalBeds"
                      name="totalBeds"
                      value={formData.totalBeds}
                      onChange={handleChange}
                      placeholder="Enter total beds"
                      className="input"
                      min="1"
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="establishedYear">Established Year</label>
                    <input
                      type="number"
                      id="establishedYear"
                      name="establishedYear"
                      value={formData.establishedYear}
                      onChange={handleChange}
                      placeholder="Enter year"
                      className="input"
                      min="1900"
                      max={new Date().getFullYear()}
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="description">Hospital Description</label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Brief description of your hospital, specialties, and services"
                    className="input"
                    rows="4"
                  />
                </div>

                <div className={styles.checkboxGroup}>
                  <h4>Services Available</h4>
                  <div className={styles.checkboxRow}>
                    <label className={styles.checkboxLabel}>
                      <input
                        type="checkbox"
                        name="emergencyServices"
                        checked={formData.emergencyServices}
                        onChange={handleChange}
                        className={styles.checkbox}
                      />
                      🚨 24/7 Emergency Services
                    </label>

                    <label className={styles.checkboxLabel}>
                      <input
                        type="checkbox"
                        name="ambulanceService"
                        checked={formData.ambulanceService}
                        onChange={handleChange}
                        className={styles.checkbox}
                      />
                      🚑 Ambulance Service
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Password Section */}
            <div className={styles.section}>
              <h3>Security</h3>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="password">Password *</label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder="Enter password (min 6 characters)"
                    className="input"
                    required
                  />
                  {errors.password && <span className={styles.error}>{errors.password}</span>}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="confirmPassword">Confirm Password *</label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    placeholder="Confirm your password"
                    className="input"
                    required
                  />
                  {errors.confirmPassword && <span className={styles.error}>{errors.confirmPassword}</span>}
                </div>
              </div>
            </div>

            {/* Terms and Submit */}
            <div className={styles.section}>
              <div className={styles.termsSection}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="terms"
                    required
                    className={styles.checkbox}
                  />
                  I agree to the{' '}
                  <Link to="#" className={styles.link}>Terms and Conditions</Link>{' '}
                  and{' '}
                  <Link to="#" className={styles.link}>Privacy Policy</Link>
                </label>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary w-full"
              >
                {isLoading ? (
                  <>
                    <span className={styles.spinner}></span>
                    {activeTab === 'hospital' ? 'Registering Hospital...' : 'Creating Account...'}
                  </>
                ) : (
                  activeTab === 'hospital' ? '🏥 Register Hospital' : '👤 Create Account'
                )}
              </button>
            </div>
          </form>

          {/* Login Link */}
          <div className={styles.loginLink}>
            <p>
              Already have an account?{' '}
              <Link to="/login" className={styles.link}>
                Sign in here
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
