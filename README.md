# Hope Medics - Hospital Booking & Management Platform

A full-stack hospital booking and management platform built with React.js (frontend) and Node.js + Express + MySQL (backend).

## 🌟 Features

- **Patient Portal**: Browse hospitals, view services & pricing, book appointments
- **Doctor Dashboard**: View appointments and patient information
- **Reception Panel**: Manual walk-in patient booking
- **Admin Dashboard**: Manage hospitals, doctors, and users
- **Hospital Listings**: Detailed hospital information with photos and services

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

### Installation

1. **Clone and install dependencies:**
```bash
npm run install-all
```

2. **Set up MySQL database:**

**Option A: Using MySQL Command Line**
```bash
# Connect to MySQL
mysql -u root -p

# Create database
CREATE DATABASE hopemedics;
exit;
```

**Option B: Using MySQL Workbench or phpMyAdmin**
- Create a new database named `hopemedics`

3. **Configure environment variables:**
```bash
# Navigate to server directory and update .env file
cd server
# Edit .env file with your MySQL credentials:
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=hopemedics
PORT=5000
```

4. **Initialize database with sample data:**
```bash
# From the server directory
npm run seed
```

5. **Run the application:**
```bash
# From the root directory
npm run dev
```

This will start both frontend (http://localhost:5173) and backend (http://localhost:5000) servers.

## 📁 Project Structure

```
HOPE-MEDICS/
├── client/                 # React frontend
├── server/                # Node.js backend
├── database/              # SQL scripts
└── README.md
```

## 🔗 Routes (Development Access)

During development, all pages are accessible directly:

- `/` - Home (Hospital listings)
- `/hospital/:id` - Hospital details
- `/book` - Book appointment
- `/login` - Login page
- `/register` - Register page
- `/admin` - Admin dashboard
- `/doctor` - Doctor dashboard
- `/reception` - Reception dashboard

## 🛠️ Tech Stack

**Frontend:**
- React.js with Vite
- React Router DOM
- Tailwind CSS
- Axios for API calls

**Backend:**
- Node.js + Express
- MySQL with mysql2
- JWT for authentication
- bcryptjs for password hashing

## 📊 Database Schema

- **Users**: Patient, doctor, admin, receptionist accounts
- **Hospitals**: Hospital information, services, pricing
- **Doctors**: Doctor profiles linked to hospitals
- **Appointments**: Booking records and status

## 🔧 Development Notes

- All pages accessible via navigation during development
- Mock authentication initially for UI testing
- Sample data seeded for testing
- Hot reload enabled for both frontend and backend

## 🎯 Current Status

✅ **Completed Features:**
- Full React frontend with routing and responsive design
- Complete backend API with MySQL integration
- All major pages implemented (Home, Hospital Details, Booking, Dashboards)
- Authentication system (login/register)
- Hospital and doctor management
- Appointment booking system
- Admin, Doctor, and Reception dashboards
- Database schema with relationships and triggers
- Sample data seeding

🔄 **Next Steps:**
- Configure MySQL database connection
- Test API endpoints
- Implement real authentication middleware
- Add image upload functionality
- Enhance error handling
- Add email notifications
- Implement payment integration

## 🔐 Demo Credentials

Once the database is set up and seeded, you can use these credentials:

- **Patient**: <EMAIL> / password
- **Doctor**: <EMAIL> / password
- **Admin**: <EMAIL> / password
- **Reception**: <EMAIL> / password

## 🛠️ API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user

### Hospitals
- `GET /api/hospitals` - List all hospitals
- `GET /api/hospitals/:id` - Get hospital details
- `POST /api/hospitals` - Create hospital (Admin)
- `PUT /api/hospitals/:id` - Update hospital (Admin)

### Doctors
- `GET /api/doctors` - List all doctors
- `GET /api/doctors/:id` - Get doctor details
- `GET /api/doctors/:id/appointments` - Get doctor's appointments

### Appointments
- `GET /api/appointments` - List appointments
- `POST /api/appointments` - Book appointment
- `POST /api/appointments/manual` - Manual booking (Reception)
- `PUT /api/appointments/:id/status` - Update appointment status

## 🚨 Troubleshooting

### Database Connection Issues
1. Ensure MySQL is running
2. Check credentials in `server/.env`
3. Verify database `hopemedics` exists
4. Check MySQL port (default: 3306)

### Frontend Issues
1. Clear browser cache
2. Restart development server
3. Check console for errors

### Backend Issues
1. Check server logs
2. Verify all dependencies installed
3. Ensure database is accessible
