.container {
  min-height: 100vh;
  background: var(--light-grey);
}

/* Header */
.header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: 3rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.headerContent {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.logoIcon {
  font-size: 3rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logoText {
  font-size: 2rem;
  font-weight: 700;
}

.headerContent h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.headerContent p {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
}

/* Main Content */
.mainContent {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  margin-bottom: 2rem;
  overflow: hidden;
}

.tabButton {
  flex: 1;
  padding: 1.5rem 2rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: var(--transition);
  border-right: 1px solid var(--border-color);
}

.tabButton:last-child {
  border-right: none;
}

.tabButton:hover {
  background: rgba(0, 121, 107, 0.05);
  color: var(--primary-color);
}

.tabActive {
  background: var(--primary-color);
  color: var(--white);
}

.tabActive:hover {
  background: var(--primary-dark);
  color: var(--white);
}

/* Form Container */
.formContainer {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Form Sections */
.section {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 2rem;
}

.section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section h3::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--accent-color);
  border-radius: 2px;
}

/* Form Layout */
.formRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.formRow:last-child {
  margin-bottom: 0;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 500;
  color: var(--dark-grey);
  font-size: 0.875rem;
}

.formGroup .input {
  margin-bottom: 0;
}

.formGroup textarea.input {
  resize: vertical;
  min-height: 80px;
}

.error {
  color: #dc2626;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Checkbox Groups */
.checkboxGroup {
  margin-top: 1rem;
}

.checkboxGroup h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--dark-grey);
  margin: 0 0 1rem 0;
}

.checkboxRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--dark-grey);
  cursor: pointer;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.checkboxLabel:hover {
  background: var(--light-grey);
  border-color: var(--primary-color);
}

.checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

/* Terms Section */
.termsSection {
  margin-bottom: 1.5rem;
}

.termsSection .checkboxLabel {
  border: none;
  padding: 0;
  background: none;
}

.termsSection .checkboxLabel:hover {
  background: none;
}

.link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Submit Button */
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Login Link */
.loginLink {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.loginLink p {
  color: #666;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .headerContent {
    padding: 0 1rem;
  }
  
  .headerContent h1 {
    font-size: 1.875rem;
  }
  
  .logoIcon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .logoText {
    font-size: 1.5rem;
  }
  
  .mainContent {
    padding: 1rem;
  }
  
  .tabNavigation {
    flex-direction: column;
  }
  
  .tabButton {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .tabButton:last-child {
    border-bottom: none;
  }
  
  .formContainer {
    padding: 1.5rem;
  }
  
  .formRow {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .checkboxRow {
    grid-template-columns: 1fr;
  }
}
