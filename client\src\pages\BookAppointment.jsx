import React, { useState } from 'react';
import styles from './BookAppointment.module.css';

const BookAppointment = () => {
  const [formData, setFormData] = useState({
    patientName: '',
    contactNumber: '',
    hospital: '',
    doctor: '',
    date: '',
    time: '',
    reason: ''
  });
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Simulate booking
    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 3000);
    // Reset form
    setFormData({
      patientName: '',
      contactNumber: '',
      hospital: '',
      doctor: '',
      date: '',
      time: '',
      reason: ''
    });
  };

  return (
    <div className={styles.container}>
      {/* Success Toast */}
      {showSuccess && (
        <div className={styles.successToast}>
          <span className={styles.checkIcon}>✓</span>
          Appointment booked successfully!
        </div>
      )}

      <div className={styles.bookingCard}>
        <div className={styles.header}>
          <h1>Book an Appointment</h1>
          <p>Schedule your visit with our qualified medical professionals</p>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="patientName">Patient Name</label>
            <input
              type="text"
              id="patientName"
              name="patientName"
              value={formData.patientName}
              onChange={handleInputChange}
              placeholder="Enter your full name"
              className="input"
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="contactNumber">Contact Number</label>
            <input
              type="tel"
              id="contactNumber"
              name="contactNumber"
              value={formData.contactNumber}
              onChange={handleInputChange}
              placeholder="+91 98765 43210"
              className="input"
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="hospital">Hospital</label>
            <select
              id="hospital"
              name="hospital"
              value={formData.hospital}
              onChange={handleInputChange}
              className="input"
              required
            >
              <option value="">Select a hospital...</option>
              <option value="apollo-delhi">Apollo Hospital, Delhi</option>
              <option value="fortis-bangalore">Fortis Hospital, Bangalore</option>
              <option value="medanta-gurgaon">Medanta Medicity, Gurgaon</option>
              <option value="aiims-delhi">AIIMS, New Delhi</option>
              <option value="max-mumbai">Max Healthcare, Mumbai</option>
              <option value="manipal-bangalore">Manipal Hospital, Bangalore</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="doctor">Doctor</label>
            <select
              id="doctor"
              name="doctor"
              value={formData.doctor}
              onChange={handleInputChange}
              className="input"
              required
            >
              <option value="">Select a doctor...</option>
              <option value="dr-meena-sharma">Dr. Meena Sharma - Cardiologist</option>
              <option value="dr-arjun-rao">Dr. Arjun Rao - Orthopedic</option>
              <option value="dr-sunil-patel">Dr. Sunil Patel - General Physician</option>
              <option value="dr-priya-singh">Dr. Priya Singh - Pediatrician</option>
              <option value="dr-rajesh-kumar">Dr. Rajesh Kumar - Neurologist</option>
            </select>
          </div>

          <div className={styles.dateTimeRow}>
            <div className={styles.formGroup}>
              <label htmlFor="date">Preferred Date</label>
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className="input"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="time">Preferred Time</label>
              <select
                id="time"
                name="time"
                value={formData.time}
                onChange={handleInputChange}
                className="input"
                required
              >
                <option value="">Select time...</option>
                <option value="09:00">09:00 AM</option>
                <option value="10:00">10:00 AM</option>
                <option value="11:00">11:00 AM</option>
                <option value="14:00">02:00 PM</option>
                <option value="15:00">03:00 PM</option>
                <option value="16:00">04:00 PM</option>
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="reason">Reason for Visit</label>
            <textarea
              id="reason"
              name="reason"
              value={formData.reason}
              onChange={handleInputChange}
              placeholder="Please describe the reason for your visit..."
              className="input"
              rows="4"
            />
          </div>

          <button type="submit" className="btn-primary w-full">
            📅 Book Appointment
          </button>
        </form>
      </div>
    </div>
  );
};

export default BookAppointment;
