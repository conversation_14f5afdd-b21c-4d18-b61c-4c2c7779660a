.container {
  min-height: 100vh;
  background: var(--light-grey);
}

/* Search Header */
.searchHeader {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: 3rem 0;
  position: relative;
  overflow: hidden;
}

.searchHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.searchContent {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.searchContent h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.searchContent p {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
}

.searchFilters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.filterGroup {
  display: flex;
  flex-direction: column;
}

/* Results Container */
.resultsContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.resultsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.resultsHeader h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.sortOptions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Hospital Grid */
.hospitalGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

.hospitalCard {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.hospitalCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--primary-color);
}

/* Hospital Image */
.hospitalImage {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.hospitalImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.hospitalCard:hover .hospitalImage img {
  transform: scale(1.05);
}

.verifiedBadge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: #10b981;
  color: var(--white);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
}

.distanceBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: var(--white);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Hospital Info */
.hospitalInfo {
  padding: 1.5rem;
}

.hospitalHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.hospitalHeader h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  flex: 1;
}

.rating {
  font-size: 0.875rem;
  color: var(--accent-color);
  font-weight: 600;
  white-space: nowrap;
  margin-left: 1rem;
}

.location {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 1rem;
}

/* Tags */
.specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.specialtyTag {
  background: rgba(0, 121, 107, 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

.services {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.serviceTag {
  background: rgba(255, 152, 0, 0.1);
  color: var(--accent-color);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Pricing */
.pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--light-grey);
  border-radius: var(--border-radius);
}

.priceInfo {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.fromPrice {
  font-size: 0.75rem;
  color: #666;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.perDay {
  font-size: 0.875rem;
  color: #666;
}

.availability {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

/* Card Actions */
.cardActions {
  display: flex;
  gap: 1rem;
}

.cardActions .btn-secondary,
.cardActions .btn-primary {
  flex: 1;
  text-align: center;
  text-decoration: none;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

/* No Results */
.noResults {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.noResultsIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.noResults h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.noResults p {
  font-size: 1rem;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .searchContent {
    padding: 0 1rem;
  }
  
  .searchContent h1 {
    font-size: 1.875rem;
  }
  
  .searchFilters {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .resultsContainer {
    padding: 1rem;
  }
  
  .resultsHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .hospitalGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .hospitalHeader {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .hospitalHeader h3 {
    margin-bottom: 0.5rem;
  }
  
  .rating {
    margin-left: 0;
  }
  
  .pricing {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .cardActions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .hospitalCard {
    margin: 0 -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .hospitalImage {
    height: 150px;
  }
  
  .hospitalInfo {
    padding: 1rem;
  }
}
