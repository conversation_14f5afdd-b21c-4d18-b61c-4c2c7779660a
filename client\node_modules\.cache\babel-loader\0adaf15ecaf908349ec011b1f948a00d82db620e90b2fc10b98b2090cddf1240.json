{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport HospitalDetails from './pages/HospitalDetails';\nimport BookAppointment from './pages/BookAppointment';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport AdminDashboard from './pages/AdminDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\nimport ReceptionDashboard from './pages/ReceptionDashboard';\nimport styles from './App.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.app,\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: styles.main,\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/hospital/:id\",\n            element: /*#__PURE__*/_jsxDEV(HospitalDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/book\",\n            element: /*#__PURE__*/_jsxDEV(BookAppointment, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin\",\n            element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/doctor\",\n            element: /*#__PURE__*/_jsxDEV(DoctorDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/reception\",\n            element: /*#__PURE__*/_jsxDEV(ReceptionDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON>", "Home", "HospitalDetails", "BookAppointment", "<PERSON><PERSON>", "Register", "AdminDashboard", "DoctorDashboard", "ReceptionDashboard", "styles", "jsxDEV", "_jsxDEV", "App", "children", "className", "app", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "main", "path", "element", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Home from './pages/Home';\nimport HospitalDetails from './pages/HospitalDetails';\nimport BookAppointment from './pages/BookAppointment';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport AdminDashboard from './pages/AdminDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\nimport ReceptionDashboard from './pages/ReceptionDashboard';\nimport styles from './App.module.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className={styles.app}>\n        <Navbar />\n        <main className={styles.main}>\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/hospital/:id\" element={<HospitalDetails />} />\n            <Route path=\"/book\" element={<BookAppointment />} />\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/admin\" element={<AdminDashboard />} />\n            <Route path=\"/doctor\" element={<DoctorDashboard />} />\n            <Route path=\"/reception\" element={<ReceptionDashboard />} />\n          </Routes>\n        </main>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,MAAM,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACd,MAAM;IAAAgB,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAEL,MAAM,CAACM,GAAI;MAAAF,QAAA,gBACzBF,OAAA,CAACX,MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVR,OAAA;QAAMG,SAAS,EAAEL,MAAM,CAACW,IAAK;QAAAP,QAAA,eAC3BF,OAAA,CAACb,MAAM;UAAAe,QAAA,gBACLF,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEX,OAAA,CAACV,IAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCR,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEX,OAAA,CAACT,eAAe;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DR,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEX,OAAA,CAACR,eAAe;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDR,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEX,OAAA,CAACP,KAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CR,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEX,OAAA,CAACN,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDR,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEX,OAAA,CAACL,cAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDR,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEX,OAAA,CAACJ,eAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDR,OAAA,CAACZ,KAAK;YAACsB,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEX,OAAA,CAACH,kBAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACI,EAAA,GApBQX,GAAG;AAsBZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}