import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../components/ui/card';
import { But<PERSON> } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { 
  Calendar, 
  Users, 
  Clock, 
  CheckCircle,
  Plus,
  Phone,
  UserPlus
} from 'lucide-react';

const ReceptionDashboard = () => {
  const stats = [
    { title: 'Today\'s Total', value: '24', icon: Calendar, color: 'text-blue-600' },
    { title: 'Checked In', value: '18', icon: CheckCircle, color: 'text-green-600' },
    { title: 'Currently Waiting', value: '6', icon: Clock, color: 'text-orange-600' },
    { title: 'Completed', value: '12', icon: Users, color: 'text-purple-600' },
  ];

  const appointments = [
    { id: 1, time: '09:00', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'checked-in' },
    { id: 2, time: '09:30', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'waiting' },
    { id: 3, time: '10:00', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'in-progress' },
    { id: 4, time: '10:30', patient: '<PERSON>', doctor: 'Dr. <PERSON>', status: 'completed' },
  ];

  const walkI<PERSON>Queue = [
    { id: 5, patient: 'David Lee', reason: 'Emergency consultation', priority: 'high', waitTime: '15 min' },
    { id: 6, patient: 'Lisa Wang', reason: 'General checkup', priority: 'normal', waitTime: '25 min' },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'checked-in': return 'bg-blue-100 text-blue-800';
      case 'waiting': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'normal': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Reception Dashboard</h1>
          <p className="text-muted-foreground">Manage appointments and walk-in patients</p>
        </div>
        <div className="flex space-x-4">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Walk-in Patient
          </Button>
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Print Schedule
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <IconComponent className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Today's Appointments */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Today's Appointments
                <span className="text-sm text-muted-foreground">
                  {new Date().toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {appointments.map((appointment) => (
                  <div key={appointment.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="font-semibold text-lg">{appointment.time}</span>
                          <span className="font-medium">{appointment.patient}</span>
                          <Badge className={getStatusColor(appointment.status)}>
                            {appointment.status.replace('-', ' ').toUpperCase()}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div>👩‍⚕️ {appointment.doctor}</div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {appointment.status === 'waiting' && (
                          <Button size="sm">Check In</Button>
                        )}
                        {appointment.status === 'checked-in' && (
                          <Button size="sm" variant="outline">
                            <Phone className="h-4 w-4 mr-1" />
                            Call Patient
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Walk-in Queue */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Walk-in Queue
                <Badge variant="outline">{walkInQueue.length} waiting</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {walkInQueue.map((patient) => (
                  <div key={patient.id} className="border rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-medium">{patient.patient}</span>
                      <Badge className={getPriorityColor(patient.priority)}>
                        {patient.priority.toUpperCase()}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>📝 {patient.reason}</div>
                      <div>⏱️ Waiting: {patient.waitTime}</div>
                    </div>
                    <div className="flex space-x-2 mt-3">
                      <Button size="sm" className="flex-1">
                        Assign Doctor
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {walkInQueue.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <div className="text-4xl mb-2">👥</div>
                  <p>No walk-in patients waiting</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Phone className="h-6 w-6 mb-2" />
              <span className="text-sm">Call Next Patient</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Calendar className="h-6 w-6 mb-2" />
              <span className="text-sm">View All Appointments</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <UserPlus className="h-6 w-6 mb-2" />
              <span className="text-sm">Room Management</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <CheckCircle className="h-6 w-6 mb-2" />
              <span className="text-sm">Daily Report</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReceptionDashboard;
