{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\components\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport styles from './Navbar.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const location = useLocation();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const navLinks = [{\n    path: '/',\n    label: 'Home'\n  }, {\n    path: '/hospitals',\n    label: 'Hospitals'\n  }, {\n    path: '/book',\n    label: 'Book Appointment'\n  }, {\n    path: '/login',\n    label: 'Login'\n  }, {\n    path: '/register',\n    label: 'Register'\n  }, {\n    path: '/admin',\n    label: 'Admin'\n  }, {\n    path: '/doctor',\n    label: 'Doctor'\n  }, {\n    path: '/reception',\n    label: 'Reception'\n  }, {\n    path: '/hospital-dashboard',\n    label: 'Hospital Dashboard'\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: styles.navbar,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `container ${styles.navContainer}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.navContent,\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: styles.logo,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.logoIcon,\n            children: \"\\uD83C\\uDFE5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.logoText,\n            children: \"Hope Medics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.desktopNav,\n          children: navLinks.map(link => /*#__PURE__*/_jsxDEV(Link, {\n            to: link.path,\n            className: `${styles.navLink} ${isActive(link.path) ? styles.navLinkActive : ''}`,\n            children: link.label\n          }, link.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: styles.mobileMenuButton,\n          onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: styles.hamburgerIcon,\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 6h16M4 12h16M4 18h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.mobileNav,\n        children: navLinks.map(link => /*#__PURE__*/_jsxDEV(Link, {\n          to: link.path,\n          className: `${styles.mobileNavLink} ${isActive(link.path) ? styles.mobileNavLinkActive : ''}`,\n          onClick: () => setIsMobileMenuOpen(false),\n          children: link.label\n        }, link.path, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"Vcf5d/+1iDxEx/cx5qDx82JQtSk=\", false, function () {\n  return [useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "styles", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "location", "isMobileMenuOpen", "setIsMobileMenuOpen", "navLinks", "path", "label", "isActive", "pathname", "className", "navbar", "children", "navContainer", "navContent", "to", "logo", "logoIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "logoText", "desktopNav", "map", "link", "navLink", "navLinkActive", "mobileMenuButton", "onClick", "hamburgerIcon", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "mobileNav", "mobileNavLink", "mobileNavLinkActive", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/components/Navbar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport styles from './Navbar.module.css';\n\nconst Navbar = () => {\n  const location = useLocation();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const navLinks = [\n    { path: '/', label: 'Home' },\n    { path: '/hospitals', label: 'Hospitals' },\n    { path: '/book', label: 'Book Appointment' },\n    { path: '/login', label: 'Login' },\n    { path: '/register', label: 'Register' },\n    { path: '/admin', label: 'Admin' },\n    { path: '/doctor', label: 'Doctor' },\n    { path: '/reception', label: 'Reception' },\n    { path: '/hospital-dashboard', label: 'Hospital Dashboard' },\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <nav className={styles.navbar}>\n      <div className={`container ${styles.navContainer}`}>\n        <div className={styles.navContent}>\n          {/* Logo */}\n          <Link to=\"/\" className={styles.logo}>\n            <div className={styles.logoIcon}>\n              🏥\n            </div>\n            <span className={styles.logoText}>Hope Medics</span>\n          </Link>\n\n          {/* Desktop Navigation Links */}\n          <div className={styles.desktopNav}>\n            {navLinks.map((link) => (\n              <Link\n                key={link.path}\n                to={link.path}\n                className={`${styles.navLink} ${isActive(link.path) ? styles.navLinkActive : ''}`}\n              >\n                {link.label}\n              </Link>\n            ))}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className={styles.mobileMenuButton}\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            <svg className={styles.hamburgerIcon} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className={styles.mobileNav}>\n            {navLinks.map((link) => (\n              <Link\n                key={link.path}\n                to={link.path}\n                className={`${styles.mobileNavLink} ${isActive(link.path) ? styles.mobileNavLinkActive : ''}`}\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                {link.label}\n              </Link>\n            ))}\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMW,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5B;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC5C;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAqB,CAAC,CAC7D;EAED,MAAMC,QAAQ,GAAIF,IAAI,IAAKJ,QAAQ,CAACO,QAAQ,KAAKH,IAAI;EAErD,oBACEP,OAAA;IAAKW,SAAS,EAAEb,MAAM,CAACc,MAAO;IAAAC,QAAA,eAC5Bb,OAAA;MAAKW,SAAS,EAAE,aAAab,MAAM,CAACgB,YAAY,EAAG;MAAAD,QAAA,gBACjDb,OAAA;QAAKW,SAAS,EAAEb,MAAM,CAACiB,UAAW;QAAAF,QAAA,gBAEhCb,OAAA,CAACJ,IAAI;UAACoB,EAAE,EAAC,GAAG;UAACL,SAAS,EAAEb,MAAM,CAACmB,IAAK;UAAAJ,QAAA,gBAClCb,OAAA;YAAKW,SAAS,EAAEb,MAAM,CAACoB,QAAS;YAAAL,QAAA,EAAC;UAEjC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAMW,SAAS,EAAEb,MAAM,CAACyB,QAAS;YAAAV,QAAA,EAAC;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAGPtB,OAAA;UAAKW,SAAS,EAAEb,MAAM,CAAC0B,UAAW;UAAAX,QAAA,EAC/BP,QAAQ,CAACmB,GAAG,CAAEC,IAAI,iBACjB1B,OAAA,CAACJ,IAAI;YAEHoB,EAAE,EAAEU,IAAI,CAACnB,IAAK;YACdI,SAAS,EAAE,GAAGb,MAAM,CAAC6B,OAAO,IAAIlB,QAAQ,CAACiB,IAAI,CAACnB,IAAI,CAAC,GAAGT,MAAM,CAAC8B,aAAa,GAAG,EAAE,EAAG;YAAAf,QAAA,EAEjFa,IAAI,CAAClB;UAAK,GAJNkB,IAAI,CAACnB,IAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtB,OAAA;UACEW,SAAS,EAAEb,MAAM,CAAC+B,gBAAiB;UACnCC,OAAO,EAAEA,CAAA,KAAMzB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UAAAS,QAAA,eAEtDb,OAAA;YAAKW,SAAS,EAAEb,MAAM,CAACiC,aAAc;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAArB,QAAA,eACzFb,OAAA;cAAMmC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAyB;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLlB,gBAAgB,iBACfJ,OAAA;QAAKW,SAAS,EAAEb,MAAM,CAACyC,SAAU;QAAA1B,QAAA,EAC9BP,QAAQ,CAACmB,GAAG,CAAEC,IAAI,iBACjB1B,OAAA,CAACJ,IAAI;UAEHoB,EAAE,EAAEU,IAAI,CAACnB,IAAK;UACdI,SAAS,EAAE,GAAGb,MAAM,CAAC0C,aAAa,IAAI/B,QAAQ,CAACiB,IAAI,CAACnB,IAAI,CAAC,GAAGT,MAAM,CAAC2C,mBAAmB,GAAG,EAAE,EAAG;UAC9FX,OAAO,EAAEA,CAAA,KAAMzB,mBAAmB,CAAC,KAAK,CAAE;UAAAQ,QAAA,EAEzCa,IAAI,CAAClB;QAAK,GALNkB,IAAI,CAACnB,IAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CAxEID,MAAM;EAAA,QACOJ,WAAW;AAAA;AAAA6C,EAAA,GADxBzC,MAAM;AA0EZ,eAAeA,MAAM;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}