{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 20h20\",\n  key: \"owomy5\"\n}], [\"path\", {\n  d: \"m9 10 2 2 4-4\",\n  key: \"1gnqz4\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"4\",\n  width: \"18\",\n  height: \"12\",\n  rx: \"2\",\n  key: \"8ur36m\"\n}]];\nconst LaptopMinimalCheck = createLucideIcon(\"laptop-minimal-check\", __iconNode);\nexport { __iconNode, LaptopMinimalCheck as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "LaptopMinimalCheck", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\laptop-minimal-check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M2 20h20', key: 'owomy5' }],\n  ['path', { d: 'm9 10 2 2 4-4', key: '1gnqz4' }],\n  ['rect', { x: '3', y: '4', width: '18', height: '12', rx: '2', key: '8ur36m' }],\n];\n\n/**\n * @component @name LaptopMinimalCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMGgyMCIgLz4KICA8cGF0aCBkPSJtOSAxMCAyIDIgNC00IiAvPgogIDxyZWN0IHg9IjMiIHk9IjQiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxMiIgcng9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/laptop-minimal-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LaptopMinimalCheck = createLucideIcon('laptop-minimal-check', __iconNode);\n\nexport default LaptopMinimalCheck;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAChF;AAaM,MAAAM,kBAAA,GAAqBC,gBAAiB,yBAAwBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}