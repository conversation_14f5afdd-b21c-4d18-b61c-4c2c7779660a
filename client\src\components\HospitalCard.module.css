.card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card:hover {
  transform: scale(1.03);
  box-shadow: var(--shadow-heavy);
}

.imageContainer {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.card:hover .image {
  transform: scale(1.1);
}

.rating {
  position: absolute;
  top: 12px;
  right: 12px;
  background: var(--white);
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: var(--shadow-medium);
}

.content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.hospitalName {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  line-height: 1.3;
}

.city {
  color: #666;
  font-size: 0.875rem;
  margin: 0;
}

.services {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.serviceTag {
  background: rgba(0, 121, 107, 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

.moreServices {
  color: #666;
  font-size: 0.75rem;
  font-style: italic;
}

.availability {
  background: var(--light-grey);
  padding: 1rem;
  border-radius: var(--border-radius);
}

.availabilityHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.availabilityCount {
  font-weight: 600;
  color: var(--primary-color);
}

.progressBar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.pricing {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.priceLabel {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
}

.actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
}

.detailsBtn,
.bookBtn {
  flex: 1;
  text-align: center;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.detailsBtn:hover,
.bookBtn:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content {
    padding: 1rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .hospitalName {
    font-size: 1.125rem;
  }
  
  .price {
    font-size: 1.25rem;
  }
}
