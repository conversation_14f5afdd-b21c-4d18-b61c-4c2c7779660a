{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n    // Clear specific error when user types\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n    setFormData({\n      ...formData,\n      role: tab\n    });\n    setErrors({});\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Common validations\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n\n    // Hospital specific validations\n    if (activeTab === 'hospital') {\n      if (!formData.hospitalName.trim()) {\n        newErrors.hospitalName = 'Hospital name is required';\n      }\n      if (!formData.address.trim()) {\n        newErrors.address = 'Address is required';\n      }\n      if (!formData.city.trim()) {\n        newErrors.city = 'City is required';\n      }\n      if (!formData.licenseNumber.trim()) {\n        newErrors.licenseNumber = 'License number is required';\n      }\n    }\n    return newErrors;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n\n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name,\n        hospitalName: formData.hospitalName || null\n      }));\n      setIsLoading(false);\n\n      // Redirect based on role\n      if (activeTab === 'hospital') {\n        navigate('/hospital-dashboard');\n      } else {\n        navigate('/login');\n      }\n    }, 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.header,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.headerContent,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.logo,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.logoIcon,\n            children: \"\\uD83C\\uDFE5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.logoText,\n            children: \"Hope Medics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Join Hope Medics Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Register as a patient or hospital to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.mainContent,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.tabNavigation,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.tabButton} ${activeTab === 'patient' ? styles.tabActive : ''}`,\n          onClick: () => handleTabChange('patient'),\n          children: \"\\uD83D\\uDC64 Patient Registration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.tabButton} ${activeTab === 'hospital' ? styles.tabActive : ''}`,\n          onClick: () => handleTabChange('hospital'),\n          children: \"\\uD83C\\uDFE5 Hospital Registration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"Get started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardDescription, {\n            children: \"Create your account to book appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"text-sm font-medium\",\n                children: \"Full Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"name\",\n                name: \"name\",\n                type: \"text\",\n                required: true,\n                value: formData.name,\n                onChange: handleChange,\n                placeholder: \"Enter your full name\",\n                className: errors.name ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"text-sm font-medium\",\n                children: \"Email address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                required: true,\n                value: formData.email,\n                onChange: handleChange,\n                placeholder: \"Enter your email\",\n                className: errors.email ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"phone\",\n                className: \"text-sm font-medium\",\n                children: \"Phone Number *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"phone\",\n                name: \"phone\",\n                type: \"tel\",\n                required: true,\n                value: formData.phone,\n                onChange: handleChange,\n                placeholder: \"+****************\",\n                className: errors.phone ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"dateOfBirth\",\n                  className: \"text-sm font-medium\",\n                  children: \"Date of Birth\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"dateOfBirth\",\n                  name: \"dateOfBirth\",\n                  type: \"date\",\n                  value: formData.dateOfBirth,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"gender\",\n                  className: \"text-sm font-medium\",\n                  children: \"Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"gender\",\n                  name: \"gender\",\n                  value: formData.gender,\n                  onChange: handleChange,\n                  className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"male\",\n                    children: \"Male\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"female\",\n                    children: \"Female\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"other\",\n                    children: \"Other\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"prefer-not-to-say\",\n                    children: \"Prefer not to say\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"text-sm font-medium\",\n                children: \"Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.password,\n                onChange: handleChange,\n                placeholder: \"Enter your password\",\n                className: errors.password ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"confirmPassword\",\n                className: \"text-sm font-medium\",\n                children: \"Confirm Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                placeholder: \"Confirm your password\",\n                className: errors.confirmPassword ? 'border-destructive' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"role\",\n                className: \"text-sm font-medium\",\n                children: \"Register as\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"role\",\n                name: \"role\",\n                value: formData.role,\n                onChange: handleChange,\n                className: \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"patient\",\n                  children: \"Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"doctor\",\n                  children: \"Doctor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"receptionist\",\n                  children: \"Receptionist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"terms\",\n                name: \"terms\",\n                type: \"checkbox\",\n                required: true,\n                className: \"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"terms\",\n                className: \"text-sm\",\n                children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: \"text-primary hover:text-primary/80\",\n                  children: \"Terms and Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"#\",\n                  className: \"text-primary hover:text-primary/80\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"w-full\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), \"Creating account...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this) : 'Create account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"PW8Qs9IORXp2FcZbLYvkiJt4RU4=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "styles", "jsxDEV", "_jsxDEV", "Register", "_s", "activeTab", "setActiveTab", "formData", "setFormData", "name", "email", "password", "confirmPassword", "phone", "role", "dateOfBirth", "gender", "hospitalName", "address", "city", "state", "pincode", "licenseNumber", "establishedYear", "hospitalType", "totalBeds", "emergencyServices", "ambulanceService", "description", "isLoading", "setIsLoading", "errors", "setErrors", "navigate", "handleChange", "e", "value", "type", "checked", "target", "handleTabChange", "tab", "validateForm", "newErrors", "trim", "test", "length", "handleSubmit", "preventDefault", "Object", "keys", "setTimeout", "console", "log", "localStorage", "setItem", "JSON", "stringify", "className", "container", "children", "header", "headerContent", "logo", "logoIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "logoText", "mainContent", "tabNavigation", "tabButton", "tabActive", "onClick", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "htmlFor", "Input", "id", "required", "onChange", "placeholder", "autoComplete", "to", "<PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styles from './Register.module.css';\n\nconst Register = () => {\n  const [activeTab, setActiveTab] = useState('patient');\n  const [formData, setFormData] = useState({\n    // Common fields\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    role: 'patient',\n\n    // Patient specific\n    dateOfBirth: '',\n    gender: '',\n\n    // Hospital specific\n    hospitalName: '',\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    licenseNumber: '',\n    establishedYear: '',\n    hospitalType: '',\n    totalBeds: '',\n    emergencyServices: false,\n    ambulanceService: false,\n    description: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n    // Clear specific error when user types\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n    setFormData({\n      ...formData,\n      role: tab\n    });\n    setErrors({});\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Common validations\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n\n    // Hospital specific validations\n    if (activeTab === 'hospital') {\n      if (!formData.hospitalName.trim()) {\n        newErrors.hospitalName = 'Hospital name is required';\n      }\n      if (!formData.address.trim()) {\n        newErrors.address = 'Address is required';\n      }\n      if (!formData.city.trim()) {\n        newErrors.city = 'City is required';\n      }\n      if (!formData.licenseNumber.trim()) {\n        newErrors.licenseNumber = 'License number is required';\n      }\n    }\n\n    return newErrors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n\n    setIsLoading(true);\n    setErrors({});\n\n    // Mock registration - simulate API call\n    setTimeout(() => {\n      console.log('Registration data:', formData);\n\n      // Mock successful registration\n      localStorage.setItem('user', JSON.stringify({\n        email: formData.email,\n        role: formData.role,\n        name: formData.name,\n        hospitalName: formData.hospitalName || null\n      }));\n\n      setIsLoading(false);\n\n      // Redirect based on role\n      if (activeTab === 'hospital') {\n        navigate('/hospital-dashboard');\n      } else {\n        navigate('/login');\n      }\n    }, 2000);\n  };\n\n  return (\n    <div className={styles.container}>\n      {/* Header */}\n      <div className={styles.header}>\n        <div className={styles.headerContent}>\n          <div className={styles.logo}>\n            <span className={styles.logoIcon}>🏥</span>\n            <span className={styles.logoText}>Hope Medics</span>\n          </div>\n          <h1>Join Hope Medics Platform</h1>\n          <p>Register as a patient or hospital to get started</p>\n        </div>\n      </div>\n\n      <div className={styles.mainContent}>\n        {/* Tab Navigation */}\n        <div className={styles.tabNavigation}>\n          <button\n            className={`${styles.tabButton} ${activeTab === 'patient' ? styles.tabActive : ''}`}\n            onClick={() => handleTabChange('patient')}\n          >\n            👤 Patient Registration\n          </button>\n          <button\n            className={`${styles.tabButton} ${activeTab === 'hospital' ? styles.tabActive : ''}`}\n            onClick={() => handleTabChange('hospital')}\n          >\n            🏥 Hospital Registration\n          </button>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Get started</CardTitle>\n            <CardDescription>\n              Create your account to book appointments\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label htmlFor=\"name\" className=\"text-sm font-medium\">\n                  Full Name *\n                </label>\n                <Input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={handleChange}\n                  placeholder=\"Enter your full name\"\n                  className={errors.name ? 'border-destructive' : ''}\n                />\n                {errors.name && <p className=\"text-sm text-destructive\">{errors.name}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"email\" className=\"text-sm font-medium\">\n                  Email address *\n                </label>\n                <Input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleChange}\n                  placeholder=\"Enter your email\"\n                  className={errors.email ? 'border-destructive' : ''}\n                />\n                {errors.email && <p className=\"text-sm text-destructive\">{errors.email}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"phone\" className=\"text-sm font-medium\">\n                  Phone Number *\n                </label>\n                <Input\n                  id=\"phone\"\n                  name=\"phone\"\n                  type=\"tel\"\n                  required\n                  value={formData.phone}\n                  onChange={handleChange}\n                  placeholder=\"+****************\"\n                  className={errors.phone ? 'border-destructive' : ''}\n                />\n                {errors.phone && <p className=\"text-sm text-destructive\">{errors.phone}</p>}\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"dateOfBirth\" className=\"text-sm font-medium\">\n                    Date of Birth\n                  </label>\n                  <Input\n                    id=\"dateOfBirth\"\n                    name=\"dateOfBirth\"\n                    type=\"date\"\n                    value={formData.dateOfBirth}\n                    onChange={handleChange}\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"gender\" className=\"text-sm font-medium\">\n                    Gender\n                  </label>\n                  <select\n                    id=\"gender\"\n                    name=\"gender\"\n                    value={formData.gender}\n                    onChange={handleChange}\n                    className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                  >\n                    <option value=\"\">Select</option>\n                    <option value=\"male\">Male</option>\n                    <option value=\"female\">Female</option>\n                    <option value=\"other\">Other</option>\n                    <option value=\"prefer-not-to-say\">Prefer not to say</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"password\" className=\"text-sm font-medium\">\n                  Password *\n                </label>\n                <Input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  placeholder=\"Enter your password\"\n                  className={errors.password ? 'border-destructive' : ''}\n                />\n                {errors.password && <p className=\"text-sm text-destructive\">{errors.password}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"confirmPassword\" className=\"text-sm font-medium\">\n                  Confirm Password *\n                </label>\n                <Input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  placeholder=\"Confirm your password\"\n                  className={errors.confirmPassword ? 'border-destructive' : ''}\n                />\n                {errors.confirmPassword && <p className=\"text-sm text-destructive\">{errors.confirmPassword}</p>}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"role\" className=\"text-sm font-medium\">\n                  Register as\n                </label>\n                <select\n                  id=\"role\"\n                  name=\"role\"\n                  value={formData.role}\n                  onChange={handleChange}\n                  className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                >\n                  <option value=\"patient\">Patient</option>\n                  <option value=\"doctor\">Doctor</option>\n                  <option value=\"receptionist\">Receptionist</option>\n                </select>\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  id=\"terms\"\n                  name=\"terms\"\n                  type=\"checkbox\"\n                  required\n                  className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\n                />\n                <label htmlFor=\"terms\" className=\"text-sm\">\n                  I agree to the{' '}\n                  <Link to=\"#\" className=\"text-primary hover:text-primary/80\">\n                    Terms and Conditions\n                  </Link>{' '}\n                  and{' '}\n                  <Link to=\"#\" className=\"text-primary hover:text-primary/80\">\n                    Privacy Policy\n                  </Link>\n                </label>\n              </div>\n\n              <Button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Creating account...\n                  </div>\n                ) : (\n                  'Create account'\n                )}\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvC;IACAY,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,SAAS;IAEf;IACAC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IAEV;IACAC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,iBAAiB,EAAE,KAAK;IACxBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMoC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAE9B,MAAMmC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/C/B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAG4B,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC;IACF;IACA,IAAIL,MAAM,CAACtB,IAAI,CAAC,EAAE;MAChBuB,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACtB,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM+B,eAAe,GAAIC,GAAG,IAAK;IAC/BnC,YAAY,CAACmC,GAAG,CAAC;IACjBjC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXO,IAAI,EAAE2B;IACR,CAAC,CAAC;IACFT,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACpC,QAAQ,CAACE,IAAI,CAACmC,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAClC,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACkC,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACjC,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACmC,IAAI,CAACtC,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/CiC,SAAS,CAACjC,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBgC,SAAS,CAAChC,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACmC,MAAM,GAAG,CAAC,EAAE;MACvCH,SAAS,CAAChC,QAAQ,GAAG,wCAAwC;IAC/D;IAEA,IAAIJ,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClD+B,SAAS,CAAC/B,eAAe,GAAG,wBAAwB;IACtD;IAEA,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC9B,KAAK,GAAG,0BAA0B;IAC9C;;IAEA;IACA,IAAIR,SAAS,KAAK,UAAU,EAAE;MAC5B,IAAI,CAACE,QAAQ,CAACU,YAAY,CAAC2B,IAAI,CAAC,CAAC,EAAE;QACjCD,SAAS,CAAC1B,YAAY,GAAG,2BAA2B;MACtD;MACA,IAAI,CAACV,QAAQ,CAACW,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAE;QAC5BD,SAAS,CAACzB,OAAO,GAAG,qBAAqB;MAC3C;MACA,IAAI,CAACX,QAAQ,CAACY,IAAI,CAACyB,IAAI,CAAC,CAAC,EAAE;QACzBD,SAAS,CAACxB,IAAI,GAAG,kBAAkB;MACrC;MACA,IAAI,CAACZ,QAAQ,CAACe,aAAa,CAACsB,IAAI,CAAC,CAAC,EAAE;QAClCD,SAAS,CAACrB,aAAa,GAAG,4BAA4B;MACxD;IACF;IAEA,OAAOqB,SAAS;EAClB,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,MAAML,SAAS,GAAGD,YAAY,CAAC,CAAC;IAChC,IAAIO,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACrCd,SAAS,CAACW,SAAS,CAAC;MACpB;IACF;IAEAb,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEb;IACAmB,UAAU,CAAC,MAAM;MACfC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE9C,QAAQ,CAAC;;MAE3C;MACA+C,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;QAC1C/C,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBI,IAAI,EAAEP,QAAQ,CAACO,IAAI;QACnBL,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBQ,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI;MACzC,CAAC,CAAC,CAAC;MAEHa,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,IAAIzB,SAAS,KAAK,UAAU,EAAE;QAC5B4B,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,MAAM;QACLA,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACE/B,OAAA;IAAKwD,SAAS,EAAE1D,MAAM,CAAC2D,SAAU;IAAAC,QAAA,gBAE/B1D,OAAA;MAAKwD,SAAS,EAAE1D,MAAM,CAAC6D,MAAO;MAAAD,QAAA,eAC5B1D,OAAA;QAAKwD,SAAS,EAAE1D,MAAM,CAAC8D,aAAc;QAAAF,QAAA,gBACnC1D,OAAA;UAAKwD,SAAS,EAAE1D,MAAM,CAAC+D,IAAK;UAAAH,QAAA,gBAC1B1D,OAAA;YAAMwD,SAAS,EAAE1D,MAAM,CAACgE,QAAS;YAAAJ,QAAA,EAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3ClE,OAAA;YAAMwD,SAAS,EAAE1D,MAAM,CAACqE,QAAS;YAAAT,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNlE,OAAA;UAAA0D,QAAA,EAAI;QAAyB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClClE,OAAA;UAAA0D,QAAA,EAAG;QAAgD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlE,OAAA;MAAKwD,SAAS,EAAE1D,MAAM,CAACsE,WAAY;MAAAV,QAAA,gBAEjC1D,OAAA;QAAKwD,SAAS,EAAE1D,MAAM,CAACuE,aAAc;QAAAX,QAAA,gBACnC1D,OAAA;UACEwD,SAAS,EAAE,GAAG1D,MAAM,CAACwE,SAAS,IAAInE,SAAS,KAAK,SAAS,GAAGL,MAAM,CAACyE,SAAS,GAAG,EAAE,EAAG;UACpFC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,SAAS,CAAE;UAAAoB,QAAA,EAC3C;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlE,OAAA;UACEwD,SAAS,EAAE,GAAG1D,MAAM,CAACwE,SAAS,IAAInE,SAAS,KAAK,UAAU,GAAGL,MAAM,CAACyE,SAAS,GAAG,EAAE,EAAG;UACrFC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,UAAU,CAAE;UAAAoB,QAAA,EAC5C;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlE,OAAA,CAACyE,IAAI;QAAAf,QAAA,gBACH1D,OAAA,CAAC0E,UAAU;UAAAhB,QAAA,gBACT1D,OAAA,CAAC2E,SAAS;YAAAjB,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAClClE,OAAA,CAAC4E,eAAe;YAAAlB,QAAA,EAAC;UAEjB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACblE,OAAA,CAAC6E,WAAW;UAAAnB,QAAA,eACV1D,OAAA;YAAM8E,QAAQ,EAAEjC,YAAa;YAACW,SAAS,EAAC,WAAW;YAAAE,QAAA,gBACjD1D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAE,QAAA,gBACxB1D,OAAA;gBAAO+E,OAAO,EAAC,MAAM;gBAACvB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAEtD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA,CAACgF,KAAK;gBACJC,EAAE,EAAC,MAAM;gBACT1E,IAAI,EAAC,MAAM;gBACX4B,IAAI,EAAC,MAAM;gBACX+C,QAAQ;gBACRhD,KAAK,EAAE7B,QAAQ,CAACE,IAAK;gBACrB4E,QAAQ,EAAEnD,YAAa;gBACvBoD,WAAW,EAAC,sBAAsB;gBAClC5B,SAAS,EAAE3B,MAAM,CAACtB,IAAI,GAAG,oBAAoB,GAAG;cAAG;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EACDrC,MAAM,CAACtB,IAAI,iBAAIP,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAE,QAAA,EAAE7B,MAAM,CAACtB;cAAI;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAENlE,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAE,QAAA,gBACxB1D,OAAA;gBAAO+E,OAAO,EAAC,OAAO;gBAACvB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAEvD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA,CAACgF,KAAK;gBACJC,EAAE,EAAC,OAAO;gBACV1E,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,OAAO;gBACZkD,YAAY,EAAC,OAAO;gBACpBH,QAAQ;gBACRhD,KAAK,EAAE7B,QAAQ,CAACG,KAAM;gBACtB2E,QAAQ,EAAEnD,YAAa;gBACvBoD,WAAW,EAAC,kBAAkB;gBAC9B5B,SAAS,EAAE3B,MAAM,CAACrB,KAAK,GAAG,oBAAoB,GAAG;cAAG;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACDrC,MAAM,CAACrB,KAAK,iBAAIR,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAE,QAAA,EAAE7B,MAAM,CAACrB;cAAK;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAENlE,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAE,QAAA,gBACxB1D,OAAA;gBAAO+E,OAAO,EAAC,OAAO;gBAACvB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAEvD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA,CAACgF,KAAK;gBACJC,EAAE,EAAC,OAAO;gBACV1E,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,KAAK;gBACV+C,QAAQ;gBACRhD,KAAK,EAAE7B,QAAQ,CAACM,KAAM;gBACtBwE,QAAQ,EAAEnD,YAAa;gBACvBoD,WAAW,EAAC,mBAAmB;gBAC/B5B,SAAS,EAAE3B,MAAM,CAAClB,KAAK,GAAG,oBAAoB,GAAG;cAAG;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACDrC,MAAM,CAAClB,KAAK,iBAAIX,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAE,QAAA,EAAE7B,MAAM,CAAClB;cAAK;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAENlE,OAAA;cAAKwD,SAAS,EAAC,wBAAwB;cAAAE,QAAA,gBACrC1D,OAAA;gBAAKwD,SAAS,EAAC,WAAW;gBAAAE,QAAA,gBACxB1D,OAAA;kBAAO+E,OAAO,EAAC,aAAa;kBAACvB,SAAS,EAAC,qBAAqB;kBAAAE,QAAA,EAAC;gBAE7D;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlE,OAAA,CAACgF,KAAK;kBACJC,EAAE,EAAC,aAAa;kBAChB1E,IAAI,EAAC,aAAa;kBAClB4B,IAAI,EAAC,MAAM;kBACXD,KAAK,EAAE7B,QAAQ,CAACQ,WAAY;kBAC5BsE,QAAQ,EAAEnD;gBAAa;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlE,OAAA;gBAAKwD,SAAS,EAAC,WAAW;gBAAAE,QAAA,gBACxB1D,OAAA;kBAAO+E,OAAO,EAAC,QAAQ;kBAACvB,SAAS,EAAC,qBAAqB;kBAAAE,QAAA,EAAC;gBAExD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlE,OAAA;kBACEiF,EAAE,EAAC,QAAQ;kBACX1E,IAAI,EAAC,QAAQ;kBACb2B,KAAK,EAAE7B,QAAQ,CAACS,MAAO;kBACvBqE,QAAQ,EAAEnD,YAAa;kBACvBwB,SAAS,EAAC,4MAA4M;kBAAAE,QAAA,gBAEtN1D,OAAA;oBAAQkC,KAAK,EAAC,EAAE;oBAAAwB,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChClE,OAAA;oBAAQkC,KAAK,EAAC,MAAM;oBAAAwB,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClClE,OAAA;oBAAQkC,KAAK,EAAC,QAAQ;oBAAAwB,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtClE,OAAA;oBAAQkC,KAAK,EAAC,OAAO;oBAAAwB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClE,OAAA;oBAAQkC,KAAK,EAAC,mBAAmB;oBAAAwB,QAAA,EAAC;kBAAiB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlE,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAE,QAAA,gBACxB1D,OAAA;gBAAO+E,OAAO,EAAC,UAAU;gBAACvB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAE1D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA,CAACgF,KAAK;gBACJC,EAAE,EAAC,UAAU;gBACb1E,IAAI,EAAC,UAAU;gBACf4B,IAAI,EAAC,UAAU;gBACfkD,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRhD,KAAK,EAAE7B,QAAQ,CAACI,QAAS;gBACzB0E,QAAQ,EAAEnD,YAAa;gBACvBoD,WAAW,EAAC,qBAAqB;gBACjC5B,SAAS,EAAE3B,MAAM,CAACpB,QAAQ,GAAG,oBAAoB,GAAG;cAAG;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,EACDrC,MAAM,CAACpB,QAAQ,iBAAIT,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAE,QAAA,EAAE7B,MAAM,CAACpB;cAAQ;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eAENlE,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAE,QAAA,gBACxB1D,OAAA;gBAAO+E,OAAO,EAAC,iBAAiB;gBAACvB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAEjE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA,CAACgF,KAAK;gBACJC,EAAE,EAAC,iBAAiB;gBACpB1E,IAAI,EAAC,iBAAiB;gBACtB4B,IAAI,EAAC,UAAU;gBACfkD,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRhD,KAAK,EAAE7B,QAAQ,CAACK,eAAgB;gBAChCyE,QAAQ,EAAEnD,YAAa;gBACvBoD,WAAW,EAAC,uBAAuB;gBACnC5B,SAAS,EAAE3B,MAAM,CAACnB,eAAe,GAAG,oBAAoB,GAAG;cAAG;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EACDrC,MAAM,CAACnB,eAAe,iBAAIV,OAAA;gBAAGwD,SAAS,EAAC,0BAA0B;gBAAAE,QAAA,EAAE7B,MAAM,CAACnB;cAAe;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC,eAENlE,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAE,QAAA,gBACxB1D,OAAA;gBAAO+E,OAAO,EAAC,MAAM;gBAACvB,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EAAC;cAEtD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEiF,EAAE,EAAC,MAAM;gBACT1E,IAAI,EAAC,MAAM;gBACX2B,KAAK,EAAE7B,QAAQ,CAACO,IAAK;gBACrBuE,QAAQ,EAAEnD,YAAa;gBACvBwB,SAAS,EAAC,4MAA4M;gBAAAE,QAAA,gBAEtN1D,OAAA;kBAAQkC,KAAK,EAAC,SAAS;kBAAAwB,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxClE,OAAA;kBAAQkC,KAAK,EAAC,QAAQ;kBAAAwB,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtClE,OAAA;kBAAQkC,KAAK,EAAC,cAAc;kBAAAwB,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENlE,OAAA;cAAKwD,SAAS,EAAC,6BAA6B;cAAAE,QAAA,gBAC1C1D,OAAA;gBACEiF,EAAE,EAAC,OAAO;gBACV1E,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,UAAU;gBACf+C,QAAQ;gBACR1B,SAAS,EAAC;cAAiE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACFlE,OAAA;gBAAO+E,OAAO,EAAC,OAAO;gBAACvB,SAAS,EAAC,SAAS;gBAAAE,QAAA,GAAC,gBAC3B,EAAC,GAAG,eAClB1D,OAAA,CAACJ,IAAI;kBAAC0F,EAAE,EAAC,GAAG;kBAAC9B,SAAS,EAAC,oCAAoC;kBAAAE,QAAA,EAAC;gBAE5D;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACPlE,OAAA,CAACJ,IAAI;kBAAC0F,EAAE,EAAC,GAAG;kBAAC9B,SAAS,EAAC,oCAAoC;kBAAAE,QAAA,EAAC;gBAE5D;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENlE,OAAA,CAACuF,MAAM;cACLpD,IAAI,EAAC,QAAQ;cACbqD,QAAQ,EAAE7D,SAAU;cACpB6B,SAAS,EAAC,QAAQ;cAAAE,QAAA,EAEjB/B,SAAS,gBACR3B,OAAA;gBAAKwD,SAAS,EAAC,mBAAmB;gBAAAE,QAAA,gBAChC1D,OAAA;kBAAKwD,SAAS,EAAC;gBAAgE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uBAExF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CAtWID,QAAQ;EAAA,QA+BKJ,WAAW;AAAA;AAAA4F,EAAA,GA/BxBxF,QAAQ;AAwWd,eAAeA,QAAQ;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}