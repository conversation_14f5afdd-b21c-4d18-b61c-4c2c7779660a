import React from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { MapPin, Phone, Star, Bed, Clock, Shield } from 'lucide-react';

const HospitalDetails = () => {
  const { id } = useParams();

  // Mock hospital data
  const hospital = {
    id: 1,
    name: "City General Hospital",
    address: "123 Main Street, Downtown, NY 10001",
    contact: "+****************",
    email: "<EMAIL>",
    bedPricePerDay: 150.00,
    photos: ["/api/placeholder/800/400"],
    services: ["Emergency Care", "Surgery", "Cardiology", "Pediatrics", "Radiology", "Laboratory"],
    facilities: ["24/7 Emergency Services", "Modern ICU", "Advanced Surgical Suites", "Digital Radiology"],
    totalBeds: 200,
    availableBeds: 45,
    visitingHours: "9:00 AM - 8:00 PM",
    emergencyContact: "+1 (555) 911-HELP",
    description: "City General Hospital is a leading healthcare institution providing comprehensive medical services.",
    rating: 4.5
  };

  return (
    <div className="space-y-8">
      {/* Hospital Header */}
      <Card>
        <CardContent className="p-0">
          <img
            src={hospital.photos[0]}
            alt={hospital.name}
            className="w-full h-64 object-cover rounded-t-lg"
          />
          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className="text-3xl font-bold">{hospital.name}</h1>
                <div className="flex items-center mt-2">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-current" />
                    {hospital.rating}
                  </Badge>
                  <span className="ml-2 text-muted-foreground">({hospital.totalBeds} beds total)</span>
                </div>
              </div>
              <Button size="lg">
                Book Appointment
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">Contact Information</h3>
                <div className="space-y-2 text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    {hospital.address}
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    {hospital.contact}
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Emergency: {hospital.emergencyContact}
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Bed Availability</h3>
                <div className="bg-muted p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <span>Available Beds</span>
                    <span className="font-bold text-green-600">
                      {hospital.availableBeds}/{hospital.totalBeds}
                    </span>
                  </div>
                  <div className="w-full bg-background rounded-full h-3">
                    <div
                      className="bg-green-500 h-3 rounded-full"
                      style={{ width: `${(hospital.availableBeds / hospital.totalBeds) * 100}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    Bed Price: <span className="font-semibold">${hospital.bedPricePerDay}/day</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Description */}
      <Card>
        <CardHeader>
          <CardTitle>About {hospital.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground leading-relaxed">{hospital.description}</p>
        </CardContent>
      </Card>

      {/* Services and Facilities */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Medical Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              {hospital.services.map((service, index) => (
                <Badge key={index} variant="outline" className="justify-center">
                  {service}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Facilities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {hospital.facilities.map((facility, index) => (
                <div key={index} className="flex items-center">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>{facility}</span>
                </div>
              ))}
            </div>
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="text-sm flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <strong>Visiting Hours:</strong> {hospital.visitingHours}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default HospitalDetails;
