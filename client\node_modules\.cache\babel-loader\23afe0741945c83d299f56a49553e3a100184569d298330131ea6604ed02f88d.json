{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\DoctorDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styles from './DoctorDashboard.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorDashboard = () => {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n  const stats = [{\n    title: \"Today's Appointments\",\n    value: '8',\n    icon: '📅',\n    change: '+2 from yesterday'\n  }, {\n    title: 'This Week',\n    value: '32',\n    icon: '📊',\n    change: '+5 from last week'\n  }, {\n    title: 'Total Patients',\n    value: '156',\n    icon: '👥',\n    change: '+12 this month'\n  }, {\n    title: 'Pending Reviews',\n    value: '3',\n    icon: '⏳',\n    change: 'Needs attention'\n  }];\n  const appointments = [{\n    id: 1,\n    patient: '<PERSON><PERSON>',\n    time: '09:00',\n    reason: 'Regular checkup',\n    status: 'confirmed',\n    phone: '+91 98765 43210'\n  }, {\n    id: 2,\n    patient: '<PERSON><PERSON>',\n    time: '10:30',\n    reason: 'Follow-up consultation',\n    status: 'pending',\n    phone: '+91 87654 32109'\n  }, {\n    id: 3,\n    patient: 'Amit Singh',\n    time: '14:00',\n    reason: 'Chest pain evaluation',\n    status: 'confirmed',\n    phone: '+91 76543 21098'\n  }, {\n    id: 4,\n    patient: 'Sunita Patel',\n    time: '15:30',\n    reason: 'Medication review',\n    status: 'completed',\n    phone: '+91 65432 10987'\n  }, {\n    id: 5,\n    patient: 'Vikram Gupta',\n    time: '16:00',\n    reason: 'Blood pressure check',\n    status: 'confirmed',\n    phone: '+91 54321 09876'\n  }];\n  const doctorProfile = {\n    name: 'Dr. Meena Sharma',\n    specialty: 'Cardiologist',\n    experience: '15 years',\n    hospital: 'Apollo Hospital, Delhi',\n    phone: '+91 98765 00000',\n    email: '<EMAIL>',\n    availability: 'Available',\n    nextSlot: 'Tomorrow 9:00 AM'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.header,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.headerContent,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.greeting,\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: [\"Welcome back, \", doctorProfile.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [doctorProfile.specialty, \" \\u2022 \", doctorProfile.hospital]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.headerActions,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"\\uD83D\\uDCCB Patient Records\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-primary\",\n            children: \"\\uD83E\\uDE7A Update Availability\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.mainContent,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.statsGrid,\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.statCard,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statIcon,\n            children: stat.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statContent,\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: stat.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statValue,\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statChange,\n              children: stat.change\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.dashboardGrid,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.appointmentsSection,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.sectionHeader,\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Today's Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: selectedDate,\n              onChange: e => setSelectedDate(e.target.value),\n              className: \"input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.appointmentsList,\n            children: appointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.appointmentCard,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.appointmentHeader,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.patientInfo,\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: appointment.patient\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"\\uD83D\\uDCDE \", appointment.phone]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.appointmentTime,\n                  children: [\"\\uD83D\\uDD50 \", appointment.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.appointmentDetails,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: styles.reason,\n                  children: appointment.reason\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.appointmentFooter,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `${styles.statusBadge} ${styles[appointment.status]}`,\n                    children: appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.appointmentActions,\n                    children: [appointment.status === 'pending' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-accent\",\n                      children: \"\\u2713 Confirm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 102,\n                      columnNumber: 27\n                    }, this), appointment.status === 'confirmed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-primary\",\n                      children: \"\\uD83E\\uDE7A Start Visit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 27\n                    }, this), appointment.status === 'completed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-secondary\",\n                      children: \"\\uD83D\\uDCCB View Notes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-secondary\",\n                      children: \"\\uD83D\\uDCDE Call\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this)]\n            }, appointment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.profileSection,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.profileCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.profileHeader,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.profileAvatar,\n                children: \"\\uD83D\\uDC68\\u200D\\u2695\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.profileInfo,\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: doctorProfile.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: doctorProfile.specialty\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [doctorProfile.experience, \" experience\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.profileDetails,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.contactInfo,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"\\uD83D\\uDCE7 \", doctorProfile.email]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"\\uD83D\\uDCDE \", doctorProfile.phone]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"\\uD83C\\uDFE5 \", doctorProfile.hospital]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.availabilityStatus,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.statusHeader,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Availability Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.statusIndicator,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: styles.statusDot\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 142,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.scheduleInfo,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"Today: 9:00 AM - 6:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"Next slot: \", doctorProfile.nextSlot]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.quickActions,\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.actionButtons,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary w-full\",\n                children: \"\\uD83D\\uDC65 View All Patients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary w-full\",\n                children: \"\\uD83D\\uDCC5 Manage Schedule\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary w-full\",\n                children: \"\\uD83D\\uDC8A Prescription Pad\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary w-full\",\n                children: \"\\uD83D\\uDCCA Patient Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(DoctorDashboard, \"LngQ16GzRNiFRzmehLf5ShHFUs8=\");\n_c = DoctorDashboard;\nexport default DoctorDashboard;\nvar _c;\n$RefreshReg$(_c, \"DoctorDashboard\");", "map": {"version": 3, "names": ["React", "useState", "styles", "jsxDEV", "_jsxDEV", "DoctorDashboard", "_s", "selectedDate", "setSelectedDate", "Date", "toISOString", "split", "stats", "title", "value", "icon", "change", "appointments", "id", "patient", "time", "reason", "status", "phone", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "specialty", "experience", "hospital", "email", "availability", "nextSlot", "className", "container", "children", "header", "headerContent", "greeting", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "headerActions", "mainContent", "statsGrid", "map", "stat", "index", "statCard", "statIcon", "statContent", "statValue", "statChange", "dashboardGrid", "appointmentsSection", "section<PERSON><PERSON><PERSON>", "type", "onChange", "e", "target", "appointmentsList", "appointment", "appointmentCard", "<PERSON><PERSON><PERSON><PERSON>", "patientInfo", "appointmentTime", "appointmentDetails", "<PERSON>Footer", "statusBadge", "char<PERSON>t", "toUpperCase", "slice", "appointmentActions", "profileSection", "profileCard", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "profileInfo", "profileDetails", "contactInfo", "availabilityStatus", "statusH<PERSON>er", "statusIndicator", "statusDot", "scheduleInfo", "quickActions", "actionButtons", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/DoctorDashboard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styles from './DoctorDashboard.module.css';\n\nconst DoctorDashboard = () => {\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n\n  const stats = [\n    { title: \"Today's Appointments\", value: '8', icon: '📅', change: '+2 from yesterday' },\n    { title: 'This Week', value: '32', icon: '📊', change: '+5 from last week' },\n    { title: 'Total Patients', value: '156', icon: '👥', change: '+12 this month' },\n    { title: 'Pending Reviews', value: '3', icon: '⏳', change: 'Needs attention' },\n  ];\n\n  const appointments = [\n    { id: 1, patient: '<PERSON><PERSON>', time: '09:00', reason: 'Regular checkup', status: 'confirmed', phone: '+91 98765 43210' },\n    { id: 2, patient: '<PERSON><PERSON>', time: '10:30', reason: 'Follow-up consultation', status: 'pending', phone: '+91 87654 32109' },\n    { id: 3, patient: '<PERSON><PERSON>', time: '14:00', reason: 'Chest pain evaluation', status: 'confirmed', phone: '+91 76543 21098' },\n    { id: 4, patient: '<PERSON><PERSON>', time: '15:30', reason: 'Medication review', status: 'completed', phone: '+91 65432 10987' },\n    { id: 5, patient: 'Vikram Gupta', time: '16:00', reason: 'Blood pressure check', status: 'confirmed', phone: '+91 54321 09876' },\n  ];\n\n  const doctorProfile = {\n    name: 'Dr. Meena Sharma',\n    specialty: 'Cardiologist',\n    experience: '15 years',\n    hospital: 'Apollo Hospital, Delhi',\n    phone: '+91 98765 00000',\n    email: '<EMAIL>',\n    availability: 'Available',\n    nextSlot: 'Tomorrow 9:00 AM'\n  };\n\n  return (\n    <div className={styles.container}>\n      {/* Header */}\n      <div className={styles.header}>\n        <div className={styles.headerContent}>\n          <div className={styles.greeting}>\n            <h1>Welcome back, {doctorProfile.name}</h1>\n            <p>{doctorProfile.specialty} • {doctorProfile.hospital}</p>\n          </div>\n          <div className={styles.headerActions}>\n            <button className=\"btn-secondary\">\n              📋 Patient Records\n            </button>\n            <button className=\"btn-primary\">\n              🩺 Update Availability\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className={styles.mainContent}>\n        {/* Stats Grid */}\n        <div className={styles.statsGrid}>\n          {stats.map((stat, index) => (\n            <div key={index} className={styles.statCard}>\n              <div className={styles.statIcon}>{stat.icon}</div>\n              <div className={styles.statContent}>\n                <h3>{stat.title}</h3>\n                <div className={styles.statValue}>{stat.value}</div>\n                <div className={styles.statChange}>{stat.change}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className={styles.dashboardGrid}>\n          {/* Appointments Section */}\n          <div className={styles.appointmentsSection}>\n            <div className={styles.sectionHeader}>\n              <h2>Today's Appointments</h2>\n              <input\n                type=\"date\"\n                value={selectedDate}\n                onChange={(e) => setSelectedDate(e.target.value)}\n                className=\"input\"\n              />\n            </div>\n\n            <div className={styles.appointmentsList}>\n              {appointments.map((appointment) => (\n                <div key={appointment.id} className={styles.appointmentCard}>\n                  <div className={styles.appointmentHeader}>\n                    <div className={styles.patientInfo}>\n                      <h3>{appointment.patient}</h3>\n                      <p>📞 {appointment.phone}</p>\n                    </div>\n                    <div className={styles.appointmentTime}>\n                      🕐 {appointment.time}\n                    </div>\n                  </div>\n\n                  <div className={styles.appointmentDetails}>\n                    <p className={styles.reason}>{appointment.reason}</p>\n                    <div className={styles.appointmentFooter}>\n                      <span className={`${styles.statusBadge} ${styles[appointment.status]}`}>\n                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}\n                      </span>\n                      <div className={styles.appointmentActions}>\n                        {appointment.status === 'pending' && (\n                          <button className=\"btn-accent\">✓ Confirm</button>\n                        )}\n                        {appointment.status === 'confirmed' && (\n                          <button className=\"btn-primary\">🩺 Start Visit</button>\n                        )}\n                        {appointment.status === 'completed' && (\n                          <button className=\"btn-secondary\">📋 View Notes</button>\n                        )}\n                        <button className=\"btn-secondary\">📞 Call</button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Profile Section */}\n          <div className={styles.profileSection}>\n            <div className={styles.profileCard}>\n              <div className={styles.profileHeader}>\n                <div className={styles.profileAvatar}>👨‍⚕️</div>\n                <div className={styles.profileInfo}>\n                  <h3>{doctorProfile.name}</h3>\n                  <p>{doctorProfile.specialty}</p>\n                  <p>{doctorProfile.experience} experience</p>\n                </div>\n              </div>\n\n              <div className={styles.profileDetails}>\n                <div className={styles.contactInfo}>\n                  <div>📧 {doctorProfile.email}</div>\n                  <div>📞 {doctorProfile.phone}</div>\n                  <div>🏥 {doctorProfile.hospital}</div>\n                </div>\n\n                <div className={styles.availabilityStatus}>\n                  <div className={styles.statusHeader}>\n                    <span>Availability Status</span>\n                    <div className={styles.statusIndicator}>\n                      <div className={styles.statusDot}></div>\n                      <span>Available</span>\n                    </div>\n                  </div>\n                  <div className={styles.scheduleInfo}>\n                    <div>Today: 9:00 AM - 6:00 PM</div>\n                    <div>Next slot: {doctorProfile.nextSlot}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className={styles.quickActions}>\n              <h3>Quick Actions</h3>\n              <div className={styles.actionButtons}>\n                <button className=\"btn-secondary w-full\">\n                  👥 View All Patients\n                </button>\n                <button className=\"btn-secondary w-full\">\n                  📅 Manage Schedule\n                </button>\n                <button className=\"btn-secondary w-full\">\n                  💊 Prescription Pad\n                </button>\n                <button className=\"btn-secondary w-full\">\n                  📊 Patient Reports\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DoctorDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,IAAIQ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAExF,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,sBAAsB;IAAEC,KAAK,EAAE,GAAG;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAoB,CAAC,EACtF;IAAEH,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAoB,CAAC,EAC5E;IAAEH,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAiB,CAAC,EAC/E;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,GAAG;IAAEC,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAkB,CAAC,CAC/E;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,cAAc;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,iBAAiB;IAAEC,MAAM,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC3H;IAAEL,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,cAAc;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,wBAAwB;IAAEC,MAAM,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAChI;IAAEL,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,YAAY;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,uBAAuB;IAAEC,MAAM,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC/H;IAAEL,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,cAAc;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,mBAAmB;IAAEC,MAAM,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC7H;IAAEL,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAE,cAAc;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE,sBAAsB;IAAEC,MAAM,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAkB,CAAC,CACjI;EAED,MAAMC,aAAa,GAAG;IACpBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAE,cAAc;IACzBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,wBAAwB;IAClCL,KAAK,EAAE,iBAAiB;IACxBM,KAAK,EAAE,qBAAqB;IAC5BC,YAAY,EAAE,WAAW;IACzBC,QAAQ,EAAE;EACZ,CAAC;EAED,oBACE3B,OAAA;IAAK4B,SAAS,EAAE9B,MAAM,CAAC+B,SAAU;IAAAC,QAAA,gBAE/B9B,OAAA;MAAK4B,SAAS,EAAE9B,MAAM,CAACiC,MAAO;MAAAD,QAAA,eAC5B9B,OAAA;QAAK4B,SAAS,EAAE9B,MAAM,CAACkC,aAAc;QAAAF,QAAA,gBACnC9B,OAAA;UAAK4B,SAAS,EAAE9B,MAAM,CAACmC,QAAS;UAAAH,QAAA,gBAC9B9B,OAAA;YAAA8B,QAAA,GAAI,gBAAc,EAACV,aAAa,CAACC,IAAI;UAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3CrC,OAAA;YAAA8B,QAAA,GAAIV,aAAa,CAACE,SAAS,EAAC,UAAG,EAACF,aAAa,CAACI,QAAQ;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACNrC,OAAA;UAAK4B,SAAS,EAAE9B,MAAM,CAACwC,aAAc;UAAAR,QAAA,gBACnC9B,OAAA;YAAQ4B,SAAS,EAAC,eAAe;YAAAE,QAAA,EAAC;UAElC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrC,OAAA;YAAQ4B,SAAS,EAAC,aAAa;YAAAE,QAAA,EAAC;UAEhC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrC,OAAA;MAAK4B,SAAS,EAAE9B,MAAM,CAACyC,WAAY;MAAAT,QAAA,gBAEjC9B,OAAA;QAAK4B,SAAS,EAAE9B,MAAM,CAAC0C,SAAU;QAAAV,QAAA,EAC9BtB,KAAK,CAACiC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB3C,OAAA;UAAiB4B,SAAS,EAAE9B,MAAM,CAAC8C,QAAS;UAAAd,QAAA,gBAC1C9B,OAAA;YAAK4B,SAAS,EAAE9B,MAAM,CAAC+C,QAAS;YAAAf,QAAA,EAAEY,IAAI,CAAC/B;UAAI;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDrC,OAAA;YAAK4B,SAAS,EAAE9B,MAAM,CAACgD,WAAY;YAAAhB,QAAA,gBACjC9B,OAAA;cAAA8B,QAAA,EAAKY,IAAI,CAACjC;YAAK;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBrC,OAAA;cAAK4B,SAAS,EAAE9B,MAAM,CAACiD,SAAU;cAAAjB,QAAA,EAAEY,IAAI,CAAChC;YAAK;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDrC,OAAA;cAAK4B,SAAS,EAAE9B,MAAM,CAACkD,UAAW;cAAAlB,QAAA,EAAEY,IAAI,CAAC9B;YAAM;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA,GANEM,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENrC,OAAA;QAAK4B,SAAS,EAAE9B,MAAM,CAACmD,aAAc;QAAAnB,QAAA,gBAEnC9B,OAAA;UAAK4B,SAAS,EAAE9B,MAAM,CAACoD,mBAAoB;UAAApB,QAAA,gBACzC9B,OAAA;YAAK4B,SAAS,EAAE9B,MAAM,CAACqD,aAAc;YAAArB,QAAA,gBACnC9B,OAAA;cAAA8B,QAAA,EAAI;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BrC,OAAA;cACEoD,IAAI,EAAC,MAAM;cACX1C,KAAK,EAAEP,YAAa;cACpBkD,QAAQ,EAAGC,CAAC,IAAKlD,eAAe,CAACkD,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;cACjDkB,SAAS,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENrC,OAAA;YAAK4B,SAAS,EAAE9B,MAAM,CAAC0D,gBAAiB;YAAA1B,QAAA,EACrCjB,YAAY,CAAC4B,GAAG,CAAEgB,WAAW,iBAC5BzD,OAAA;cAA0B4B,SAAS,EAAE9B,MAAM,CAAC4D,eAAgB;cAAA5B,QAAA,gBAC1D9B,OAAA;gBAAK4B,SAAS,EAAE9B,MAAM,CAAC6D,iBAAkB;gBAAA7B,QAAA,gBACvC9B,OAAA;kBAAK4B,SAAS,EAAE9B,MAAM,CAAC8D,WAAY;kBAAA9B,QAAA,gBACjC9B,OAAA;oBAAA8B,QAAA,EAAK2B,WAAW,CAAC1C;kBAAO;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BrC,OAAA;oBAAA8B,QAAA,GAAG,eAAG,EAAC2B,WAAW,CAACtC,KAAK;kBAAA;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNrC,OAAA;kBAAK4B,SAAS,EAAE9B,MAAM,CAAC+D,eAAgB;kBAAA/B,QAAA,GAAC,eACnC,EAAC2B,WAAW,CAACzC,IAAI;gBAAA;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrC,OAAA;gBAAK4B,SAAS,EAAE9B,MAAM,CAACgE,kBAAmB;gBAAAhC,QAAA,gBACxC9B,OAAA;kBAAG4B,SAAS,EAAE9B,MAAM,CAACmB,MAAO;kBAAAa,QAAA,EAAE2B,WAAW,CAACxC;gBAAM;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrDrC,OAAA;kBAAK4B,SAAS,EAAE9B,MAAM,CAACiE,iBAAkB;kBAAAjC,QAAA,gBACvC9B,OAAA;oBAAM4B,SAAS,EAAE,GAAG9B,MAAM,CAACkE,WAAW,IAAIlE,MAAM,CAAC2D,WAAW,CAACvC,MAAM,CAAC,EAAG;oBAAAY,QAAA,EACpE2B,WAAW,CAACvC,MAAM,CAAC+C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGT,WAAW,CAACvC,MAAM,CAACiD,KAAK,CAAC,CAAC;kBAAC;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACPrC,OAAA;oBAAK4B,SAAS,EAAE9B,MAAM,CAACsE,kBAAmB;oBAAAtC,QAAA,GACvC2B,WAAW,CAACvC,MAAM,KAAK,SAAS,iBAC/BlB,OAAA;sBAAQ4B,SAAS,EAAC,YAAY;sBAAAE,QAAA,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACjD,EACAoB,WAAW,CAACvC,MAAM,KAAK,WAAW,iBACjClB,OAAA;sBAAQ4B,SAAS,EAAC,aAAa;sBAAAE,QAAA,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACvD,EACAoB,WAAW,CAACvC,MAAM,KAAK,WAAW,iBACjClB,OAAA;sBAAQ4B,SAAS,EAAC,eAAe;sBAAAE,QAAA,EAAC;oBAAa;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACxD,eACDrC,OAAA;sBAAQ4B,SAAS,EAAC,eAAe;sBAAAE,QAAA,EAAC;oBAAO;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA9BEoB,WAAW,CAAC3C,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+BnB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrC,OAAA;UAAK4B,SAAS,EAAE9B,MAAM,CAACuE,cAAe;UAAAvC,QAAA,gBACpC9B,OAAA;YAAK4B,SAAS,EAAE9B,MAAM,CAACwE,WAAY;YAAAxC,QAAA,gBACjC9B,OAAA;cAAK4B,SAAS,EAAE9B,MAAM,CAACyE,aAAc;cAAAzC,QAAA,gBACnC9B,OAAA;gBAAK4B,SAAS,EAAE9B,MAAM,CAAC0E,aAAc;gBAAA1C,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjDrC,OAAA;gBAAK4B,SAAS,EAAE9B,MAAM,CAAC2E,WAAY;gBAAA3C,QAAA,gBACjC9B,OAAA;kBAAA8B,QAAA,EAAKV,aAAa,CAACC;gBAAI;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7BrC,OAAA;kBAAA8B,QAAA,EAAIV,aAAa,CAACE;gBAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChCrC,OAAA;kBAAA8B,QAAA,GAAIV,aAAa,CAACG,UAAU,EAAC,aAAW;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAK4B,SAAS,EAAE9B,MAAM,CAAC4E,cAAe;cAAA5C,QAAA,gBACpC9B,OAAA;gBAAK4B,SAAS,EAAE9B,MAAM,CAAC6E,WAAY;gBAAA7C,QAAA,gBACjC9B,OAAA;kBAAA8B,QAAA,GAAK,eAAG,EAACV,aAAa,CAACK,KAAK;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnCrC,OAAA;kBAAA8B,QAAA,GAAK,eAAG,EAACV,aAAa,CAACD,KAAK;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnCrC,OAAA;kBAAA8B,QAAA,GAAK,eAAG,EAACV,aAAa,CAACI,QAAQ;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAENrC,OAAA;gBAAK4B,SAAS,EAAE9B,MAAM,CAAC8E,kBAAmB;gBAAA9C,QAAA,gBACxC9B,OAAA;kBAAK4B,SAAS,EAAE9B,MAAM,CAAC+E,YAAa;kBAAA/C,QAAA,gBAClC9B,OAAA;oBAAA8B,QAAA,EAAM;kBAAmB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCrC,OAAA;oBAAK4B,SAAS,EAAE9B,MAAM,CAACgF,eAAgB;oBAAAhD,QAAA,gBACrC9B,OAAA;sBAAK4B,SAAS,EAAE9B,MAAM,CAACiF;oBAAU;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxCrC,OAAA;sBAAA8B,QAAA,EAAM;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrC,OAAA;kBAAK4B,SAAS,EAAE9B,MAAM,CAACkF,YAAa;kBAAAlD,QAAA,gBAClC9B,OAAA;oBAAA8B,QAAA,EAAK;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnCrC,OAAA;oBAAA8B,QAAA,GAAK,aAAW,EAACV,aAAa,CAACO,QAAQ;kBAAA;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrC,OAAA;YAAK4B,SAAS,EAAE9B,MAAM,CAACmF,YAAa;YAAAnD,QAAA,gBAClC9B,OAAA;cAAA8B,QAAA,EAAI;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBrC,OAAA;cAAK4B,SAAS,EAAE9B,MAAM,CAACoF,aAAc;cAAApD,QAAA,gBACnC9B,OAAA;gBAAQ4B,SAAS,EAAC,sBAAsB;gBAAAE,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrC,OAAA;gBAAQ4B,SAAS,EAAC,sBAAsB;gBAAAE,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrC,OAAA;gBAAQ4B,SAAS,EAAC,sBAAsB;gBAAAE,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrC,OAAA;gBAAQ4B,SAAS,EAAC,sBAAsB;gBAAAE,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA5KID,eAAe;AAAAkF,EAAA,GAAflF,eAAe;AA8KrB,eAAeA,eAAe;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}