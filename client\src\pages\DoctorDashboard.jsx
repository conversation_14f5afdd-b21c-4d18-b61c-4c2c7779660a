import React, { useState } from 'react';
import styles from './DoctorDashboard.module.css';

const DoctorDashboard = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  const stats = [
    { title: "Today's Appointments", value: '8', icon: '📅', change: '+2 from yesterday' },
    { title: 'This Week', value: '32', icon: '📊', change: '+5 from last week' },
    { title: 'Total Patients', value: '156', icon: '👥', change: '+12 this month' },
    { title: 'Pending Reviews', value: '3', icon: '⏳', change: 'Needs attention' },
  ];

  const appointments = [
    { id: 1, patient: '<PERSON><PERSON>', time: '09:00', reason: 'Regular checkup', status: 'confirmed', phone: '+91 98765 43210' },
    { id: 2, patient: '<PERSON><PERSON>', time: '10:30', reason: 'Follow-up consultation', status: 'pending', phone: '+91 87654 32109' },
    { id: 3, patient: '<PERSON><PERSON>', time: '14:00', reason: 'Chest pain evaluation', status: 'confirmed', phone: '+91 76543 21098' },
    { id: 4, patient: '<PERSON><PERSON>', time: '15:30', reason: 'Medication review', status: 'completed', phone: '+91 65432 10987' },
    { id: 5, patient: 'Vikram Gupta', time: '16:00', reason: 'Blood pressure check', status: 'confirmed', phone: '+91 54321 09876' },
  ];

  const doctorProfile = {
    name: 'Dr. Meena Sharma',
    specialty: 'Cardiologist',
    experience: '15 years',
    hospital: 'Apollo Hospital, Delhi',
    phone: '+91 98765 00000',
    email: '<EMAIL>',
    availability: 'Available',
    nextSlot: 'Tomorrow 9:00 AM'
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.greeting}>
            <h1>Welcome back, {doctorProfile.name}</h1>
            <p>{doctorProfile.specialty} • {doctorProfile.hospital}</p>
          </div>
          <div className={styles.headerActions}>
            <button className="btn-secondary">
              📋 Patient Records
            </button>
            <button className="btn-primary">
              🩺 Update Availability
            </button>
          </div>
        </div>
      </div>

      <div className={styles.mainContent}>
        {/* Stats Grid */}
        <div className={styles.statsGrid}>
          {stats.map((stat, index) => (
            <div key={index} className={styles.statCard}>
              <div className={styles.statIcon}>{stat.icon}</div>
              <div className={styles.statContent}>
                <h3>{stat.title}</h3>
                <div className={styles.statValue}>{stat.value}</div>
                <div className={styles.statChange}>{stat.change}</div>
              </div>
            </div>
          ))}
        </div>

        <div className={styles.dashboardGrid}>
          {/* Appointments Section */}
          <div className={styles.appointmentsSection}>
            <div className={styles.sectionHeader}>
              <h2>Today's Appointments</h2>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="input"
              />
            </div>

            <div className={styles.appointmentsList}>
              {appointments.map((appointment) => (
                <div key={appointment.id} className={styles.appointmentCard}>
                  <div className={styles.appointmentHeader}>
                    <div className={styles.patientInfo}>
                      <h3>{appointment.patient}</h3>
                      <p>📞 {appointment.phone}</p>
                    </div>
                    <div className={styles.appointmentTime}>
                      🕐 {appointment.time}
                    </div>
                  </div>

                  <div className={styles.appointmentDetails}>
                    <p className={styles.reason}>{appointment.reason}</p>
                    <div className={styles.appointmentFooter}>
                      <span className={`${styles.statusBadge} ${styles[appointment.status]}`}>
                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                      </span>
                      <div className={styles.appointmentActions}>
                        {appointment.status === 'pending' && (
                          <button className="btn-accent">✓ Confirm</button>
                        )}
                        {appointment.status === 'confirmed' && (
                          <button className="btn-primary">🩺 Start Visit</button>
                        )}
                        {appointment.status === 'completed' && (
                          <button className="btn-secondary">📋 View Notes</button>
                        )}
                        <button className="btn-secondary">📞 Call</button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Profile Section */}
          <div className={styles.profileSection}>
            <div className={styles.profileCard}>
              <div className={styles.profileHeader}>
                <div className={styles.profileAvatar}>👨‍⚕️</div>
                <div className={styles.profileInfo}>
                  <h3>{doctorProfile.name}</h3>
                  <p>{doctorProfile.specialty}</p>
                  <p>{doctorProfile.experience} experience</p>
                </div>
              </div>

              <div className={styles.profileDetails}>
                <div className={styles.contactInfo}>
                  <div>📧 {doctorProfile.email}</div>
                  <div>📞 {doctorProfile.phone}</div>
                  <div>🏥 {doctorProfile.hospital}</div>
                </div>

                <div className={styles.availabilityStatus}>
                  <div className={styles.statusHeader}>
                    <span>Availability Status</span>
                    <div className={styles.statusIndicator}>
                      <div className={styles.statusDot}></div>
                      <span>Available</span>
                    </div>
                  </div>
                  <div className={styles.scheduleInfo}>
                    <div>Today: 9:00 AM - 6:00 PM</div>
                    <div>Next slot: {doctorProfile.nextSlot}</div>
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.quickActions}>
              <h3>Quick Actions</h3>
              <div className={styles.actionButtons}>
                <button className="btn-secondary w-full">
                  👥 View All Patients
                </button>
                <button className="btn-secondary w-full">
                  📅 Manage Schedule
                </button>
                <button className="btn-secondary w-full">
                  💊 Prescription Pad
                </button>
                <button className="btn-secondary w-full">
                  📊 Patient Reports
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorDashboard;
