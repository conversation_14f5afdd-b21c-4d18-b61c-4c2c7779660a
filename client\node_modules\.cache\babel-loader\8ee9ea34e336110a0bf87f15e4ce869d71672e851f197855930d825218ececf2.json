{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styles from './AdminDashboard.module.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [newHospital, setNewHospital] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: '',\n    city: '',\n    licenseNumber: '',\n    type: 'private'\n  });\n  const [newUser, setNewUser] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    role: 'doctor',\n    hospital: '',\n    specialty: ''\n  });\n  const [hospitals, setHospitals] = useState([{\n    id: 1,\n    name: 'Apollo Hospital Delhi',\n    city: 'New Delhi',\n    type: 'Private',\n    status: 'Active',\n    doctors: 45,\n    patients: 2500,\n    rating: 4.5\n  }, {\n    id: 2,\n    name: 'Fortis Healthcare Bangalore',\n    city: 'Bangalore',\n    type: 'Private',\n    status: 'Active',\n    doctors: 38,\n    patients: 2100,\n    rating: 4.3\n  }, {\n    id: 3,\n    name: 'Max Super Speciality Mumbai',\n    city: 'Mumbai',\n    type: 'Private',\n    status: 'Active',\n    doctors: 52,\n    patients: 2800,\n    rating: 4.6\n  }, {\n    id: 4,\n    name: 'AIIMS Delhi',\n    city: 'New Delhi',\n    type: 'Government',\n    status: 'Active',\n    doctors: 65,\n    patients: 3200,\n    rating: 4.4\n  }, {\n    id: 5,\n    name: 'Medanta Gurgaon',\n    city: 'Gurgaon',\n    type: 'Private',\n    status: 'Pending',\n    doctors: 42,\n    patients: 1900,\n    rating: 4.2\n  }]);\n  const [users, setUsers] = useState([{\n    id: 1,\n    name: 'Dr. Meena Sharma',\n    email: '<EMAIL>',\n    role: 'Doctor',\n    hospital: 'Apollo Hospital Delhi',\n    status: 'Active',\n    specialty: 'Cardiology'\n  }, {\n    id: 2,\n    name: 'Dr. Arjun Rao',\n    email: '<EMAIL>',\n    role: 'Doctor',\n    hospital: 'Fortis Healthcare Bangalore',\n    status: 'Active',\n    specialty: 'Orthopedics'\n  }, {\n    id: 3,\n    name: 'Priya Singh',\n    email: '<EMAIL>',\n    role: 'Receptionist',\n    hospital: 'Apollo Hospital Delhi',\n    status: 'Active',\n    specialty: ''\n  }, {\n    id: 4,\n    name: 'Dr. Sunil Patel',\n    email: '<EMAIL>',\n    role: 'Doctor',\n    hospital: 'Max Super Speciality Mumbai',\n    status: 'Active',\n    specialty: 'General Medicine'\n  }, {\n    id: 5,\n    name: 'Rajesh Kumar',\n    email: '<EMAIL>',\n    role: 'Admin',\n    hospital: 'Medanta Gurgaon',\n    status: 'Inactive',\n    specialty: ''\n  }]);\n  const stats = [{\n    title: 'Total Hospitals',\n    value: hospitals.length.toString(),\n    icon: '🏥',\n    change: '+5 this month'\n  }, {\n    title: 'Active Doctors',\n    value: users.filter(u => u.role === 'Doctor' && u.status === 'Active').length.toString(),\n    icon: '👨‍⚕️',\n    change: '+12 this week'\n  }, {\n    title: 'Total Users',\n    value: users.length.toString(),\n    icon: '👥',\n    change: '+8 this week'\n  }, {\n    title: 'Pending Approvals',\n    value: hospitals.filter(h => h.status === 'Pending').length.toString(),\n    icon: '📅',\n    change: '2 new requests'\n  }];\n\n  // Handler functions\n  const handleAddHospital = () => {\n    if (!newHospital.name || !newHospital.email || !newHospital.licenseNumber) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    const hospital = {\n      id: hospitals.length + 1,\n      ...newHospital,\n      status: 'Pending',\n      doctors: 0,\n      patients: 0,\n      rating: 0\n    };\n    setHospitals([...hospitals, hospital]);\n    setNewHospital({\n      name: '',\n      email: '',\n      phone: '',\n      address: '',\n      city: '',\n      licenseNumber: '',\n      type: 'private'\n    });\n    setShowModal(false);\n    alert('Hospital added successfully!');\n  };\n  const handleAddUser = () => {\n    if (!newUser.name || !newUser.email || !newUser.role) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    const user = {\n      id: users.length + 1,\n      ...newUser,\n      status: 'Active'\n    };\n    setUsers([...users, user]);\n    setNewUser({\n      name: '',\n      email: '',\n      phone: '',\n      role: 'doctor',\n      hospital: '',\n      specialty: ''\n    });\n    setShowModal(false);\n    alert('User added successfully!');\n  };\n  const approveHospital = hospitalId => {\n    setHospitals(hospitals.map(h => h.id === hospitalId ? {\n      ...h,\n      status: 'Active'\n    } : h));\n    alert('Hospital approved successfully!');\n  };\n  const suspendHospital = hospitalId => {\n    setHospitals(hospitals.map(h => h.id === hospitalId ? {\n      ...h,\n      status: 'Suspended'\n    } : h));\n    alert('Hospital suspended!');\n  };\n  const toggleUserStatus = userId => {\n    setUsers(users.map(u => u.id === userId ? {\n      ...u,\n      status: u.status === 'Active' ? 'Inactive' : 'Active'\n    } : u));\n    alert('User status updated!');\n  };\n  const deleteUser = userId => {\n    if (window.confirm('Are you sure you want to delete this user?')) {\n      setUsers(users.filter(u => u.id !== userId));\n      alert('User deleted successfully!');\n    }\n  };\n  const openModal = (type, item = null) => {\n    setModalType(type);\n    setSelectedItem(item);\n    setShowModal(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.sidebar,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.sidebarHeader,\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83C\\uDFE5 Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: styles.sidebarNav,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'overview' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('overview'),\n          children: \"\\uD83D\\uDCCA Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'hospitals' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('hospitals'),\n          children: \"\\uD83C\\uDFE5 Hospitals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'users' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('users'),\n          children: \"\\uD83D\\uDC65 Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'appointments' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('appointments'),\n          children: \"\\uD83D\\uDCC5 Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `${styles.navItem} ${activeTab === 'settings' ? styles.navItemActive : ''}`,\n          onClick: () => setActiveTab('settings'),\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.mainContent,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.header,\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.headerActions,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-accent\",\n            children: \"\\u2795 Add Hospital\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            children: \"\\uD83D\\uDC64 Add User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.statsGrid,\n          children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statIcon,\n              children: stat.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statContent,\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: stat.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.statValue,\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.statChange,\n                children: stat.change\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.recentActivities,\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Recent Activities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.activitiesList,\n            children: [{\n              action: 'New appointment booked',\n              user: 'John Doe',\n              time: '2 minutes ago',\n              type: 'appointment'\n            }, {\n              action: 'Doctor updated availability',\n              user: 'Dr. Sarah Johnson',\n              time: '15 minutes ago',\n              type: 'doctor'\n            }, {\n              action: 'New hospital added',\n              user: 'Admin',\n              time: '1 hour ago',\n              type: 'hospital'\n            }, {\n              action: 'Patient registration',\n              user: 'Jane Smith',\n              time: '2 hours ago',\n              type: 'user'\n            }].map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.activityItem,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.activityIcon,\n                children: [activity.type === 'appointment' && '📅', activity.type === 'doctor' && '👩‍⚕️', activity.type === 'hospital' && '🏥', activity.type === 'user' && '👤']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.activityContent,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: styles.activityAction,\n                  children: activity.action\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: styles.activityMeta,\n                  children: [\"by \", activity.user, \" \\u2022 \", activity.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), activeTab === 'hospitals' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.tableContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Manage Hospitals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.table,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableHeader,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Hospital Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"City\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Beds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this), [{\n            name: 'Apollo Hospital',\n            city: 'Delhi',\n            beds: 500,\n            status: 'Active'\n          }, {\n            name: 'Fortis Hospital',\n            city: 'Bangalore',\n            beds: 400,\n            status: 'Active'\n          }, {\n            name: 'Max Healthcare',\n            city: 'Mumbai',\n            beds: 350,\n            status: 'Pending'\n          }, {\n            name: 'AIIMS',\n            city: 'New Delhi',\n            beds: 2500,\n            status: 'Active'\n          }].map((hospital, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableRow,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: hospital.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: hospital.city\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: hospital.beds\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `${styles.statusBadge} ${hospital.status === 'Active' ? styles.statusActive : styles.statusPending}`,\n                children: hospital.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.tableActions,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary\",\n                children: \"Edit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-accent\",\n                children: \"View\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), activeTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.tableContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Manage Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.table,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableHeader,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Hospital\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), [{\n            name: 'Dr. Meena Sharma',\n            role: 'Doctor',\n            hospital: 'Apollo Hospital',\n            status: 'Active'\n          }, {\n            name: 'John Doe',\n            role: 'Patient',\n            hospital: '-',\n            status: 'Active'\n          }, {\n            name: 'Sarah Wilson',\n            role: 'Receptionist',\n            hospital: 'Fortis Hospital',\n            status: 'Active'\n          }, {\n            name: 'Dr. Arjun Rao',\n            role: 'Doctor',\n            hospital: 'Max Healthcare',\n            status: 'Pending'\n          }].map((user, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.tableRow,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: user.hospital\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `${styles.statusBadge} ${user.status === 'Active' ? styles.statusActive : styles.statusPending}`,\n                children: user.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.tableActions,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary\",\n                children: \"Edit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-accent\",\n                children: \"View\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"iK0y+u0TugbS5p51Riy3YottnIY=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "_s", "activeTab", "setActiveTab", "showModal", "setShowModal", "modalType", "setModalType", "selectedItem", "setSelectedItem", "newHospital", "setNewHospital", "name", "email", "phone", "address", "city", "licenseNumber", "type", "newUser", "setNewUser", "role", "hospital", "specialty", "hospitals", "setHospitals", "id", "status", "doctors", "patients", "rating", "users", "setUsers", "stats", "title", "value", "length", "toString", "icon", "change", "filter", "u", "h", "handleAddHospital", "alert", "handleAddUser", "user", "approveHospital", "hospitalId", "map", "suspendHospital", "toggleUserStatus", "userId", "deleteUser", "window", "confirm", "openModal", "item", "className", "container", "children", "sidebar", "sidebarHeader", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sidebarNav", "navItem", "navItemActive", "onClick", "mainContent", "header", "headerActions", "statsGrid", "stat", "index", "statCard", "statIcon", "statContent", "statValue", "statChange", "recentActivities", "activitiesList", "action", "time", "activity", "activityItem", "activityIcon", "activityContent", "activityAction", "activityMeta", "tableContainer", "table", "tableHeader", "beds", "tableRow", "statusBadge", "statusActive", "statusPending", "tableActions", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styles from './AdminDashboard.module.css';\n\nconst AdminDashboard = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [newHospital, setNewHospital] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: '',\n    city: '',\n    licenseNumber: '',\n    type: 'private'\n  });\n  const [newUser, setNewUser] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    role: 'doctor',\n    hospital: '',\n    specialty: ''\n  });\n\n  const [hospitals, setHospitals] = useState([\n    { id: 1, name: 'Apollo Hospital Delhi', city: 'New Delhi', type: 'Private', status: 'Active', doctors: 45, patients: 2500, rating: 4.5 },\n    { id: 2, name: 'Fortis Healthcare Bangalore', city: 'Bangalore', type: 'Private', status: 'Active', doctors: 38, patients: 2100, rating: 4.3 },\n    { id: 3, name: 'Max Super Speciality Mumbai', city: 'Mumbai', type: 'Private', status: 'Active', doctors: 52, patients: 2800, rating: 4.6 },\n    { id: 4, name: 'AIIMS Delhi', city: 'New Delhi', type: 'Government', status: 'Active', doctors: 65, patients: 3200, rating: 4.4 },\n    { id: 5, name: 'Medanta Gurgaon', city: 'Gurgaon', type: 'Private', status: 'Pending', doctors: 42, patients: 1900, rating: 4.2 }\n  ]);\n\n  const [users, setUsers] = useState([\n    { id: 1, name: 'Dr. Meena Sharma', email: '<EMAIL>', role: 'Doctor', hospital: 'Apollo Hospital Delhi', status: 'Active', specialty: 'Cardiology' },\n    { id: 2, name: 'Dr. Arjun Rao', email: '<EMAIL>', role: 'Doctor', hospital: 'Fortis Healthcare Bangalore', status: 'Active', specialty: 'Orthopedics' },\n    { id: 3, name: 'Priya Singh', email: '<EMAIL>', role: 'Receptionist', hospital: 'Apollo Hospital Delhi', status: 'Active', specialty: '' },\n    { id: 4, name: 'Dr. Sunil Patel', email: '<EMAIL>', role: 'Doctor', hospital: 'Max Super Speciality Mumbai', status: 'Active', specialty: 'General Medicine' },\n    { id: 5, name: 'Rajesh Kumar', email: '<EMAIL>', role: 'Admin', hospital: 'Medanta Gurgaon', status: 'Inactive', specialty: '' }\n  ]);\n\n  const stats = [\n    { title: 'Total Hospitals', value: hospitals.length.toString(), icon: '🏥', change: '+5 this month' },\n    { title: 'Active Doctors', value: users.filter(u => u.role === 'Doctor' && u.status === 'Active').length.toString(), icon: '👨‍⚕️', change: '+12 this week' },\n    { title: 'Total Users', value: users.length.toString(), icon: '👥', change: '+8 this week' },\n    { title: 'Pending Approvals', value: hospitals.filter(h => h.status === 'Pending').length.toString(), icon: '📅', change: '2 new requests' },\n  ];\n\n  // Handler functions\n  const handleAddHospital = () => {\n    if (!newHospital.name || !newHospital.email || !newHospital.licenseNumber) {\n      alert('Please fill in all required fields');\n      return;\n    }\n\n    const hospital = {\n      id: hospitals.length + 1,\n      ...newHospital,\n      status: 'Pending',\n      doctors: 0,\n      patients: 0,\n      rating: 0\n    };\n\n    setHospitals([...hospitals, hospital]);\n    setNewHospital({\n      name: '',\n      email: '',\n      phone: '',\n      address: '',\n      city: '',\n      licenseNumber: '',\n      type: 'private'\n    });\n    setShowModal(false);\n    alert('Hospital added successfully!');\n  };\n\n  const handleAddUser = () => {\n    if (!newUser.name || !newUser.email || !newUser.role) {\n      alert('Please fill in all required fields');\n      return;\n    }\n\n    const user = {\n      id: users.length + 1,\n      ...newUser,\n      status: 'Active'\n    };\n\n    setUsers([...users, user]);\n    setNewUser({\n      name: '',\n      email: '',\n      phone: '',\n      role: 'doctor',\n      hospital: '',\n      specialty: ''\n    });\n    setShowModal(false);\n    alert('User added successfully!');\n  };\n\n  const approveHospital = (hospitalId) => {\n    setHospitals(hospitals.map(h =>\n      h.id === hospitalId ? { ...h, status: 'Active' } : h\n    ));\n    alert('Hospital approved successfully!');\n  };\n\n  const suspendHospital = (hospitalId) => {\n    setHospitals(hospitals.map(h =>\n      h.id === hospitalId ? { ...h, status: 'Suspended' } : h\n    ));\n    alert('Hospital suspended!');\n  };\n\n  const toggleUserStatus = (userId) => {\n    setUsers(users.map(u =>\n      u.id === userId ? { ...u, status: u.status === 'Active' ? 'Inactive' : 'Active' } : u\n    ));\n    alert('User status updated!');\n  };\n\n  const deleteUser = (userId) => {\n    if (window.confirm('Are you sure you want to delete this user?')) {\n      setUsers(users.filter(u => u.id !== userId));\n      alert('User deleted successfully!');\n    }\n  };\n\n  const openModal = (type, item = null) => {\n    setModalType(type);\n    setSelectedItem(item);\n    setShowModal(true);\n  };\n\n  return (\n    <div className={styles.container}>\n      {/* Sidebar */}\n      <div className={styles.sidebar}>\n        <div className={styles.sidebarHeader}>\n          <h2>🏥 Admin Panel</h2>\n        </div>\n        <nav className={styles.sidebarNav}>\n          <button\n            className={`${styles.navItem} ${activeTab === 'overview' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('overview')}\n          >\n            📊 Overview\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'hospitals' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('hospitals')}\n          >\n            🏥 Hospitals\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'users' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('users')}\n          >\n            👥 Users\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'appointments' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('appointments')}\n          >\n            📅 Appointments\n          </button>\n          <button\n            className={`${styles.navItem} ${activeTab === 'settings' ? styles.navItemActive : ''}`}\n            onClick={() => setActiveTab('settings')}\n          >\n            ⚙️ Settings\n          </button>\n        </nav>\n      </div>\n\n      {/* Main Content */}\n      <div className={styles.mainContent}>\n        <div className={styles.header}>\n          <h1>Admin Dashboard</h1>\n          <div className={styles.headerActions}>\n            <button className=\"btn-accent\">\n              ➕ Add Hospital\n            </button>\n            <button className=\"btn-secondary\">\n              👤 Add User\n            </button>\n          </div>\n        </div>\n\n        {activeTab === 'overview' && (\n          <>\n            {/* Stats Cards */}\n            <div className={styles.statsGrid}>\n              {stats.map((stat, index) => (\n                <div key={index} className={styles.statCard}>\n                  <div className={styles.statIcon}>{stat.icon}</div>\n                  <div className={styles.statContent}>\n                    <h3>{stat.title}</h3>\n                    <div className={styles.statValue}>{stat.value}</div>\n                    <div className={styles.statChange}>{stat.change}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Recent Activities */}\n            <div className={styles.recentActivities}>\n              <h2>Recent Activities</h2>\n              <div className={styles.activitiesList}>\n                {[\n                  { action: 'New appointment booked', user: 'John Doe', time: '2 minutes ago', type: 'appointment' },\n                  { action: 'Doctor updated availability', user: 'Dr. Sarah Johnson', time: '15 minutes ago', type: 'doctor' },\n                  { action: 'New hospital added', user: 'Admin', time: '1 hour ago', type: 'hospital' },\n                  { action: 'Patient registration', user: 'Jane Smith', time: '2 hours ago', type: 'user' },\n                ].map((activity, index) => (\n                  <div key={index} className={styles.activityItem}>\n                    <div className={styles.activityIcon}>\n                      {activity.type === 'appointment' && '📅'}\n                      {activity.type === 'doctor' && '👩‍⚕️'}\n                      {activity.type === 'hospital' && '🏥'}\n                      {activity.type === 'user' && '👤'}\n                    </div>\n                    <div className={styles.activityContent}>\n                      <p className={styles.activityAction}>{activity.action}</p>\n                      <p className={styles.activityMeta}>by {activity.user} • {activity.time}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </>\n        )}\n\n        {activeTab === 'hospitals' && (\n          <div className={styles.tableContainer}>\n            <h2>Manage Hospitals</h2>\n            <div className={styles.table}>\n              <div className={styles.tableHeader}>\n                <div>Hospital Name</div>\n                <div>City</div>\n                <div>Beds</div>\n                <div>Status</div>\n                <div>Actions</div>\n              </div>\n              {[\n                { name: 'Apollo Hospital', city: 'Delhi', beds: 500, status: 'Active' },\n                { name: 'Fortis Hospital', city: 'Bangalore', beds: 400, status: 'Active' },\n                { name: 'Max Healthcare', city: 'Mumbai', beds: 350, status: 'Pending' },\n                { name: 'AIIMS', city: 'New Delhi', beds: 2500, status: 'Active' }\n              ].map((hospital, index) => (\n                <div key={index} className={styles.tableRow}>\n                  <div>{hospital.name}</div>\n                  <div>{hospital.city}</div>\n                  <div>{hospital.beds}</div>\n                  <div>\n                    <span className={`${styles.statusBadge} ${hospital.status === 'Active' ? styles.statusActive : styles.statusPending}`}>\n                      {hospital.status}\n                    </span>\n                  </div>\n                  <div className={styles.tableActions}>\n                    <button className=\"btn-secondary\">Edit</button>\n                    <button className=\"btn-accent\">View</button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'users' && (\n          <div className={styles.tableContainer}>\n            <h2>Manage Users</h2>\n            <div className={styles.table}>\n              <div className={styles.tableHeader}>\n                <div>Name</div>\n                <div>Role</div>\n                <div>Hospital</div>\n                <div>Status</div>\n                <div>Actions</div>\n              </div>\n              {[\n                { name: 'Dr. Meena Sharma', role: 'Doctor', hospital: 'Apollo Hospital', status: 'Active' },\n                { name: 'John Doe', role: 'Patient', hospital: '-', status: 'Active' },\n                { name: 'Sarah Wilson', role: 'Receptionist', hospital: 'Fortis Hospital', status: 'Active' },\n                { name: 'Dr. Arjun Rao', role: 'Doctor', hospital: 'Max Healthcare', status: 'Pending' }\n              ].map((user, index) => (\n                <div key={index} className={styles.tableRow}>\n                  <div>{user.name}</div>\n                  <div>{user.role}</div>\n                  <div>{user.hospital}</div>\n                  <div>\n                    <span className={`${styles.statusBadge} ${user.status === 'Active' ? styles.statusActive : styles.statusPending}`}>\n                      {user.status}\n                    </span>\n                  </div>\n                  <div className={styles.tableActions}>\n                    <button className=\"btn-secondary\">Edit</button>\n                    <button className=\"btn-accent\">View</button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC;IAC7CkB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,aAAa,EAAE,EAAE;IACjBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC;IACrCkB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTO,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,CACzC;IAAEgC,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,uBAAuB;IAAEI,IAAI,EAAE,WAAW;IAAEE,IAAI,EAAE,SAAS;IAAES,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EACxI;IAAEJ,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,6BAA6B;IAAEI,IAAI,EAAE,WAAW;IAAEE,IAAI,EAAE,SAAS;IAAES,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC9I;IAAEJ,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,6BAA6B;IAAEI,IAAI,EAAE,QAAQ;IAAEE,IAAI,EAAE,SAAS;IAAES,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC3I;IAAEJ,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,aAAa;IAAEI,IAAI,EAAE,WAAW;IAAEE,IAAI,EAAE,YAAY;IAAES,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EACjI;IAAEJ,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,iBAAiB;IAAEI,IAAI,EAAE,SAAS;IAAEE,IAAI,EAAE,SAAS;IAAES,MAAM,EAAE,SAAS;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,CAClI,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,CACjC;IAAEgC,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEQ,IAAI,EAAE,QAAQ;IAAEC,QAAQ,EAAE,uBAAuB;IAAEK,MAAM,EAAE,QAAQ;IAAEJ,SAAS,EAAE;EAAa,CAAC,EAC5J;IAAEG,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,kBAAkB;IAAEQ,IAAI,EAAE,QAAQ;IAAEC,QAAQ,EAAE,6BAA6B;IAAEK,MAAM,EAAE,QAAQ;IAAEJ,SAAS,EAAE;EAAc,CAAC,EAChK;IAAEG,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,kBAAkB;IAAEQ,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,uBAAuB;IAAEK,MAAM,EAAE,QAAQ;IAAEJ,SAAS,EAAE;EAAG,CAAC,EACnJ;IAAEG,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,eAAe;IAAEQ,IAAI,EAAE,QAAQ;IAAEC,QAAQ,EAAE,6BAA6B;IAAEK,MAAM,EAAE,QAAQ;IAAEJ,SAAS,EAAE;EAAmB,CAAC,EACpK;IAAEG,EAAE,EAAE,CAAC;IAAEd,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,oBAAoB;IAAEQ,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,iBAAiB;IAAEK,MAAM,EAAE,UAAU;IAAEJ,SAAS,EAAE;EAAG,CAAC,CAC5I,CAAC;EAEF,MAAMU,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAEX,SAAS,CAACY,MAAM,CAACC,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAgB,CAAC,EACrG;IAAEL,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAEJ,KAAK,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpB,IAAI,KAAK,QAAQ,IAAIoB,CAAC,CAACd,MAAM,KAAK,QAAQ,CAAC,CAACS,MAAM,CAACC,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE;EAAgB,CAAC,EAC7J;IAAEL,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAEJ,KAAK,CAACK,MAAM,CAACC,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAe,CAAC,EAC5F;IAAEL,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAEX,SAAS,CAACgB,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAK,SAAS,CAAC,CAACS,MAAM,CAACC,QAAQ,CAAC,CAAC;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAiB,CAAC,CAC7I;;EAED;EACA,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACjC,WAAW,CAACE,IAAI,IAAI,CAACF,WAAW,CAACG,KAAK,IAAI,CAACH,WAAW,CAACO,aAAa,EAAE;MACzE2B,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,MAAMtB,QAAQ,GAAG;MACfI,EAAE,EAAEF,SAAS,CAACY,MAAM,GAAG,CAAC;MACxB,GAAG1B,WAAW;MACdiB,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC;IAEDL,YAAY,CAAC,CAAC,GAAGD,SAAS,EAAEF,QAAQ,CAAC,CAAC;IACtCX,cAAc,CAAC;MACbC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE;IACR,CAAC,CAAC;IACFb,YAAY,CAAC,KAAK,CAAC;IACnBuC,KAAK,CAAC,8BAA8B,CAAC;EACvC,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAC1B,OAAO,CAACP,IAAI,IAAI,CAACO,OAAO,CAACN,KAAK,IAAI,CAACM,OAAO,CAACE,IAAI,EAAE;MACpDuB,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,MAAME,IAAI,GAAG;MACXpB,EAAE,EAAEK,KAAK,CAACK,MAAM,GAAG,CAAC;MACpB,GAAGjB,OAAO;MACVQ,MAAM,EAAE;IACV,CAAC;IAEDK,QAAQ,CAAC,CAAC,GAAGD,KAAK,EAAEe,IAAI,CAAC,CAAC;IAC1B1B,UAAU,CAAC;MACTR,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTO,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE;IACb,CAAC,CAAC;IACFlB,YAAY,CAAC,KAAK,CAAC;IACnBuC,KAAK,CAAC,0BAA0B,CAAC;EACnC,CAAC;EAED,MAAMG,eAAe,GAAIC,UAAU,IAAK;IACtCvB,YAAY,CAACD,SAAS,CAACyB,GAAG,CAACP,CAAC,IAC1BA,CAAC,CAAChB,EAAE,KAAKsB,UAAU,GAAG;MAAE,GAAGN,CAAC;MAAEf,MAAM,EAAE;IAAS,CAAC,GAAGe,CACrD,CAAC,CAAC;IACFE,KAAK,CAAC,iCAAiC,CAAC;EAC1C,CAAC;EAED,MAAMM,eAAe,GAAIF,UAAU,IAAK;IACtCvB,YAAY,CAACD,SAAS,CAACyB,GAAG,CAACP,CAAC,IAC1BA,CAAC,CAAChB,EAAE,KAAKsB,UAAU,GAAG;MAAE,GAAGN,CAAC;MAAEf,MAAM,EAAE;IAAY,CAAC,GAAGe,CACxD,CAAC,CAAC;IACFE,KAAK,CAAC,qBAAqB,CAAC;EAC9B,CAAC;EAED,MAAMO,gBAAgB,GAAIC,MAAM,IAAK;IACnCpB,QAAQ,CAACD,KAAK,CAACkB,GAAG,CAACR,CAAC,IAClBA,CAAC,CAACf,EAAE,KAAK0B,MAAM,GAAG;MAAE,GAAGX,CAAC;MAAEd,MAAM,EAAEc,CAAC,CAACd,MAAM,KAAK,QAAQ,GAAG,UAAU,GAAG;IAAS,CAAC,GAAGc,CACtF,CAAC,CAAC;IACFG,KAAK,CAAC,sBAAsB,CAAC;EAC/B,CAAC;EAED,MAAMS,UAAU,GAAID,MAAM,IAAK;IAC7B,IAAIE,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChEvB,QAAQ,CAACD,KAAK,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAK0B,MAAM,CAAC,CAAC;MAC5CR,KAAK,CAAC,4BAA4B,CAAC;IACrC;EACF,CAAC;EAED,MAAMY,SAAS,GAAGA,CAACtC,IAAI,EAAEuC,IAAI,GAAG,IAAI,KAAK;IACvClD,YAAY,CAACW,IAAI,CAAC;IAClBT,eAAe,CAACgD,IAAI,CAAC;IACrBpD,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,oBACER,OAAA;IAAK6D,SAAS,EAAE/D,MAAM,CAACgE,SAAU;IAAAC,QAAA,gBAE/B/D,OAAA;MAAK6D,SAAS,EAAE/D,MAAM,CAACkE,OAAQ;MAAAD,QAAA,gBAC7B/D,OAAA;QAAK6D,SAAS,EAAE/D,MAAM,CAACmE,aAAc;QAAAF,QAAA,eACnC/D,OAAA;UAAA+D,QAAA,EAAI;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACNrE,OAAA;QAAK6D,SAAS,EAAE/D,MAAM,CAACwE,UAAW;QAAAP,QAAA,gBAChC/D,OAAA;UACE6D,SAAS,EAAE,GAAG/D,MAAM,CAACyE,OAAO,IAAIlE,SAAS,KAAK,UAAU,GAAGP,MAAM,CAAC0E,aAAa,GAAG,EAAE,EAAG;UACvFC,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,UAAU,CAAE;UAAAyD,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA;UACE6D,SAAS,EAAE,GAAG/D,MAAM,CAACyE,OAAO,IAAIlE,SAAS,KAAK,WAAW,GAAGP,MAAM,CAAC0E,aAAa,GAAG,EAAE,EAAG;UACxFC,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,WAAW,CAAE;UAAAyD,QAAA,EAC1C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA;UACE6D,SAAS,EAAE,GAAG/D,MAAM,CAACyE,OAAO,IAAIlE,SAAS,KAAK,OAAO,GAAGP,MAAM,CAAC0E,aAAa,GAAG,EAAE,EAAG;UACpFC,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,OAAO,CAAE;UAAAyD,QAAA,EACtC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA;UACE6D,SAAS,EAAE,GAAG/D,MAAM,CAACyE,OAAO,IAAIlE,SAAS,KAAK,cAAc,GAAGP,MAAM,CAAC0E,aAAa,GAAG,EAAE,EAAG;UAC3FC,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,cAAc,CAAE;UAAAyD,QAAA,EAC7C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA;UACE6D,SAAS,EAAE,GAAG/D,MAAM,CAACyE,OAAO,IAAIlE,SAAS,KAAK,UAAU,GAAGP,MAAM,CAAC0E,aAAa,GAAG,EAAE,EAAG;UACvFC,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,UAAU,CAAE;UAAAyD,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA;MAAK6D,SAAS,EAAE/D,MAAM,CAAC4E,WAAY;MAAAX,QAAA,gBACjC/D,OAAA;QAAK6D,SAAS,EAAE/D,MAAM,CAAC6E,MAAO;QAAAZ,QAAA,gBAC5B/D,OAAA;UAAA+D,QAAA,EAAI;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBrE,OAAA;UAAK6D,SAAS,EAAE/D,MAAM,CAAC8E,aAAc;UAAAb,QAAA,gBACnC/D,OAAA;YAAQ6D,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAE/B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrE,OAAA;YAAQ6D,SAAS,EAAC,eAAe;YAAAE,QAAA,EAAC;UAElC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELhE,SAAS,KAAK,UAAU,iBACvBL,OAAA,CAAAE,SAAA;QAAA6D,QAAA,gBAEE/D,OAAA;UAAK6D,SAAS,EAAE/D,MAAM,CAAC+E,SAAU;UAAAd,QAAA,EAC9B3B,KAAK,CAACgB,GAAG,CAAC,CAAC0B,IAAI,EAAEC,KAAK,kBACrB/E,OAAA;YAAiB6D,SAAS,EAAE/D,MAAM,CAACkF,QAAS;YAAAjB,QAAA,gBAC1C/D,OAAA;cAAK6D,SAAS,EAAE/D,MAAM,CAACmF,QAAS;cAAAlB,QAAA,EAAEe,IAAI,CAACrC;YAAI;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDrE,OAAA;cAAK6D,SAAS,EAAE/D,MAAM,CAACoF,WAAY;cAAAnB,QAAA,gBACjC/D,OAAA;gBAAA+D,QAAA,EAAKe,IAAI,CAACzC;cAAK;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBrE,OAAA;gBAAK6D,SAAS,EAAE/D,MAAM,CAACqF,SAAU;gBAAApB,QAAA,EAAEe,IAAI,CAACxC;cAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDrE,OAAA;gBAAK6D,SAAS,EAAE/D,MAAM,CAACsF,UAAW;gBAAArB,QAAA,EAAEe,IAAI,CAACpC;cAAM;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA,GANEU,KAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrE,OAAA;UAAK6D,SAAS,EAAE/D,MAAM,CAACuF,gBAAiB;UAAAtB,QAAA,gBACtC/D,OAAA;YAAA+D,QAAA,EAAI;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BrE,OAAA;YAAK6D,SAAS,EAAE/D,MAAM,CAACwF,cAAe;YAAAvB,QAAA,EACnC,CACC;cAAEwB,MAAM,EAAE,wBAAwB;cAAEtC,IAAI,EAAE,UAAU;cAAEuC,IAAI,EAAE,eAAe;cAAEnE,IAAI,EAAE;YAAc,CAAC,EAClG;cAAEkE,MAAM,EAAE,6BAA6B;cAAEtC,IAAI,EAAE,mBAAmB;cAAEuC,IAAI,EAAE,gBAAgB;cAAEnE,IAAI,EAAE;YAAS,CAAC,EAC5G;cAAEkE,MAAM,EAAE,oBAAoB;cAAEtC,IAAI,EAAE,OAAO;cAAEuC,IAAI,EAAE,YAAY;cAAEnE,IAAI,EAAE;YAAW,CAAC,EACrF;cAAEkE,MAAM,EAAE,sBAAsB;cAAEtC,IAAI,EAAE,YAAY;cAAEuC,IAAI,EAAE,aAAa;cAAEnE,IAAI,EAAE;YAAO,CAAC,CAC1F,CAAC+B,GAAG,CAAC,CAACqC,QAAQ,EAAEV,KAAK,kBACpB/E,OAAA;cAAiB6D,SAAS,EAAE/D,MAAM,CAAC4F,YAAa;cAAA3B,QAAA,gBAC9C/D,OAAA;gBAAK6D,SAAS,EAAE/D,MAAM,CAAC6F,YAAa;gBAAA5B,QAAA,GACjC0B,QAAQ,CAACpE,IAAI,KAAK,aAAa,IAAI,IAAI,EACvCoE,QAAQ,CAACpE,IAAI,KAAK,QAAQ,IAAI,OAAO,EACrCoE,QAAQ,CAACpE,IAAI,KAAK,UAAU,IAAI,IAAI,EACpCoE,QAAQ,CAACpE,IAAI,KAAK,MAAM,IAAI,IAAI;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNrE,OAAA;gBAAK6D,SAAS,EAAE/D,MAAM,CAAC8F,eAAgB;gBAAA7B,QAAA,gBACrC/D,OAAA;kBAAG6D,SAAS,EAAE/D,MAAM,CAAC+F,cAAe;kBAAA9B,QAAA,EAAE0B,QAAQ,CAACF;gBAAM;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1DrE,OAAA;kBAAG6D,SAAS,EAAE/D,MAAM,CAACgG,YAAa;kBAAA/B,QAAA,GAAC,KAAG,EAAC0B,QAAQ,CAACxC,IAAI,EAAC,UAAG,EAACwC,QAAQ,CAACD,IAAI;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC;YAAA,GAVEU,KAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH,EAEAhE,SAAS,KAAK,WAAW,iBACxBL,OAAA;QAAK6D,SAAS,EAAE/D,MAAM,CAACiG,cAAe;QAAAhC,QAAA,gBACpC/D,OAAA;UAAA+D,QAAA,EAAI;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBrE,OAAA;UAAK6D,SAAS,EAAE/D,MAAM,CAACkG,KAAM;UAAAjC,QAAA,gBAC3B/D,OAAA;YAAK6D,SAAS,EAAE/D,MAAM,CAACmG,WAAY;YAAAlC,QAAA,gBACjC/D,OAAA;cAAA+D,QAAA,EAAK;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBrE,OAAA;cAAA+D,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfrE,OAAA;cAAA+D,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfrE,OAAA;cAAA+D,QAAA,EAAK;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjBrE,OAAA;cAAA+D,QAAA,EAAK;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACL,CACC;YAAEtD,IAAI,EAAE,iBAAiB;YAAEI,IAAI,EAAE,OAAO;YAAE+E,IAAI,EAAE,GAAG;YAAEpE,MAAM,EAAE;UAAS,CAAC,EACvE;YAAEf,IAAI,EAAE,iBAAiB;YAAEI,IAAI,EAAE,WAAW;YAAE+E,IAAI,EAAE,GAAG;YAAEpE,MAAM,EAAE;UAAS,CAAC,EAC3E;YAAEf,IAAI,EAAE,gBAAgB;YAAEI,IAAI,EAAE,QAAQ;YAAE+E,IAAI,EAAE,GAAG;YAAEpE,MAAM,EAAE;UAAU,CAAC,EACxE;YAAEf,IAAI,EAAE,OAAO;YAAEI,IAAI,EAAE,WAAW;YAAE+E,IAAI,EAAE,IAAI;YAAEpE,MAAM,EAAE;UAAS,CAAC,CACnE,CAACsB,GAAG,CAAC,CAAC3B,QAAQ,EAAEsD,KAAK,kBACpB/E,OAAA;YAAiB6D,SAAS,EAAE/D,MAAM,CAACqG,QAAS;YAAApC,QAAA,gBAC1C/D,OAAA;cAAA+D,QAAA,EAAMtC,QAAQ,CAACV;YAAI;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BrE,OAAA;cAAA+D,QAAA,EAAMtC,QAAQ,CAACN;YAAI;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BrE,OAAA;cAAA+D,QAAA,EAAMtC,QAAQ,CAACyE;YAAI;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BrE,OAAA;cAAA+D,QAAA,eACE/D,OAAA;gBAAM6D,SAAS,EAAE,GAAG/D,MAAM,CAACsG,WAAW,IAAI3E,QAAQ,CAACK,MAAM,KAAK,QAAQ,GAAGhC,MAAM,CAACuG,YAAY,GAAGvG,MAAM,CAACwG,aAAa,EAAG;gBAAAvC,QAAA,EACnHtC,QAAQ,CAACK;cAAM;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrE,OAAA;cAAK6D,SAAS,EAAE/D,MAAM,CAACyG,YAAa;cAAAxC,QAAA,gBAClC/D,OAAA;gBAAQ6D,SAAS,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CrE,OAAA;gBAAQ6D,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA,GAZEU,KAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAhE,SAAS,KAAK,OAAO,iBACpBL,OAAA;QAAK6D,SAAS,EAAE/D,MAAM,CAACiG,cAAe;QAAAhC,QAAA,gBACpC/D,OAAA;UAAA+D,QAAA,EAAI;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBrE,OAAA;UAAK6D,SAAS,EAAE/D,MAAM,CAACkG,KAAM;UAAAjC,QAAA,gBAC3B/D,OAAA;YAAK6D,SAAS,EAAE/D,MAAM,CAACmG,WAAY;YAAAlC,QAAA,gBACjC/D,OAAA;cAAA+D,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfrE,OAAA;cAAA+D,QAAA,EAAK;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfrE,OAAA;cAAA+D,QAAA,EAAK;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnBrE,OAAA;cAAA+D,QAAA,EAAK;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjBrE,OAAA;cAAA+D,QAAA,EAAK;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACL,CACC;YAAEtD,IAAI,EAAE,kBAAkB;YAAES,IAAI,EAAE,QAAQ;YAAEC,QAAQ,EAAE,iBAAiB;YAAEK,MAAM,EAAE;UAAS,CAAC,EAC3F;YAAEf,IAAI,EAAE,UAAU;YAAES,IAAI,EAAE,SAAS;YAAEC,QAAQ,EAAE,GAAG;YAAEK,MAAM,EAAE;UAAS,CAAC,EACtE;YAAEf,IAAI,EAAE,cAAc;YAAES,IAAI,EAAE,cAAc;YAAEC,QAAQ,EAAE,iBAAiB;YAAEK,MAAM,EAAE;UAAS,CAAC,EAC7F;YAAEf,IAAI,EAAE,eAAe;YAAES,IAAI,EAAE,QAAQ;YAAEC,QAAQ,EAAE,gBAAgB;YAAEK,MAAM,EAAE;UAAU,CAAC,CACzF,CAACsB,GAAG,CAAC,CAACH,IAAI,EAAE8B,KAAK,kBAChB/E,OAAA;YAAiB6D,SAAS,EAAE/D,MAAM,CAACqG,QAAS;YAAApC,QAAA,gBAC1C/D,OAAA;cAAA+D,QAAA,EAAMd,IAAI,CAAClC;YAAI;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtBrE,OAAA;cAAA+D,QAAA,EAAMd,IAAI,CAACzB;YAAI;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtBrE,OAAA;cAAA+D,QAAA,EAAMd,IAAI,CAACxB;YAAQ;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BrE,OAAA;cAAA+D,QAAA,eACE/D,OAAA;gBAAM6D,SAAS,EAAE,GAAG/D,MAAM,CAACsG,WAAW,IAAInD,IAAI,CAACnB,MAAM,KAAK,QAAQ,GAAGhC,MAAM,CAACuG,YAAY,GAAGvG,MAAM,CAACwG,aAAa,EAAG;gBAAAvC,QAAA,EAC/Gd,IAAI,CAACnB;cAAM;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrE,OAAA;cAAK6D,SAAS,EAAE/D,MAAM,CAACyG,YAAa;cAAAxC,QAAA,gBAClC/D,OAAA;gBAAQ6D,SAAS,EAAC,eAAe;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CrE,OAAA;gBAAQ6D,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA,GAZEU,KAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CApTID,cAAc;AAAAqG,EAAA,GAAdrG,cAAc;AAsTpB,eAAeA,cAAc;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}