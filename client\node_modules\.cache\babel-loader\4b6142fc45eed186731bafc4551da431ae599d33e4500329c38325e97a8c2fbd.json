{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 22v-5\",\n  key: \"sfixh4\"\n}], [\"path\", {\n  d: \"M14 19v-2\",\n  key: \"pdve8j\"\n}], [\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"M18 20v-3\",\n  key: \"uox2gk\"\n}], [\"path\", {\n  d: \"M2 13h20\",\n  key: \"5evz65\"\n}], [\"path\", {\n  d: \"M20 13V7l-5-5H6a2 2 0 0 0-2 2v9\",\n  key: \"1rnpe2\"\n}], [\"path\", {\n  d: \"M6 20v-3\",\n  key: \"c6pdcb\"\n}]];\nconst Shredder = createLucideIcon(\"shredder\", __iconNode);\nexport { __iconNode, Shredder as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Shredder", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\shredder.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 22v-5', key: 'sfixh4' }],\n  ['path', { d: 'M14 19v-2', key: 'pdve8j' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M18 20v-3', key: 'uox2gk' }],\n  ['path', { d: 'M2 13h20', key: '5evz65' }],\n  ['path', { d: 'M20 13V7l-5-5H6a2 2 0 0 0-2 2v9', key: '1rnpe2' }],\n  ['path', { d: 'M6 20v-3', key: 'c6pdcb' }],\n];\n\n/**\n * @component @name Shredder\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjJ2LTUiIC8+CiAgPHBhdGggZD0iTTE0IDE5di0yIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xOCAyMHYtMyIgLz4KICA8cGF0aCBkPSJNMiAxM2gyMCIgLz4KICA8cGF0aCBkPSJNMjAgMTNWN2wtNS01SDZhMiAyIDAgMCAwLTIgMnY5IiAvPgogIDxwYXRoIGQ9Ik02IDIwdi0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/shredder\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shredder = createLucideIcon('shredder', __iconNode);\n\nexport default Shredder;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,aAAYJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}