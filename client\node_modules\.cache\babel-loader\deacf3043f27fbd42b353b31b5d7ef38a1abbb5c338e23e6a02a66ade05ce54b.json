{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"4.5\",\n  r: \"2.5\",\n  key: \"r5ysbb\"\n}], [\"path\", {\n  d: \"m10.2 6.3-3.9 3.9\",\n  key: \"1nzqf6\"\n}], [\"circle\", {\n  cx: \"4.5\",\n  cy: \"12\",\n  r: \"2.5\",\n  key: \"jydg6v\"\n}], [\"path\", {\n  d: \"M7 12h10\",\n  key: \"b7w52i\"\n}], [\"circle\", {\n  cx: \"19.5\",\n  cy: \"12\",\n  r: \"2.5\",\n  key: \"1piiel\"\n}], [\"path\", {\n  d: \"m13.8 17.7 3.9-3.9\",\n  key: \"1wyg1y\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"19.5\",\n  r: \"2.5\",\n  key: \"13o1pw\"\n}]];\nconst Waypoints = createLucideIcon(\"waypoints\", __iconNode);\nexport { __iconNode, Waypoints as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Waypoints", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\waypoints.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '4.5', r: '2.5', key: 'r5ysbb' }],\n  ['path', { d: 'm10.2 6.3-3.9 3.9', key: '1nzqf6' }],\n  ['circle', { cx: '4.5', cy: '12', r: '2.5', key: 'jydg6v' }],\n  ['path', { d: 'M7 12h10', key: 'b7w52i' }],\n  ['circle', { cx: '19.5', cy: '12', r: '2.5', key: '1piiel' }],\n  ['path', { d: 'm13.8 17.7 3.9-3.9', key: '1wyg1y' }],\n  ['circle', { cx: '12', cy: '19.5', r: '2.5', key: '13o1pw' }],\n];\n\n/**\n * @component @name Waypoints\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjQuNSIgcj0iMi41IiAvPgogIDxwYXRoIGQ9Im0xMC4yIDYuMy0zLjkgMy45IiAvPgogIDxjaXJjbGUgY3g9IjQuNSIgY3k9IjEyIiByPSIyLjUiIC8+CiAgPHBhdGggZD0iTTcgMTJoMTAiIC8+CiAgPGNpcmNsZSBjeD0iMTkuNSIgY3k9IjEyIiByPSIyLjUiIC8+CiAgPHBhdGggZD0ibTEzLjggMTcuNyAzLjktMy45IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTkuNSIgcj0iMi41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/waypoints\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Waypoints = createLucideIcon('waypoints', __iconNode);\n\nexport default Waypoints;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAOC,CAAG;EAAOC,GAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,QAAU;EAAEH,EAAI;EAAOC,EAAI;EAAMC,CAAG;EAAOC,GAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEH,EAAI;EAAQC,EAAI;EAAMC,CAAG;EAAOC,GAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAQC,CAAG;EAAOC,GAAK;AAAA,CAAU,EAC9D;AAaM,MAAAE,SAAA,GAAYC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}