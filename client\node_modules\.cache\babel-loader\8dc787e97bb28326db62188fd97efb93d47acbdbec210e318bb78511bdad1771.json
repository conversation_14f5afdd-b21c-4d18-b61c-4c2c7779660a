{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport HospitalCard from '../components/HospitalCard';\nimport styles from './Home.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [hospitals, setHospitals] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Indian hospitals dummy data\n  const mockHospitals = [{\n    id: 1,\n    name: \"Apollo Hospital\",\n    city: \"Delhi\",\n    image: \"https://images.unsplash.com/photo-1586773860418-d37222d8fce3?w=400\",\n    pricePerDay: 800,\n    rating: 4.8,\n    totalBeds: 500,\n    availableBeds: 45,\n    services: [\"Cardiology\", \"Neurology\", \"Oncology\", \"Emergency Care\", \"ICU\"]\n  }, {\n    id: 2,\n    name: \"Fortis Hospital\",\n    city: \"Bangalore\",\n    image: \"https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?w=400\",\n    pricePerDay: 1500,\n    rating: 4.7,\n    totalBeds: 400,\n    availableBeds: 23,\n    services: [\"Orthopedic\", \"Cardiology\", \"Gastroenterology\", \"Pediatrics\"]\n  }, {\n    id: 3,\n    name: \"Medanta Medicity\",\n    city: \"Gurgaon\",\n    image: \"https://images.unsplash.com/photo-1551190822-a9333d879b1f?w=400\",\n    pricePerDay: 2500,\n    rating: 4.9,\n    totalBeds: 800,\n    availableBeds: 67,\n    services: [\"Multi-Specialty\", \"Heart Surgery\", \"Transplant\", \"Cancer Care\"]\n  }, {\n    id: 4,\n    name: \"AIIMS\",\n    city: \"New Delhi\",\n    image: \"https://images.unsplash.com/photo-1538108149393-fbbd81895907?w=400\",\n    pricePerDay: 500,\n    rating: 4.6,\n    totalBeds: 2500,\n    availableBeds: 150,\n    services: [\"General Medicine\", \"Surgery\", \"Emergency\", \"Research\"]\n  }, {\n    id: 5,\n    name: \"Max Healthcare\",\n    city: \"Mumbai\",\n    image: \"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400\",\n    pricePerDay: 1200,\n    rating: 4.5,\n    totalBeds: 350,\n    availableBeds: 89,\n    services: [\"Maternity\", \"Pediatrics\", \"Orthopedic\", \"Dermatology\"]\n  }, {\n    id: 6,\n    name: \"Manipal Hospital\",\n    city: \"Bangalore\",\n    image: \"https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400\",\n    pricePerDay: 900,\n    rating: 4.4,\n    totalBeds: 300,\n    availableBeds: 42,\n    services: [\"Nephrology\", \"Urology\", \"Gastroenterology\", \"Emergency\"]\n  }];\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setHospitals(mockHospitals);\n      setLoading(false);\n    }, 1000);\n  }, [mockHospitals]);\n  const filteredHospitals = hospitals.filter(hospital => hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) || hospital.city.toLowerCase().includes(searchTerm.toLowerCase()));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.loading,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.spinner\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading hospitals...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.home,\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: styles.hero,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.heroContent,\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: styles.heroTitle,\n            children: \"Find Hospitals Near You\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: styles.heroSubtitle,\n            children: \"Discover trusted healthcare providers across India with transparent pricing and real-time availability\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.heroActions,\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/book\",\n              className: \"btn-primary\",\n              children: \"\\uD83E\\uDE7A Book Appointment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"#emergency\",\n              className: \"btn-secondary\",\n              children: \"\\uD83D\\uDEA8 Emergency Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: styles.features,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-responsive\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.featureCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.featureIcon,\n              children: \"\\uD83D\\uDC68\\u200D\\u2695\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Expert Doctors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Access to qualified and experienced medical professionals across various specialties.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.featureCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.featureIcon,\n              children: \"\\uD83D\\uDD50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"24/7 Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Round-the-clock medical services and emergency care when you need it most.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.featureCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.featureIcon,\n              children: \"\\uD83D\\uDEE1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Quality Care\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"State-of-the-art facilities and equipment ensuring the highest quality of care.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: styles.searchSection,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.searchCard,\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDD0D Find Hospitals Near You\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Search for hospitals by name or location\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.searchBox,\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search by hospital name or city...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-primary\",\n              children: \"Search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: styles.hospitalListings,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: styles.sectionTitle,\n          children: \"Available Hospitals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-responsive\",\n          children: filteredHospitals.map(hospital => /*#__PURE__*/_jsxDEV(HospitalCard, {\n            hospital: hospital\n          }, hospital.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), filteredHospitals.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.noResults,\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No hospitals found matching your search criteria.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            onClick: () => setSearchTerm(''),\n            children: \"Clear Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: styles.stats,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: styles.sectionTitle,\n          children: \"Why Choose Hope Medics?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-responsive\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statNumber,\n              children: \"50+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statLabel,\n              children: \"\\uD83C\\uDFE5 Partner Hospitals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statNumber,\n              children: \"200+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statLabel,\n              children: \"\\uD83D\\uDC68\\u200D\\u2695\\uFE0F Qualified Doctors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statNumber,\n              children: \"24/7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statLabel,\n              children: \"\\uD83D\\uDEA8 Emergency Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.statCard,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statNumber,\n              children: \"10k+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.statLabel,\n              children: \"\\u2B50 Happy Patients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"lnWCKIUsmLyYVD+ik/eYqcSeLdY=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "HospitalCard", "styles", "jsxDEV", "_jsxDEV", "Home", "_s", "hospitals", "setHospitals", "loading", "setLoading", "searchTerm", "setSearchTerm", "mockHospitals", "id", "name", "city", "image", "pricePerDay", "rating", "totalBeds", "availableBeds", "services", "setTimeout", "filteredHospitals", "filter", "hospital", "toLowerCase", "includes", "className", "children", "spinner", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "home", "hero", "hero<PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "heroActions", "to", "features", "featureCard", "featureIcon", "searchSection", "searchCard", "searchBox", "type", "placeholder", "value", "onChange", "e", "target", "hospitalListings", "sectionTitle", "map", "length", "noResults", "onClick", "stats", "statCard", "statNumber", "statLabel", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport HospitalCard from '../components/HospitalCard';\nimport styles from './Home.module.css';\n\nconst Home = () => {\n  const [hospitals, setHospitals] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Indian hospitals dummy data\n  const mockHospitals = [\n    {\n      id: 1,\n      name: \"Apollo Hospital\",\n      city: \"Delhi\",\n      image: \"https://images.unsplash.com/photo-1586773860418-d37222d8fce3?w=400\",\n      pricePerDay: 800,\n      rating: 4.8,\n      totalBeds: 500,\n      availableBeds: 45,\n      services: [\"Cardiology\", \"Neurology\", \"Oncology\", \"Emergency Care\", \"ICU\"]\n    },\n    {\n      id: 2,\n      name: \"Fortis Hospital\",\n      city: \"Bangalore\",\n      image: \"https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?w=400\",\n      pricePerDay: 1500,\n      rating: 4.7,\n      totalBeds: 400,\n      availableBeds: 23,\n      services: [\"Orthopedic\", \"Cardiology\", \"Gastroenterology\", \"Pediatrics\"]\n    },\n    {\n      id: 3,\n      name: \"Medanta Medicity\",\n      city: \"Gurgaon\",\n      image: \"https://images.unsplash.com/photo-1551190822-a9333d879b1f?w=400\",\n      pricePerDay: 2500,\n      rating: 4.9,\n      totalBeds: 800,\n      availableBeds: 67,\n      services: [\"Multi-Specialty\", \"Heart Surgery\", \"Transplant\", \"Cancer Care\"]\n    },\n    {\n      id: 4,\n      name: \"AIIMS\",\n      city: \"New Delhi\",\n      image: \"https://images.unsplash.com/photo-1538108149393-fbbd81895907?w=400\",\n      pricePerDay: 500,\n      rating: 4.6,\n      totalBeds: 2500,\n      availableBeds: 150,\n      services: [\"General Medicine\", \"Surgery\", \"Emergency\", \"Research\"]\n    },\n    {\n      id: 5,\n      name: \"Max Healthcare\",\n      city: \"Mumbai\",\n      image: \"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400\",\n      pricePerDay: 1200,\n      rating: 4.5,\n      totalBeds: 350,\n      availableBeds: 89,\n      services: [\"Maternity\", \"Pediatrics\", \"Orthopedic\", \"Dermatology\"]\n    },\n    {\n      id: 6,\n      name: \"Manipal Hospital\",\n      city: \"Bangalore\",\n      image: \"https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400\",\n      pricePerDay: 900,\n      rating: 4.4,\n      totalBeds: 300,\n      availableBeds: 42,\n      services: [\"Nephrology\", \"Urology\", \"Gastroenterology\", \"Emergency\"]\n    }\n  ];\n\n  useEffect(() => {\n    // Simulate API call\n    setTimeout(() => {\n      setHospitals(mockHospitals);\n      setLoading(false);\n    }, 1000);\n  }, [mockHospitals]);\n\n  const filteredHospitals = hospitals.filter(hospital =>\n    hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    hospital.city.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) {\n    return (\n      <div className={styles.loading}>\n        <div className={styles.spinner}></div>\n        <p>Loading hospitals...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.home}>\n      {/* Hero Section */}\n      <section className={styles.hero}>\n        <div className=\"container\">\n          <div className={styles.heroContent}>\n            <h1 className={styles.heroTitle}>Find Hospitals Near You</h1>\n            <p className={styles.heroSubtitle}>\n              Discover trusted healthcare providers across India with transparent pricing and real-time availability\n            </p>\n            <div className={styles.heroActions}>\n              <Link to=\"/book\" className=\"btn-primary\">\n                🩺 Book Appointment\n              </Link>\n              <Link to=\"#emergency\" className=\"btn-secondary\">\n                🚨 Emergency Services\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className={styles.features}>\n        <div className=\"container\">\n          <div className=\"grid grid-cols-responsive\">\n            <div className={styles.featureCard}>\n              <div className={styles.featureIcon}>👨‍⚕️</div>\n              <h3>Expert Doctors</h3>\n              <p>Access to qualified and experienced medical professionals across various specialties.</p>\n            </div>\n\n            <div className={styles.featureCard}>\n              <div className={styles.featureIcon}>🕐</div>\n              <h3>24/7 Service</h3>\n              <p>Round-the-clock medical services and emergency care when you need it most.</p>\n            </div>\n\n            <div className={styles.featureCard}>\n              <div className={styles.featureIcon}>🛡️</div>\n              <h3>Quality Care</h3>\n              <p>State-of-the-art facilities and equipment ensuring the highest quality of care.</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Search Section */}\n      <section className={styles.searchSection}>\n        <div className=\"container\">\n          <div className={styles.searchCard}>\n            <h2>🔍 Find Hospitals Near You</h2>\n            <p>Search for hospitals by name or location</p>\n            <div className={styles.searchBox}>\n              <input\n                type=\"text\"\n                placeholder=\"Search by hospital name or city...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"input\"\n              />\n              <button className=\"btn-primary\">Search</button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Hospital Listings */}\n      <section className={styles.hospitalListings}>\n        <div className=\"container\">\n          <h2 className={styles.sectionTitle}>Available Hospitals</h2>\n          <div className=\"grid grid-cols-responsive\">\n            {filteredHospitals.map((hospital) => (\n              <HospitalCard key={hospital.id} hospital={hospital} />\n            ))}\n          </div>\n\n          {filteredHospitals.length === 0 && (\n            <div className={styles.noResults}>\n              <p>No hospitals found matching your search criteria.</p>\n              <button\n                className=\"btn-secondary\"\n                onClick={() => setSearchTerm('')}\n              >\n                Clear Search\n              </button>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className={styles.stats}>\n        <div className=\"container\">\n          <h2 className={styles.sectionTitle}>Why Choose Hope Medics?</h2>\n          <div className=\"grid grid-cols-responsive\">\n            <div className={styles.statCard}>\n              <div className={styles.statNumber}>50+</div>\n              <div className={styles.statLabel}>🏥 Partner Hospitals</div>\n            </div>\n            <div className={styles.statCard}>\n              <div className={styles.statNumber}>200+</div>\n              <div className={styles.statLabel}>👨‍⚕️ Qualified Doctors</div>\n            </div>\n            <div className={styles.statCard}>\n              <div className={styles.statNumber}>24/7</div>\n              <div className={styles.statLabel}>🚨 Emergency Services</div>\n            </div>\n            <div className={styles.statCard}>\n              <div className={styles.statNumber}>10k+</div>\n              <div className={styles.statLabel}>⭐ Happy Patients</div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMe,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,oEAAoE;IAC3EC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,KAAK;EAC3E,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,oEAAoE;IAC3EC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,kBAAkB,EAAE,YAAY;EACzE,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,iEAAiE;IACxEC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,YAAY,EAAE,aAAa;EAC5E,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,oEAAoE;IAC3EC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,GAAG;IAClBC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU;EACnE,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,oEAAoE;IAC3EC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa;EACnE,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,iEAAiE;IACxEC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW;EACrE,CAAC,CACF;EAEDvB,SAAS,CAAC,MAAM;IACd;IACAwB,UAAU,CAAC,MAAM;MACff,YAAY,CAACK,aAAa,CAAC;MAC3BH,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACG,aAAa,CAAC,CAAC;EAEnB,MAAMW,iBAAiB,GAAGjB,SAAS,CAACkB,MAAM,CAACC,QAAQ,IACjDA,QAAQ,CAACX,IAAI,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjB,UAAU,CAACgB,WAAW,CAAC,CAAC,CAAC,IAC9DD,QAAQ,CAACV,IAAI,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjB,UAAU,CAACgB,WAAW,CAAC,CAAC,CAC/D,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKyB,SAAS,EAAE3B,MAAM,CAACO,OAAQ;MAAAqB,QAAA,gBAC7B1B,OAAA;QAAKyB,SAAS,EAAE3B,MAAM,CAAC6B;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtC/B,OAAA;QAAA0B,QAAA,EAAG;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEV;EAEA,oBACE/B,OAAA;IAAKyB,SAAS,EAAE3B,MAAM,CAACkC,IAAK;IAAAN,QAAA,gBAE1B1B,OAAA;MAASyB,SAAS,EAAE3B,MAAM,CAACmC,IAAK;MAAAP,QAAA,eAC9B1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB1B,OAAA;UAAKyB,SAAS,EAAE3B,MAAM,CAACoC,WAAY;UAAAR,QAAA,gBACjC1B,OAAA;YAAIyB,SAAS,EAAE3B,MAAM,CAACqC,SAAU;YAAAT,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7D/B,OAAA;YAAGyB,SAAS,EAAE3B,MAAM,CAACsC,YAAa;YAAAV,QAAA,EAAC;UAEnC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ/B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAACuC,WAAY;YAAAX,QAAA,gBACjC1B,OAAA,CAACJ,IAAI;cAAC0C,EAAE,EAAC,OAAO;cAACb,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAEzC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP/B,OAAA,CAACJ,IAAI;cAAC0C,EAAE,EAAC,YAAY;cAACb,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAEhD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/B,OAAA;MAASyB,SAAS,EAAE3B,MAAM,CAACyC,QAAS;MAAAb,QAAA,eAClC1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB1B,OAAA;UAAKyB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC1B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC0C,WAAY;YAAAd,QAAA,gBACjC1B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC2C,WAAY;cAAAf,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/C/B,OAAA;cAAA0B,QAAA,EAAI;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvB/B,OAAA;cAAA0B,QAAA,EAAG;YAAqF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eAEN/B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC0C,WAAY;YAAAd,QAAA,gBACjC1B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC2C,WAAY;cAAAf,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C/B,OAAA;cAAA0B,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/B,OAAA;cAAA0B,QAAA,EAAG;YAA0E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEN/B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC0C,WAAY;YAAAd,QAAA,gBACjC1B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC2C,WAAY;cAAAf,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7C/B,OAAA;cAAA0B,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/B,OAAA;cAAA0B,QAAA,EAAG;YAA+E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/B,OAAA;MAASyB,SAAS,EAAE3B,MAAM,CAAC4C,aAAc;MAAAhB,QAAA,eACvC1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB1B,OAAA;UAAKyB,SAAS,EAAE3B,MAAM,CAAC6C,UAAW;UAAAjB,QAAA,gBAChC1B,OAAA;YAAA0B,QAAA,EAAI;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnC/B,OAAA;YAAA0B,QAAA,EAAG;UAAwC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/C/B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC8C,SAAU;YAAAlB,QAAA,gBAC/B1B,OAAA;cACE6C,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,oCAAoC;cAChDC,KAAK,EAAExC,UAAW;cAClByC,QAAQ,EAAGC,CAAC,IAAKzC,aAAa,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CtB,SAAS,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACF/B,OAAA;cAAQyB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/B,OAAA;MAASyB,SAAS,EAAE3B,MAAM,CAACqD,gBAAiB;MAAAzB,QAAA,eAC1C1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1B,OAAA;UAAIyB,SAAS,EAAE3B,MAAM,CAACsD,YAAa;UAAA1B,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5D/B,OAAA;UAAKyB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACvCN,iBAAiB,CAACiC,GAAG,CAAE/B,QAAQ,iBAC9BtB,OAAA,CAACH,YAAY;YAAmByB,QAAQ,EAAEA;UAAS,GAAhCA,QAAQ,CAACZ,EAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAuB,CACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELX,iBAAiB,CAACkC,MAAM,KAAK,CAAC,iBAC7BtD,OAAA;UAAKyB,SAAS,EAAE3B,MAAM,CAACyD,SAAU;UAAA7B,QAAA,gBAC/B1B,OAAA;YAAA0B,QAAA,EAAG;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxD/B,OAAA;YACEyB,SAAS,EAAC,eAAe;YACzB+B,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,EAAE,CAAE;YAAAkB,QAAA,EAClC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/B,OAAA;MAASyB,SAAS,EAAE3B,MAAM,CAAC2D,KAAM;MAAA/B,QAAA,eAC/B1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1B,OAAA;UAAIyB,SAAS,EAAE3B,MAAM,CAACsD,YAAa;UAAA1B,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChE/B,OAAA;UAAKyB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC1B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC4D,QAAS;YAAAhC,QAAA,gBAC9B1B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC6D,UAAW;cAAAjC,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C/B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC8D,SAAU;cAAAlC,QAAA,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN/B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC4D,QAAS;YAAAhC,QAAA,gBAC9B1B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC6D,UAAW;cAAAjC,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7C/B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC8D,SAAU;cAAAlC,QAAA,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN/B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC4D,QAAS;YAAAhC,QAAA,gBAC9B1B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC6D,UAAW;cAAAjC,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7C/B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC8D,SAAU;cAAAlC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACN/B,OAAA;YAAKyB,SAAS,EAAE3B,MAAM,CAAC4D,QAAS;YAAAhC,QAAA,gBAC9B1B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC6D,UAAW;cAAAjC,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7C/B,OAAA;cAAKyB,SAAS,EAAE3B,MAAM,CAAC8D,SAAU;cAAAlC,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAtNID,IAAI;AAAA4D,EAAA,GAAJ5D,IAAI;AAwNV,eAAeA,IAAI;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}