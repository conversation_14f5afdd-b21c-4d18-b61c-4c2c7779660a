# Hope Medics - Quick Setup Guide

## 🚀 5-Minute Setup

### Step 1: Install Dependencies
```bash
npm run install-all
```

### Step 2: Database Setup

**Option A: Quick MySQL Setup (No Password)**
```bash
# Connect to MySQL (if no password set)
mysql -u root

# Run these commands in MySQL:
CREATE DATABASE hopemedics;
exit;
```

**Option B: MySQL with Password**
```bash
# Connect to MySQL with password
mysql -u root -p

# Enter your password, then run:
CREATE DATABASE hopemedics;
exit;

# Update server/.env file with your password:
MYSQL_PASSWORD=your_password_here
```

### Step 3: Seed Database
```bash
cd server
npm run seed
```

### Step 4: Start Application
```bash
# From root directory
npm run dev
```

### Step 5: Open Application
- Frontend: http://localhost:5173
- Backend API: http://localhost:5000

## 🎯 What You'll See

### Frontend Features (All Accessible)
- **Home Page**: Hospital listings with search and filters
- **Hospital Details**: Individual hospital information with doctors
- **Book Appointment**: Complete booking form with validation
- **Login/Register**: Authentication forms with demo credentials
- **Admin Dashboard**: Hospital and doctor management interface
- **Doctor Dashboard**: Appointment management for doctors
- **Reception Dashboard**: Walk-in patient management

### Demo Login Credentials
- **Patient**: <EMAIL> / password
- **Doctor**: <EMAIL> / password
- **Admin**: <EMAIL> / password
- **Reception**: <EMAIL> / password

## 🔧 Development Features

### All Pages Directly Accessible
During development, you can navigate to any page directly:
- `/` - Home
- `/hospital/1` - Hospital Details
- `/book` - Book Appointment
- `/login` - Login
- `/register` - Register
- `/admin` - Admin Dashboard
- `/doctor` - Doctor Dashboard
- `/reception` - Reception Dashboard

### Mock Data Included
- 3 Sample hospitals with different services and pricing
- 6 Sample doctors across different specialties
- Sample appointments and patient data
- Realistic hospital information with bed availability

### API Testing
Test the backend API at http://localhost:5000:
- `GET /health` - Health check
- `GET /api/hospitals` - List hospitals
- `GET /api/doctors` - List doctors
- `POST /api/auth/login` - Login

## 🚨 Common Issues & Solutions

### "Database connection failed"
1. Ensure MySQL is running
2. Check if database `hopemedics` exists
3. Verify credentials in `server/.env`

### "Port already in use"
1. Kill existing processes: `npx kill-port 5173 5000`
2. Or change ports in configuration

### "Module not found"
1. Run `npm run install-all` again
2. Clear node_modules: `rm -rf node_modules && npm install`

### Tailwind CSS not working
1. Restart the development server
2. Clear browser cache

## 📱 Mobile Responsive
The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## 🎨 UI Features
- Modern, clean design with Tailwind CSS
- Intuitive navigation with role-based dashboards
- Real-time form validation
- Loading states and error handling
- Accessible design patterns

## 🔄 Next Steps After Setup
1. Explore all the pages and features
2. Test the booking flow
3. Try different user roles (admin, doctor, reception)
4. Customize hospital and doctor data
5. Add your own branding and styling

## 💡 Tips for Development
- Use the browser's developer tools to inspect the responsive design
- Check the network tab to see API calls
- All forms have validation - try submitting empty forms to see error handling
- The navigation bar shows all available pages for easy testing
