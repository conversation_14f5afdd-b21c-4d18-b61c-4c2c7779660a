{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v8\",\n  key: \"d4bbey\"\n}], [\"path\", {\n  d: \"M12.8 21.6A2 2 0 1 0 14 18H2\",\n  key: \"19kp1d\"\n}], [\"path\", {\n  d: \"M17.5 10a2.5 2.5 0 1 1 2 4H2\",\n  key: \"19kpjc\"\n}], [\"path\", {\n  d: \"m6 6 4 4 4-4\",\n  key: \"k13n16\"\n}]];\nconst WindArrowDown = createLucideIcon(\"wind-arrow-down\", __iconNode);\nexport { __iconNode, WindArrowDown as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "WindArrowDown", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\wind-arrow-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 2v8', key: 'd4bbey' }],\n  ['path', { d: 'M12.8 21.6A2 2 0 1 0 14 18H2', key: '19kp1d' }],\n  ['path', { d: 'M17.5 10a2.5 2.5 0 1 1 2 4H2', key: '19kpjc' }],\n  ['path', { d: 'm6 6 4 4 4-4', key: 'k13n16' }],\n];\n\n/**\n * @component @name WindArrowDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnY4IiAvPgogIDxwYXRoIGQ9Ik0xMi44IDIxLjZBMiAyIDAgMSAwIDE0IDE4SDIiIC8+CiAgPHBhdGggZD0iTTE3LjUgMTBhMi41IDIuNSAwIDEgMSAyIDRIMiIgLz4KICA8cGF0aCBkPSJtNiA2IDQgNCA0LTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/wind-arrow-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst WindArrowDown = createLucideIcon('wind-arrow-down', __iconNode);\n\nexport default WindArrowDown;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/C;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}