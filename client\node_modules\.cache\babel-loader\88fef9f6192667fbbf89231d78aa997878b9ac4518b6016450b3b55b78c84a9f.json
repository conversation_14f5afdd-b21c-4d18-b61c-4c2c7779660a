{"ast": null, "code": "var _jsxFileName = \"D:\\\\WebProject\\\\HOPE-MEDICS\\\\client\\\\src\\\\pages\\\\ReceptionDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styles from './ReceptionDashboard.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReceptionDashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('appointments');\n  const stats = [{\n    title: \"Today's Total\",\n    value: '24',\n    icon: '📅',\n    change: '+3 from yesterday'\n  }, {\n    title: 'Checked In',\n    value: '18',\n    icon: '✅',\n    change: '75% completion rate'\n  }, {\n    title: 'Currently Waiting',\n    value: '6',\n    icon: '⏳',\n    change: 'Avg wait: 15 min'\n  }, {\n    title: 'Completed',\n    value: '12',\n    icon: '👥',\n    change: '50% of total'\n  }];\n  const appointments = [{\n    id: 1,\n    time: '09:00',\n    patient: '<PERSON><PERSON>',\n    doctor: 'Dr. <PERSON><PERSON>',\n    status: 'checked-in',\n    phone: '+91 98765 43210',\n    room: 'Room 101'\n  }, {\n    id: 2,\n    time: '09:30',\n    patient: '<PERSON><PERSON>',\n    doctor: 'Dr. Arjun <PERSON>',\n    status: 'waiting',\n    phone: '+91 87654 32109',\n    room: 'Room 102'\n  }, {\n    id: 3,\n    time: '10:00',\n    patient: 'Amit <PERSON>',\n    doctor: 'Dr. Meena <PERSON>',\n    status: 'in-progress',\n    phone: '+91 76543 21098',\n    room: 'Room 101'\n  }, {\n    id: 4,\n    time: '10:30',\n    patient: 'Sunita <PERSON>',\n    doctor: 'Dr. Sunil Patel',\n    status: 'completed',\n    phone: '+91 65432 10987',\n    room: 'Room 103'\n  }, {\n    id: 5,\n    time: '11:00',\n    patient: 'Vikram Sharma',\n    doctor: 'Dr. Priya Singh',\n    status: 'scheduled',\n    phone: '+91 54321 09876',\n    room: 'Room 104'\n  }];\n  const walkInQueue = [{\n    id: 6,\n    patient: 'Deepak Verma',\n    reason: 'Emergency consultation',\n    priority: 'high',\n    waitTime: '15 min',\n    phone: '+91 43210 98765'\n  }, {\n    id: 7,\n    patient: 'Kavita Joshi',\n    reason: 'General checkup',\n    priority: 'normal',\n    waitTime: '25 min',\n    phone: '+91 32109 87654'\n  }, {\n    id: 8,\n    patient: 'Rohit Agarwal',\n    reason: 'Follow-up visit',\n    priority: 'normal',\n    waitTime: '35 min',\n    phone: '+91 21098 76543'\n  }];\n  const todayDate = new Date().toLocaleDateString('en-IN', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold\",\n          children: \"Reception Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted-foreground\",\n          children: \"Manage appointments and walk-in patients\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), \"Add Walk-in Patient\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), \"Print Schedule\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: stats.map((stat, index) => {\n        const IconComponent = stat.icon;\n        return /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-muted-foreground\",\n                  children: stat.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: stat.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconComponent, {\n                className: `h-8 w-8 ${stat.color}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center justify-between\",\n              children: [\"Today's Appointments\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-muted-foreground\",\n                children: new Date().toLocaleDateString('en-US', {\n                  weekday: 'long',\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: appointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-lg\",\n                        children: appointment.time\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 99,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: appointment.patient\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 100,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        className: getStatusColor(appointment.status),\n                        children: appointment.status.replace('-', ' ').toUpperCase()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 101,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 98,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-muted-foreground\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\uD83D\\uDC69\\u200D\\u2695\\uFE0F \", appointment.doctor]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 106,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [appointment.status === 'waiting' && /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      children: \"Check In\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 27\n                    }, this), appointment.status === 'checked-in' && /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline\",\n                      children: [/*#__PURE__*/_jsxDEV(Phone, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 115,\n                        columnNumber: 29\n                      }, this), \"Call Patient\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 114,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 21\n                }, this)\n              }, appointment.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center justify-between\",\n              children: [\"Walk-in Queue\", /*#__PURE__*/_jsxDEV(Badge, {\n                variant: \"outline\",\n                children: [walkInQueue.length, \" waiting\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: walkInQueue.map(patient => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border rounded-lg p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: patient.patient\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    className: getPriorityColor(patient.priority),\n                    children: patient.priority.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-muted-foreground space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\uD83D\\uDCDD \", patient.reason]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\u23F1\\uFE0F Waiting: \", patient.waitTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2 mt-3\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    className: \"flex-1\",\n                    children: \"Assign Doctor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this)]\n              }, patient.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), walkInQueue.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-muted-foreground py-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-2\",\n                children: \"\\uD83D\\uDC65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No walk-in patients waiting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        children: /*#__PURE__*/_jsxDEV(CardTitle, {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(Phone, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Call Next Patient\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"View All Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Room Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            className: \"h-20 flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Daily Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(ReceptionDashboard, \"tzf+8hdF0NkKuo0cOQ0Y/MooWiM=\");\n_c = ReceptionDashboard;\nexport default ReceptionDashboard;\nvar _c;\n$RefreshReg$(_c, \"ReceptionDashboard\");", "map": {"version": 3, "names": ["React", "useState", "styles", "jsxDEV", "_jsxDEV", "ReceptionDashboard", "_s", "activeTab", "setActiveTab", "stats", "title", "value", "icon", "change", "appointments", "id", "time", "patient", "doctor", "status", "phone", "room", "walkInQueue", "reason", "priority", "waitTime", "todayDate", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "Plus", "variant", "Calendar", "map", "stat", "index", "IconComponent", "Card", "<PERSON><PERSON><PERSON><PERSON>", "color", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "appointment", "Badge", "getStatusColor", "replace", "toUpperCase", "size", "Phone", "length", "getPriorityColor", "UserPlus", "CheckCircle", "_c", "$RefreshReg$"], "sources": ["D:/WebProject/HOPE-MEDICS/client/src/pages/ReceptionDashboard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styles from './ReceptionDashboard.module.css';\n\nconst ReceptionDashboard = () => {\n  const [activeTab, setActiveTab] = useState('appointments');\n\n  const stats = [\n    { title: \"Today's Total\", value: '24', icon: '📅', change: '+3 from yesterday' },\n    { title: 'Checked In', value: '18', icon: '✅', change: '75% completion rate' },\n    { title: 'Currently Waiting', value: '6', icon: '⏳', change: 'Avg wait: 15 min' },\n    { title: 'Completed', value: '12', icon: '👥', change: '50% of total' },\n  ];\n\n  const appointments = [\n    { id: 1, time: '09:00', patient: '<PERSON><PERSON>', doctor: 'Dr. <PERSON><PERSON>', status: 'checked-in', phone: '+91 98765 43210', room: 'Room 101' },\n    { id: 2, time: '09:30', patient: '<PERSON><PERSON>', doctor: 'Dr. <PERSON><PERSON><PERSON>', status: 'waiting', phone: '+91 87654 32109', room: 'Room 102' },\n    { id: 3, time: '10:00', patient: '<PERSON><PERSON>', doctor: 'Dr. <PERSON><PERSON>', status: 'in-progress', phone: '+91 76543 21098', room: 'Room 101' },\n    { id: 4, time: '10:30', patient: '<PERSON><PERSON> <PERSON>', doctor: 'Dr. <PERSON>il <PERSON>', status: 'completed', phone: '+91 65432 10987', room: 'Room 103' },\n    { id: 5, time: '11:00', patient: 'Vikram <PERSON>', doctor: 'Dr. Priya <PERSON>', status: 'scheduled', phone: '+91 54321 09876', room: 'Room 104' },\n  ];\n\n  const walkInQueue = [\n    { id: 6, patient: 'Deepak Verma', reason: 'Emergency consultation', priority: 'high', waitTime: '15 min', phone: '+91 43210 98765' },\n    { id: 7, patient: 'Kavita Joshi', reason: 'General checkup', priority: 'normal', waitTime: '25 min', phone: '+91 32109 87654' },\n    { id: 8, patient: 'Rohit Agarwal', reason: 'Follow-up visit', priority: 'normal', waitTime: '35 min', phone: '+91 21098 76543' },\n  ];\n\n  const todayDate = new Date().toLocaleDateString('en-IN', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold\">Reception Dashboard</h1>\n          <p className=\"text-muted-foreground\">Manage appointments and walk-in patients</p>\n        </div>\n        <div className=\"flex space-x-4\">\n          <Button>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Add Walk-in Patient\n          </Button>\n          <Button variant=\"outline\">\n            <Calendar className=\"mr-2 h-4 w-4\" />\n            Print Schedule\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => {\n          const IconComponent = stat.icon;\n          return (\n            <Card key={index}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">{stat.title}</p>\n                    <p className=\"text-2xl font-bold\">{stat.value}</p>\n                  </div>\n                  <IconComponent className={`h-8 w-8 ${stat.color}`} />\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n\n      {/* Main Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Today's Appointments */}\n        <div className=\"lg:col-span-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                Today's Appointments\n                <span className=\"text-sm text-muted-foreground\">\n                  {new Date().toLocaleDateString('en-US', { \n                    weekday: 'long', \n                    year: 'numeric', \n                    month: 'long', \n                    day: 'numeric' \n                  })}\n                </span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {appointments.map((appointment) => (\n                  <div key={appointment.id} className=\"border rounded-lg p-4 hover:shadow-md transition-shadow\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-2\">\n                          <span className=\"font-semibold text-lg\">{appointment.time}</span>\n                          <span className=\"font-medium\">{appointment.patient}</span>\n                          <Badge className={getStatusColor(appointment.status)}>\n                            {appointment.status.replace('-', ' ').toUpperCase()}\n                          </Badge>\n                        </div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          <div>👩‍⚕️ {appointment.doctor}</div>\n                        </div>\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        {appointment.status === 'waiting' && (\n                          <Button size=\"sm\">Check In</Button>\n                        )}\n                        {appointment.status === 'checked-in' && (\n                          <Button size=\"sm\" variant=\"outline\">\n                            <Phone className=\"h-4 w-4 mr-1\" />\n                            Call Patient\n                          </Button>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Walk-in Queue */}\n        <div>\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                Walk-in Queue\n                <Badge variant=\"outline\">{walkInQueue.length} waiting</Badge>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {walkInQueue.map((patient) => (\n                  <div key={patient.id} className=\"border rounded-lg p-3\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <span className=\"font-medium\">{patient.patient}</span>\n                      <Badge className={getPriorityColor(patient.priority)}>\n                        {patient.priority.toUpperCase()}\n                      </Badge>\n                    </div>\n                    <div className=\"text-sm text-muted-foreground space-y-1\">\n                      <div>📝 {patient.reason}</div>\n                      <div>⏱️ Waiting: {patient.waitTime}</div>\n                    </div>\n                    <div className=\"flex space-x-2 mt-3\">\n                      <Button size=\"sm\" className=\"flex-1\">\n                        Assign Doctor\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {walkInQueue.length === 0 && (\n                <div className=\"text-center text-muted-foreground py-8\">\n                  <div className=\"text-4xl mb-2\">👥</div>\n                  <p>No walk-in patients waiting</p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Quick Actions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <Phone className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Call Next Patient</span>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <Calendar className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">View All Appointments</span>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <UserPlus className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Room Management</span>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <CheckCircle className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Daily Report</span>\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default ReceptionDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,cAAc,CAAC;EAE1D,MAAMQ,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAoB,CAAC,EAChF;IAAEH,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAsB,CAAC,EAC9E;IAAEH,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE,GAAG;IAAEC,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAmB,CAAC,EACjF;IAAEH,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAe,CAAC,CACxE;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,cAAc;IAAEC,MAAM,EAAE,kBAAkB;IAAEC,MAAM,EAAE,YAAY;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAW,CAAC,EAC/I;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,aAAa;IAAEC,MAAM,EAAE,eAAe;IAAEC,MAAM,EAAE,SAAS;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAW,CAAC,EACxI;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,YAAY;IAAEC,MAAM,EAAE,kBAAkB;IAAEC,MAAM,EAAE,aAAa;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAW,CAAC,EAC9I;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,cAAc;IAAEC,MAAM,EAAE,iBAAiB;IAAEC,MAAM,EAAE,WAAW;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAW,CAAC,EAC7I;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,OAAO;IAAEC,OAAO,EAAE,eAAe;IAAEC,MAAM,EAAE,iBAAiB;IAAEC,MAAM,EAAE,WAAW;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAW,CAAC,CAC/I;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEP,EAAE,EAAE,CAAC;IAAEE,OAAO,EAAE,cAAc;IAAEM,MAAM,EAAE,wBAAwB;IAAEC,QAAQ,EAAE,MAAM;IAAEC,QAAQ,EAAE,QAAQ;IAAEL,KAAK,EAAE;EAAkB,CAAC,EACpI;IAAEL,EAAE,EAAE,CAAC;IAAEE,OAAO,EAAE,cAAc;IAAEM,MAAM,EAAE,iBAAiB;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEL,KAAK,EAAE;EAAkB,CAAC,EAC/H;IAAEL,EAAE,EAAE,CAAC;IAAEE,OAAO,EAAE,eAAe;IAAEM,MAAM,EAAE,iBAAiB;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,QAAQ,EAAE,QAAQ;IAAEL,KAAK,EAAE;EAAkB,CAAC,CACjI;EAED,MAAMM,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;IACvDC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,oBACE5B,OAAA;IAAK6B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB9B,OAAA;MAAK6B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD9B,OAAA;QAAA8B,QAAA,gBACE9B,OAAA;UAAI6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DlC,OAAA;UAAG6B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eACNlC,OAAA;QAAK6B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9B,OAAA,CAACmC,MAAM;UAAAL,QAAA,gBACL9B,OAAA,CAACoC,IAAI;YAACP,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlC,OAAA,CAACmC,MAAM;UAACE,OAAO,EAAC,SAAS;UAAAP,QAAA,gBACvB9B,OAAA,CAACsC,QAAQ;YAACT,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAK6B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEzB,KAAK,CAACkC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC1B,MAAMC,aAAa,GAAGF,IAAI,CAAChC,IAAI;QAC/B,oBACER,OAAA,CAAC2C,IAAI;UAAAb,QAAA,eACH9B,OAAA,CAAC4C,WAAW;YAACf,SAAS,EAAC,KAAK;YAAAC,QAAA,eAC1B9B,OAAA;cAAK6B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAG6B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEU,IAAI,CAAClC;gBAAK;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzElC,OAAA;kBAAG6B,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEU,IAAI,CAACjC;gBAAK;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNlC,OAAA,CAAC0C,aAAa;gBAACb,SAAS,EAAE,WAAWW,IAAI,CAACK,KAAK;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GATLO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlC,OAAA;MAAK6B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD9B,OAAA;QAAK6B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B9B,OAAA,CAAC2C,IAAI;UAAAb,QAAA,gBACH9B,OAAA,CAAC8C,UAAU;YAAAhB,QAAA,eACT9B,OAAA,CAAC+C,SAAS;cAAClB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,sBAEvD,eAAA9B,OAAA;gBAAM6B,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC5C,IAAIP,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;kBACtCC,OAAO,EAAE,MAAM;kBACfC,IAAI,EAAE,SAAS;kBACfC,KAAK,EAAE,MAAM;kBACbC,GAAG,EAAE;gBACP,CAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACblC,OAAA,CAAC4C,WAAW;YAAAd,QAAA,eACV9B,OAAA;cAAK6B,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpB,YAAY,CAAC6B,GAAG,CAAES,WAAW,iBAC5BhD,OAAA;gBAA0B6B,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,eAC3F9B,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C9B,OAAA;oBAAK6B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrB9B,OAAA;sBAAK6B,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/C9B,OAAA;wBAAM6B,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEkB,WAAW,CAACpC;sBAAI;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACjElC,OAAA;wBAAM6B,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAEkB,WAAW,CAACnC;sBAAO;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC1DlC,OAAA,CAACiD,KAAK;wBAACpB,SAAS,EAAEqB,cAAc,CAACF,WAAW,CAACjC,MAAM,CAAE;wBAAAe,QAAA,EAClDkB,WAAW,CAACjC,MAAM,CAACoC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;sBAAC;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNlC,OAAA;sBAAK6B,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,eAC5C9B,OAAA;wBAAA8B,QAAA,GAAK,iCAAM,EAACkB,WAAW,CAAClC,MAAM;sBAAA;wBAAAiB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlC,OAAA;oBAAK6B,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAC5BkB,WAAW,CAACjC,MAAM,KAAK,SAAS,iBAC/Bf,OAAA,CAACmC,MAAM;sBAACkB,IAAI,EAAC,IAAI;sBAAAvB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACnC,EACAc,WAAW,CAACjC,MAAM,KAAK,YAAY,iBAClCf,OAAA,CAACmC,MAAM;sBAACkB,IAAI,EAAC,IAAI;sBAAChB,OAAO,EAAC,SAAS;sBAAAP,QAAA,gBACjC9B,OAAA,CAACsD,KAAK;wBAACzB,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAzBEc,WAAW,CAACrC,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BnB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlC,OAAA;QAAA8B,QAAA,eACE9B,OAAA,CAAC2C,IAAI;UAAAb,QAAA,gBACH9B,OAAA,CAAC8C,UAAU;YAAAhB,QAAA,eACT9B,OAAA,CAAC+C,SAAS;cAAClB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,eAEvD,eAAA9B,OAAA,CAACiD,KAAK;gBAACZ,OAAO,EAAC,SAAS;gBAAAP,QAAA,GAAEZ,WAAW,CAACqC,MAAM,EAAC,UAAQ;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACblC,OAAA,CAAC4C,WAAW;YAAAd,QAAA,gBACV9B,OAAA;cAAK6B,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBZ,WAAW,CAACqB,GAAG,CAAE1B,OAAO,iBACvBb,OAAA;gBAAsB6B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACrD9B,OAAA;kBAAK6B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD9B,OAAA;oBAAM6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEjB,OAAO,CAACA;kBAAO;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDlC,OAAA,CAACiD,KAAK;oBAACpB,SAAS,EAAE2B,gBAAgB,CAAC3C,OAAO,CAACO,QAAQ,CAAE;oBAAAU,QAAA,EAClDjB,OAAO,CAACO,QAAQ,CAACgC,WAAW,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNlC,OAAA;kBAAK6B,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtD9B,OAAA;oBAAA8B,QAAA,GAAK,eAAG,EAACjB,OAAO,CAACM,MAAM;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9BlC,OAAA;oBAAA8B,QAAA,GAAK,wBAAY,EAACjB,OAAO,CAACQ,QAAQ;kBAAA;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNlC,OAAA;kBAAK6B,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAClC9B,OAAA,CAACmC,MAAM;oBAACkB,IAAI,EAAC,IAAI;oBAACxB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAfErB,OAAO,CAACF,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELhB,WAAW,CAACqC,MAAM,KAAK,CAAC,iBACvBvD,OAAA;cAAK6B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD9B,OAAA;gBAAK6B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvClC,OAAA;gBAAA8B,QAAA,EAAG;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA,CAAC2C,IAAI;MAAAb,QAAA,gBACH9B,OAAA,CAAC8C,UAAU;QAAAhB,QAAA,eACT9B,OAAA,CAAC+C,SAAS;UAAAjB,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACblC,OAAA,CAAC4C,WAAW;QAAAd,QAAA,eACV9B,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD9B,OAAA,CAACmC,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjD9B,OAAA,CAACsD,KAAK;cAACzB,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClClC,OAAA;cAAM6B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACTlC,OAAA,CAACmC,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjD9B,OAAA,CAACsC,QAAQ;cAACT,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrClC,OAAA;cAAM6B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACTlC,OAAA,CAACmC,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjD9B,OAAA,CAACyD,QAAQ;cAAC5B,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrClC,OAAA;cAAM6B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACTlC,OAAA,CAACmC,MAAM;YAACE,OAAO,EAAC,SAAS;YAACR,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACjD9B,OAAA,CAAC0D,WAAW;cAAC7B,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxClC,OAAA;cAAM6B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChC,EAAA,CAnMID,kBAAkB;AAAA0D,EAAA,GAAlB1D,kBAAkB;AAqMxB,eAAeA,kBAAkB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}