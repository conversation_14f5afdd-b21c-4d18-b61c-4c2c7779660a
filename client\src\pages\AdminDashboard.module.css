.container {
  display: flex;
  min-height: 100vh;
  background: var(--light-grey);
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: var(--white);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.sidebarHeader {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebarHeader h2 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
}

.sidebarNav {
  padding: 1rem 0;
}

.navItem {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: var(--dark-grey);
  cursor: pointer;
  transition: var(--transition);
  border-left: 3px solid transparent;
}

.navItem:hover {
  background: rgba(0, 121, 107, 0.1);
  color: var(--primary-color);
}

.navItemActive {
  background: rgba(0, 121, 107, 0.1);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  font-weight: 600;
}

/* Main Content */
.mainContent {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.statIcon {
  font-size: 2.5rem;
  background: rgba(0, 121, 107, 0.1);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statContent h3 {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.statChange {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

/* Recent Activities */
.recentActivities {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 1.5rem;
}

.recentActivities h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1rem 0;
}

.activitiesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activityItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--light-grey);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.activityItem:hover {
  background: rgba(0, 121, 107, 0.05);
}

.activityIcon {
  font-size: 1.25rem;
  background: var(--white);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-light);
}

.activityContent {
  flex: 1;
}

.activityAction {
  font-weight: 500;
  color: var(--dark-grey);
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
}

.activityMeta {
  font-size: 0.75rem;
  color: #666;
  margin: 0;
}

/* Table Container */
.tableContainer {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: 1.5rem;
}

.tableContainer h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 1.5rem 0;
}

.table {
  width: 100%;
}

.tableHeader {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem;
  background: var(--light-grey);
  border-radius: var(--border-radius);
  font-weight: 600;
  color: var(--dark-grey);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.tableRow {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  align-items: center;
  transition: var(--transition);
}

.tableRow:hover {
  background: var(--light-grey);
}

.tableRow:last-child {
  border-bottom: none;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
}

.statusActive {
  background: #dcfce7;
  color: #166534;
}

.statusPending {
  background: #fef3c7;
  color: #92400e;
}

.tableActions {
  display: flex;
  gap: 0.5rem;
}

.tableActions button {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    position: relative;
  }
  
  .mainContent {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .headerActions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .tableHeader,
  .tableRow {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .tableActions {
    justify-content: flex-start;
  }
}
