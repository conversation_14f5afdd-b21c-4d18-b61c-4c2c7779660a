import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styles from './HospitalListing.module.css';

const HospitalListing = () => {
  const [filters, setFilters] = useState({
    city: '',
    specialty: '',
    priceRange: '',
    rating: '',
    services: []
  });

  const [hospitals] = useState([
    {
      id: 1,
      name: 'Apollo Hospital Delhi',
      city: 'New Delhi',
      state: 'Delhi',
      rating: 4.5,
      reviews: 1250,
      image: '/api/placeholder/300/200',
      specialties: ['Cardiology', 'Neurology', 'Oncology'],
      services: ['24/7 Emergency', 'Ambulance', 'ICU'],
      rooms: [
        { type: 'General Ward', rate: 800, available: 45 },
        { type: 'Private Room', rate: 2500, available: 23 },
        { type: 'ICU', rate: 4500, available: 8 }
      ],
      distance: '2.5 km',
      verified: true
    },
    {
      id: 2,
      name: 'Fortis Hospital Bangalore',
      city: 'Bangalore',
      state: 'Karnataka',
      rating: 4.3,
      reviews: 890,
      image: '/api/placeholder/300/200',
      specialties: ['Orthopedics', 'Cardiology', 'Pediatrics'],
      services: ['24/7 Emergency', 'Ambulance', 'Pharmacy'],
      rooms: [
        { type: 'General Ward', rate: 750, available: 32 },
        { type: 'Private Room', rate: 2200, available: 18 },
        { type: 'ICU', rate: 4200, available: 5 }
      ],
      distance: '1.8 km',
      verified: true
    },
    {
      id: 3,
      name: 'Max Healthcare Mumbai',
      city: 'Mumbai',
      state: 'Maharashtra',
      rating: 4.6,
      reviews: 1580,
      image: '/api/placeholder/300/200',
      specialties: ['Oncology', 'Neurology', 'Gastroenterology'],
      services: ['24/7 Emergency', 'Ambulance', 'ICU', 'Blood Bank'],
      rooms: [
        { type: 'General Ward', rate: 900, available: 28 },
        { type: 'Private Room', rate: 2800, available: 15 },
        { type: 'ICU', rate: 5000, available: 6 }
      ],
      distance: '3.2 km',
      verified: true
    },
    {
      id: 4,
      name: 'AIIMS New Delhi',
      city: 'New Delhi',
      state: 'Delhi',
      rating: 4.4,
      reviews: 2100,
      image: '/api/placeholder/300/200',
      specialties: ['All Specialties', 'Research', 'Emergency Medicine'],
      services: ['24/7 Emergency', 'Ambulance', 'ICU', 'Trauma Center'],
      rooms: [
        { type: 'General Ward', rate: 500, available: 120 },
        { type: 'Private Room', rate: 1500, available: 45 },
        { type: 'ICU', rate: 3500, available: 25 }
      ],
      distance: '5.1 km',
      verified: true
    },
    {
      id: 5,
      name: 'Manipal Hospital Bangalore',
      city: 'Bangalore',
      state: 'Karnataka',
      rating: 4.2,
      reviews: 750,
      image: '/api/placeholder/300/200',
      specialties: ['Cardiology', 'Nephrology', 'Urology'],
      services: ['24/7 Emergency', 'Ambulance', 'Dialysis'],
      rooms: [
        { type: 'General Ward', rate: 700, available: 38 },
        { type: 'Private Room', rate: 2000, available: 20 },
        { type: 'ICU', rate: 4000, available: 7 }
      ],
      distance: '4.3 km',
      verified: false
    },
    {
      id: 6,
      name: 'Medanta Medicity Gurgaon',
      city: 'Gurgaon',
      state: 'Haryana',
      rating: 4.7,
      reviews: 1680,
      image: '/api/placeholder/300/200',
      specialties: ['Heart Surgery', 'Liver Transplant', 'Robotics'],
      services: ['24/7 Emergency', 'Ambulance', 'ICU', 'Helicopter Service'],
      rooms: [
        { type: 'General Ward', rate: 1200, available: 35 },
        { type: 'Private Room', rate: 3500, available: 12 },
        { type: 'ICU', rate: 6000, available: 4 }
      ],
      distance: '8.7 km',
      verified: true
    }
  ]);

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const getLowestRate = (rooms) => {
    return Math.min(...rooms.map(room => room.rate));
  };

  const filteredHospitals = hospitals.filter(hospital => {
    if (filters.city && !hospital.city.toLowerCase().includes(filters.city.toLowerCase())) {
      return false;
    }
    if (filters.specialty && !hospital.specialties.some(spec => 
      spec.toLowerCase().includes(filters.specialty.toLowerCase())
    )) {
      return false;
    }
    if (filters.rating && hospital.rating < parseFloat(filters.rating)) {
      return false;
    }
    return true;
  });

  return (
    <div className={styles.container}>
      {/* Search Header */}
      <div className={styles.searchHeader}>
        <div className={styles.searchContent}>
          <h1>Find the Best Hospitals Near You</h1>
          <p>Book appointments with top-rated hospitals across India</p>
          
          <div className={styles.searchFilters}>
            <div className={styles.filterGroup}>
              <input
                type="text"
                name="city"
                placeholder="Search by city..."
                value={filters.city}
                onChange={handleFilterChange}
                className="input"
              />
            </div>
            
            <div className={styles.filterGroup}>
              <select
                name="specialty"
                value={filters.specialty}
                onChange={handleFilterChange}
                className="input"
              >
                <option value="">All Specialties</option>
                <option value="cardiology">Cardiology</option>
                <option value="neurology">Neurology</option>
                <option value="oncology">Oncology</option>
                <option value="orthopedics">Orthopedics</option>
                <option value="pediatrics">Pediatrics</option>
              </select>
            </div>
            
            <div className={styles.filterGroup}>
              <select
                name="rating"
                value={filters.rating}
                onChange={handleFilterChange}
                className="input"
              >
                <option value="">Any Rating</option>
                <option value="4.5">4.5+ Stars</option>
                <option value="4.0">4.0+ Stars</option>
                <option value="3.5">3.5+ Stars</option>
              </select>
            </div>
            
            <button className="btn-primary">
              🔍 Search Hospitals
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className={styles.resultsContainer}>
        <div className={styles.resultsHeader}>
          <h2>{filteredHospitals.length} Hospitals Found</h2>
          <div className={styles.sortOptions}>
            <select className="input">
              <option>Sort by: Relevance</option>
              <option>Price: Low to High</option>
              <option>Price: High to Low</option>
              <option>Rating: High to Low</option>
              <option>Distance: Nearest First</option>
            </select>
          </div>
        </div>

        <div className={styles.hospitalGrid}>
          {filteredHospitals.map((hospital) => (
            <div key={hospital.id} className={styles.hospitalCard}>
              <div className={styles.hospitalImage}>
                <img src={hospital.image} alt={hospital.name} />
                {hospital.verified && (
                  <div className={styles.verifiedBadge}>
                    ✓ Verified
                  </div>
                )}
                <div className={styles.distanceBadge}>
                  📍 {hospital.distance}
                </div>
              </div>

              <div className={styles.hospitalInfo}>
                <div className={styles.hospitalHeader}>
                  <h3>{hospital.name}</h3>
                  <div className={styles.rating}>
                    ⭐ {hospital.rating} ({hospital.reviews})
                  </div>
                </div>

                <div className={styles.location}>
                  📍 {hospital.city}, {hospital.state}
                </div>

                <div className={styles.specialties}>
                  {hospital.specialties.slice(0, 3).map((specialty, index) => (
                    <span key={index} className={styles.specialtyTag}>
                      {specialty}
                    </span>
                  ))}
                </div>

                <div className={styles.services}>
                  {hospital.services.slice(0, 3).map((service, index) => (
                    <span key={index} className={styles.serviceTag}>
                      {service}
                    </span>
                  ))}
                </div>

                <div className={styles.pricing}>
                  <div className={styles.priceInfo}>
                    <span className={styles.fromPrice}>Starting from</span>
                    <span className={styles.price}>₹{getLowestRate(hospital.rooms)}</span>
                    <span className={styles.perDay}>/day</span>
                  </div>
                  
                  <div className={styles.availability}>
                    {hospital.rooms.reduce((sum, room) => sum + room.available, 0)} beds available
                  </div>
                </div>

                <div className={styles.cardActions}>
                  <Link 
                    to={`/hospital/${hospital.id}`} 
                    className="btn-secondary"
                  >
                    View Details
                  </Link>
                  <Link 
                    to={`/book?hospital=${hospital.id}`} 
                    className="btn-primary"
                  >
                    Book Now
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredHospitals.length === 0 && (
          <div className={styles.noResults}>
            <div className={styles.noResultsIcon}>🏥</div>
            <h3>No hospitals found</h3>
            <p>Try adjusting your search filters to find more options.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default HospitalListing;
