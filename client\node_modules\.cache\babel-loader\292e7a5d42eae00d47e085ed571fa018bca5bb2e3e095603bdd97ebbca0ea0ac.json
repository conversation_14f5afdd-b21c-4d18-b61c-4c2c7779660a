{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 5v11\",\n  key: \"mdvv1e\"\n}], [\"path\", {\n  d: \"M12 5v6\",\n  key: \"14ar3b\"\n}], [\"path\", {\n  d: \"M18 5v14\",\n  key: \"7ji314\"\n}]];\nconst Kanban = createLucideIcon(\"kanban\", __iconNode);\nexport { __iconNode, Kanban as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Ka<PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\WebProject\\HOPE-MEDICS\\client\\node_modules\\lucide-react\\src\\icons\\kanban.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 5v11', key: 'mdvv1e' }],\n  ['path', { d: 'M12 5v6', key: '14ar3b' }],\n  ['path', { d: 'M18 5v14', key: '7ji314' }],\n];\n\n/**\n * @component @name Kanban\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiA1djExIiAvPgogIDxwYXRoIGQ9Ik0xMiA1djYiIC8+CiAgPHBhdGggZD0iTTE4IDV2MTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/kanban\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Kanban = createLucideIcon('kanban', __iconNode);\n\nexport default Kanban;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}