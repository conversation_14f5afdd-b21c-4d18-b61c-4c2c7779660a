import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Link } from 'react-router-dom';
import {
  MapPin,
  Phone,
  Star,
  Bed,
  Search,
  Calendar,
  Shield,
  Users,
  Clock,
  Building2
} from 'lucide-react';

const Home = () => {
  const [hospitals, setHospitals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for development
  const mockHospitals = [
    {
      id: 1,
      name: "City General Hospital",
      address: "123 Main Street, Downtown",
      contact: "+****************",
      bedPricePerDay: 150.00,
      photos: ["/api/placeholder/400/300"],
      services: ["Emergency Care", "Surgery", "Cardiology", "Pediatrics"],
      rating: 4.5,
      totalBeds: 200,
      availableBeds: 45
    },
    {
      id: 2,
      name: "Hope Medical Center",
      address: "456 Oak Avenue, Midtown",
      contact: "+****************",
      bedPricePerDay: 200.00,
      photos: ["/api/placeholder/400/300"],
      services: ["Oncology", "Neurology", "Orthopedics", "ICU"],
      rating: 4.8,
      totalBeds: 150,
      availableBeds: 23
    },
    {
      id: 3,
      name: "Sunrise Community Hospital",
      address: "789 Pine Road, Suburbs",
      contact: "+1 (555) 456-7890",
      bedPricePerDay: 120.00,
      photos: ["/api/placeholder/400/300"],
      services: ["Family Medicine", "Maternity", "Pharmacy", "Lab Services"],
      rating: 4.2,
      totalBeds: 100,
      availableBeds: 67
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setHospitals(mockHospitals);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredHospitals = hospitals.filter(hospital =>
    hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hospital.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-lg p-8 text-center">
        <h1 className="text-4xl font-bold mb-4">Welcome to Hope Medics</h1>
        <p className="text-xl mb-6 opacity-90">Your trusted partner for quality healthcare services</p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button size="lg" variant="secondary">
            <Calendar className="mr-2 h-5 w-5" />
            Find a Doctor
          </Button>
          <Button size="lg" variant="outline" className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
            <Shield className="mr-2 h-5 w-5" />
            Emergency Services
          </Button>
        </div>
      </div>

      {/* Search Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Find Hospitals Near You
          </CardTitle>
          <CardDescription>
            Search for hospitals by name or location
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="Search by hospital name or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Hospital Listings */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Available Hospitals</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredHospitals.map((hospital) => (
            <Card key={hospital.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{hospital.name}</CardTitle>
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-current" />
                    {hospital.rating}
                  </Badge>
                </div>
                <CardDescription className="space-y-1">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4" />
                    {hospital.address}
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4" />
                    {hospital.contact}
                  </div>
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Services */}
                <div>
                  <h4 className="font-medium mb-2">Services:</h4>
                  <div className="flex flex-wrap gap-1">
                    {hospital.services.slice(0, 3).map((service, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {service}
                      </Badge>
                    ))}
                    {hospital.services.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{hospital.services.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Bed Availability */}
                <div className="bg-muted p-3 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium flex items-center gap-1">
                      <Bed className="h-4 w-4" />
                      Bed Availability
                    </span>
                    <span className="text-sm font-bold text-green-600">
                      {hospital.availableBeds}/{hospital.totalBeds} available
                    </span>
                  </div>
                  <div className="w-full bg-background rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${(hospital.availableBeds / hospital.totalBeds) * 100}%` }}
                    ></div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="flex justify-between items-center">
                  <div>
                    <span className="text-sm text-muted-foreground">Bed Price per Day</span>
                    <div className="text-lg font-bold text-primary">
                      ${hospital.bedPricePerDay.toFixed(2)}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" asChild className="flex-1">
                    <Link to={`/hospital/${hospital.id}`}>
                      View Details
                    </Link>
                  </Button>
                  <Button size="sm" asChild className="flex-1">
                    <Link to={`/book?hospital=${hospital.id}`}>
                      Book Now
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Stats Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-center">Why Choose Hope Medics?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">50+</div>
              <div className="text-muted-foreground flex items-center justify-center gap-1">
                <Building2 className="h-4 w-4" />
                Partner Hospitals
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">200+</div>
              <div className="text-muted-foreground flex items-center justify-center gap-1">
                <Users className="h-4 w-4" />
                Qualified Doctors
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">24/7</div>
              <div className="text-muted-foreground flex items-center justify-center gap-1">
                <Clock className="h-4 w-4" />
                Emergency Services
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">10k+</div>
              <div className="text-muted-foreground flex items-center justify-center gap-1">
                <Star className="h-4 w-4" />
                Happy Patients
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Home;
