import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import HospitalCard from '../components/HospitalCard';
import styles from './Home.module.css';

const Home = () => {
  const [hospitals, setHospitals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Indian hospitals dummy data
    const mockHospitals = [
      {
        id: 1,
        name: "Apollo Hospital",
        city: "Delhi",
        image: "https://images.unsplash.com/photo-1586773860418-d37222d8fce3?w=400",
        pricePerDay: 800,
        rating: 4.8,
        totalBeds: 500,
        availableBeds: 45,
        services: ["Cardiology", "Neurology", "Oncology", "Emergency Care", "ICU"]
      },
      {
        id: 2,
        name: "Fortis Hospital",
        city: "Bangalore",
        image: "https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?w=400",
        pricePerDay: 1500,
        rating: 4.7,
        totalBeds: 400,
        availableBeds: 23,
        services: ["Orthopedic", "Cardiology", "Gastroenterology", "Pediatrics"]
      },
      {
        id: 3,
        name: "Medanta Medicity",
        city: "Gurgaon",
        image: "https://images.unsplash.com/photo-1551190822-a9333d879b1f?w=400",
        pricePerDay: 2500,
        rating: 4.9,
        totalBeds: 800,
        availableBeds: 67,
        services: ["Multi-Specialty", "Heart Surgery", "Transplant", "Cancer Care"]
      },
      {
        id: 4,
        name: "AIIMS",
        city: "New Delhi",
        image: "https://images.unsplash.com/photo-1538108149393-fbbd81895907?w=400",
        pricePerDay: 500,
        rating: 4.6,
        totalBeds: 2500,
        availableBeds: 150,
        services: ["General Medicine", "Surgery", "Emergency", "Research"]
      },
      {
        id: 5,
        name: "Max Healthcare",
        city: "Mumbai",
        image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400",
        pricePerDay: 1200,
        rating: 4.5,
        totalBeds: 350,
        availableBeds: 89,
        services: ["Maternity", "Pediatrics", "Orthopedic", "Dermatology"]
      },
      {
        id: 6,
        name: "Manipal Hospital",
        city: "Bangalore",
        image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400",
        pricePerDay: 900,
        rating: 4.4,
        totalBeds: 300,
        availableBeds: 42,
        services: ["Nephrology", "Urology", "Gastroenterology", "Emergency"]
      }
    ];

    // Simulate API call
    setTimeout(() => {
      setHospitals(mockHospitals);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredHospitals = hospitals.filter(hospital =>
    hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hospital.city.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading hospitals...</p>
      </div>
    );
  }

  return (
    <div className={styles.home}>
      {/* Hero Section */}
      <section className={styles.hero}>
        <div className="container">
          <div className={styles.heroContent}>
            <h1 className={styles.heroTitle}>Find Hospitals Near You</h1>
            <p className={styles.heroSubtitle}>
              Discover trusted healthcare providers across India with transparent pricing and real-time availability
            </p>
            <div className={styles.heroActions}>
              <Link to="/book" className="btn-primary">
                🩺 Book Appointment
              </Link>
              <Link to="#emergency" className="btn-secondary">
                🚨 Emergency Services
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className={styles.features}>
        <div className="container">
          <div className="grid grid-cols-responsive">
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>👨‍⚕️</div>
              <h3>Expert Doctors</h3>
              <p>Access to qualified and experienced medical professionals across various specialties.</p>
            </div>

            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🕐</div>
              <h3>24/7 Service</h3>
              <p>Round-the-clock medical services and emergency care when you need it most.</p>
            </div>

            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🛡️</div>
              <h3>Quality Care</h3>
              <p>State-of-the-art facilities and equipment ensuring the highest quality of care.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Search Section */}
      <section className={styles.searchSection}>
        <div className="container">
          <div className={styles.searchCard}>
            <h2>🔍 Find Hospitals Near You</h2>
            <p>Search for hospitals by name or location</p>
            <div className={styles.searchBox}>
              <input
                type="text"
                placeholder="Search by hospital name or city..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input"
              />
              <button className="btn-primary">Search</button>
            </div>
          </div>
        </div>
      </section>

      {/* Hospital Listings */}
      <section className={styles.hospitalListings}>
        <div className="container">
          <h2 className={styles.sectionTitle}>Available Hospitals</h2>
          <div className="grid grid-cols-responsive">
            {filteredHospitals.map((hospital) => (
              <HospitalCard key={hospital.id} hospital={hospital} />
            ))}
          </div>

          {filteredHospitals.length === 0 && (
            <div className={styles.noResults}>
              <p>No hospitals found matching your search criteria.</p>
              <button
                className="btn-secondary"
                onClick={() => setSearchTerm('')}
              >
                Clear Search
              </button>
            </div>
          )}
        </div>
      </section>

      {/* Stats Section */}
      <section className={styles.stats}>
        <div className="container">
          <h2 className={styles.sectionTitle}>Why Choose Hope Medics?</h2>
          <div className="grid grid-cols-responsive">
            <div className={styles.statCard}>
              <div className={styles.statNumber}>50+</div>
              <div className={styles.statLabel}>🏥 Partner Hospitals</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>200+</div>
              <div className={styles.statLabel}>👨‍⚕️ Qualified Doctors</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>24/7</div>
              <div className={styles.statLabel}>🚨 Emergency Services</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statNumber}>10k+</div>
              <div className={styles.statLabel}>⭐ Happy Patients</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
