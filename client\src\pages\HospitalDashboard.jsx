import React, { useState } from 'react';
import styles from './HospitalDashboard.module.css';

const HospitalDashboard = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [hospitalData, setHospitalData] = useState({
    name: 'Apollo Hospital Delhi',
    email: '<EMAIL>',
    phone: '+91 98765 00000',
    address: 'Mathura Road, Sarita Vihar, New Delhi',
    city: 'New Delhi',
    state: 'Delhi',
    pincode: '110076',
    licenseNumber: 'DL-HOSP-2023-001',
    establishedYear: '1995',
    hospitalType: 'private',
    totalBeds: '500',
    description: 'Leading multi-specialty hospital with state-of-the-art facilities and experienced medical professionals.',
    emergencyServices: true,
    ambulanceService: true,
    website: 'https://apollo-delhi.com',
    rating: 4.5,
    totalReviews: 1250
  });

  const [rooms, setRooms] = useState([
    { id: 1, type: 'General Ward', beds: 200, rate: 800, available: 45 },
    { id: 2, type: 'Private Room', beds: 150, rate: 2500, available: 23 },
    { id: 3, type: 'ICU', beds: 50, rate: 4500, available: 8 },
    { id: 4, type: 'NICU', beds: 20, rate: 6000, available: 3 },
    { id: 5, type: 'Deluxe Suite', beds: 30, rate: 8000, available: 12 }
  ]);

  const [doctors, setDoctors] = useState([
    { id: 1, name: 'Dr. Meena Sharma', specialty: 'Cardiology', experience: '15 years', available: true, consultationFee: 1500 },
    { id: 2, name: 'Dr. Arjun Rao', specialty: 'Orthopedics', experience: '12 years', available: true, consultationFee: 1200 },
    { id: 3, name: 'Dr. Priya Singh', specialty: 'Pediatrics', experience: '10 years', available: false, consultationFee: 1000 },
    { id: 4, name: 'Dr. Sunil Patel', specialty: 'General Medicine', experience: '8 years', available: true, consultationFee: 800 }
  ]);

  const stats = [
    { title: 'Total Beds', value: hospitalData.totalBeds, icon: '🛏️', change: '+5 this month' },
    { title: 'Available Beds', value: rooms.reduce((sum, room) => sum + room.available, 0), icon: '✅', change: 'Real-time' },
    { title: 'Active Doctors', value: doctors.filter(d => d.available).length, icon: '👨‍⚕️', change: `${doctors.length} total` },
    { title: 'Rating', value: hospitalData.rating, icon: '⭐', change: `${hospitalData.totalReviews} reviews` }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setHospitalData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSave = () => {
    // Save hospital data
    console.log('Saving hospital data:', hospitalData);
    alert('Hospital profile updated successfully!');
  };

  const addRoom = () => {
    const newRoom = {
      id: rooms.length + 1,
      type: 'New Room Type',
      beds: 0,
      rate: 0,
      available: 0
    };
    setRooms([...rooms, newRoom]);
  };

  const updateRoom = (id, field, value) => {
    setRooms(rooms.map(room => 
      room.id === id ? { ...room, [field]: value } : room
    ));
  };

  const addDoctor = () => {
    const newDoctor = {
      id: doctors.length + 1,
      name: 'New Doctor',
      specialty: 'General Medicine',
      experience: '0 years',
      available: true,
      consultationFee: 500
    };
    setDoctors([...doctors, newDoctor]);
  };

  const updateDoctor = (id, field, value) => {
    setDoctors(doctors.map(doctor => 
      doctor.id === id ? { ...doctor, [field]: value } : doctor
    ));
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.hospitalInfo}>
            <h1>{hospitalData.name}</h1>
            <p>{hospitalData.city}, {hospitalData.state}</p>
            <div className={styles.rating}>
              ⭐ {hospitalData.rating} ({hospitalData.totalReviews} reviews)
            </div>
          </div>
          <div className={styles.headerActions}>
            <button className="btn-secondary">
              👁️ View Public Profile
            </button>
            <button className="btn-primary" onClick={handleSave}>
              💾 Save Changes
            </button>
          </div>
        </div>
      </div>

      <div className={styles.mainContent}>
        {/* Stats Grid */}
        <div className={styles.statsGrid}>
          {stats.map((stat, index) => (
            <div key={index} className={styles.statCard}>
              <div className={styles.statIcon}>{stat.icon}</div>
              <div className={styles.statContent}>
                <h3>{stat.title}</h3>
                <div className={styles.statValue}>{stat.value}</div>
                <div className={styles.statChange}>{stat.change}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Tab Navigation */}
        <div className={styles.tabNavigation}>
          <button 
            className={`${styles.tabButton} ${activeTab === 'profile' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            🏥 Hospital Profile
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'rooms' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('rooms')}
          >
            🛏️ Rooms & Pricing
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'doctors' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('doctors')}
          >
            👨‍⚕️ Doctors
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'photos' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('photos')}
          >
            📸 Photos & Amenities
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'availability' ? styles.tabActive : ''}`}
            onClick={() => setActiveTab('availability')}
          >
            📅 Availability
          </button>
        </div>

        {/* Tab Content */}
        <div className={styles.tabContent}>
          {activeTab === 'profile' && (
            <div className={styles.profileSection}>
              <h2>Hospital Profile</h2>
              <div className={styles.form}>
                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="name">Hospital Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={hospitalData.name}
                      onChange={handleInputChange}
                      className="input"
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="licenseNumber">License Number</label>
                    <input
                      type="text"
                      id="licenseNumber"
                      name="licenseNumber"
                      value={hospitalData.licenseNumber}
                      onChange={handleInputChange}
                      className="input"
                    />
                  </div>
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="email">Email</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={hospitalData.email}
                      onChange={handleInputChange}
                      className="input"
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="phone">Phone</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={hospitalData.phone}
                      onChange={handleInputChange}
                      className="input"
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="address">Address</label>
                  <textarea
                    id="address"
                    name="address"
                    value={hospitalData.address}
                    onChange={handleInputChange}
                    className="input"
                    rows="3"
                  />
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="city">City</label>
                    <input
                      type="text"
                      id="city"
                      name="city"
                      value={hospitalData.city}
                      onChange={handleInputChange}
                      className="input"
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="state">State</label>
                    <select
                      id="state"
                      name="state"
                      value={hospitalData.state}
                      onChange={handleInputChange}
                      className="input"
                    >
                      <option value="delhi">Delhi</option>
                      <option value="maharashtra">Maharashtra</option>
                      <option value="karnataka">Karnataka</option>
                      <option value="tamil-nadu">Tamil Nadu</option>
                    </select>
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="pincode">Pincode</label>
                    <input
                      type="text"
                      id="pincode"
                      name="pincode"
                      value={hospitalData.pincode}
                      onChange={handleInputChange}
                      className="input"
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="description">Description</label>
                  <textarea
                    id="description"
                    name="description"
                    value={hospitalData.description}
                    onChange={handleInputChange}
                    className="input"
                    rows="4"
                    placeholder="Describe your hospital, specialties, and services"
                  />
                </div>

                <div className={styles.checkboxGroup}>
                  <h4>Services</h4>
                  <div className={styles.checkboxRow}>
                    <label className={styles.checkboxLabel}>
                      <input
                        type="checkbox"
                        name="emergencyServices"
                        checked={hospitalData.emergencyServices}
                        onChange={handleInputChange}
                        className={styles.checkbox}
                      />
                      🚨 24/7 Emergency Services
                    </label>
                    <label className={styles.checkboxLabel}>
                      <input
                        type="checkbox"
                        name="ambulanceService"
                        checked={hospitalData.ambulanceService}
                        onChange={handleInputChange}
                        className={styles.checkbox}
                      />
                      🚑 Ambulance Service
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'rooms' && (
            <div className={styles.roomsSection}>
              <div className={styles.sectionHeader}>
                <h2>Rooms & Pricing</h2>
                <button className="btn-primary" onClick={addRoom}>
                  ➕ Add Room Type
                </button>
              </div>
              
              <div className={styles.roomsList}>
                {rooms.map((room) => (
                  <div key={room.id} className={styles.roomCard}>
                    <div className={styles.roomHeader}>
                      <input
                        type="text"
                        value={room.type}
                        onChange={(e) => updateRoom(room.id, 'type', e.target.value)}
                        className={styles.roomTypeInput}
                      />
                    </div>
                    <div className={styles.roomDetails}>
                      <div className={styles.roomField}>
                        <label>Total Beds</label>
                        <input
                          type="number"
                          value={room.beds}
                          onChange={(e) => updateRoom(room.id, 'beds', parseInt(e.target.value))}
                          className="input"
                        />
                      </div>
                      <div className={styles.roomField}>
                        <label>Rate per Day (₹)</label>
                        <input
                          type="number"
                          value={room.rate}
                          onChange={(e) => updateRoom(room.id, 'rate', parseInt(e.target.value))}
                          className="input"
                        />
                      </div>
                      <div className={styles.roomField}>
                        <label>Available</label>
                        <input
                          type="number"
                          value={room.available}
                          onChange={(e) => updateRoom(room.id, 'available', parseInt(e.target.value))}
                          className="input"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HospitalDashboard;
