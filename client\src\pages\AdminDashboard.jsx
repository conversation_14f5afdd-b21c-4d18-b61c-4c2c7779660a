import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import {
  Building2,
  Users,
  Calendar,
  Plus,
  BarChart3,
  Settings
} from 'lucide-react';

const AdminDashboard = () => {
  const stats = [
    { title: 'Total Hospitals', value: '12', icon: Building2, color: 'text-blue-600' },
    { title: 'Total Doctors', value: '156', icon: Users, color: 'text-green-600' },
    { title: 'Total Patients', value: '2,847', icon: Users, color: 'text-purple-600' },
    { title: 'Today\'s Appointments', value: '45', icon: Calendar, color: 'text-orange-600' },
  ];

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">Manage your healthcare platform</p>
        </div>
        <div className="flex space-x-4">
          <Button variant="outline">
            <BarChart3 className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Hospital
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <IconComponent className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { action: 'New appointment booked', user: 'John Doe', time: '2 minutes ago', type: 'appointment' },
                  { action: 'Doctor updated availability', user: 'Dr. Sarah Johnson', time: '15 minutes ago', type: 'doctor' },
                  { action: 'New hospital added', user: 'Admin', time: '1 hour ago', type: 'hospital' },
                  { action: 'Patient registration', user: 'Jane Smith', time: '2 hours ago', type: 'user' },
                ].map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="text-lg">
                      {activity.type === 'appointment' && '📅'}
                      {activity.type === 'doctor' && '👩‍⚕️'}
                      {activity.type === 'hospital' && '🏥'}
                      {activity.type === 'user' && '👤'}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.action}</p>
                      <p className="text-xs text-muted-foreground">by {activity.user} • {activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Building2 className="mr-2 h-4 w-4" />
                Manage Hospitals
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                Manage Doctors
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="mr-2 h-4 w-4" />
                View Appointments
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="mr-2 h-4 w-4" />
                Analytics
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Settings className="mr-2 h-4 w-4" />
                System Settings
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
