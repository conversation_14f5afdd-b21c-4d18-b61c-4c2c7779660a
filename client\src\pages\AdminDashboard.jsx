import React, { useState } from 'react';
import styles from './AdminDashboard.module.css';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const stats = [
    { title: 'Total Hospitals', value: '12', change: '+2 from last month', icon: '🏥' },
    { title: 'Total Users', value: '1,234', change: '+180 from last month', icon: '👥' },
    { title: 'Total Appointments', value: '5,678', change: '+12% from last month', icon: '📅' },
    { title: 'Revenue', value: '₹45,231', change: '+20.1% from last month', icon: '💰' }
  ];

  return (
    <div className={styles.container}>
      {/* Sidebar */}
      <div className={styles.sidebar}>
        <div className={styles.sidebarHeader}>
          <h2>🏥 Admin Panel</h2>
        </div>
        <nav className={styles.sidebarNav}>
          <button
            className={`${styles.navItem} ${activeTab === 'overview' ? styles.navItemActive : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            📊 Overview
          </button>
          <button
            className={`${styles.navItem} ${activeTab === 'hospitals' ? styles.navItemActive : ''}`}
            onClick={() => setActiveTab('hospitals')}
          >
            🏥 Hospitals
          </button>
          <button
            className={`${styles.navItem} ${activeTab === 'users' ? styles.navItemActive : ''}`}
            onClick={() => setActiveTab('users')}
          >
            👥 Users
          </button>
          <button
            className={`${styles.navItem} ${activeTab === 'appointments' ? styles.navItemActive : ''}`}
            onClick={() => setActiveTab('appointments')}
          >
            📅 Appointments
          </button>
          <button
            className={`${styles.navItem} ${activeTab === 'settings' ? styles.navItemActive : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            ⚙️ Settings
          </button>
        </nav>
      </div>

      {/* Main Content */}
      <div className={styles.mainContent}>
        <div className={styles.header}>
          <h1>Admin Dashboard</h1>
          <div className={styles.headerActions}>
            <button className="btn-accent">
              ➕ Add Hospital
            </button>
            <button className="btn-secondary">
              👤 Add User
            </button>
          </div>
        </div>

        {activeTab === 'overview' && (
          <>
            {/* Stats Cards */}
            <div className={styles.statsGrid}>
              {stats.map((stat, index) => (
                <div key={index} className={styles.statCard}>
                  <div className={styles.statIcon}>{stat.icon}</div>
                  <div className={styles.statContent}>
                    <h3>{stat.title}</h3>
                    <div className={styles.statValue}>{stat.value}</div>
                    <div className={styles.statChange}>{stat.change}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Recent Activities */}
            <div className={styles.recentActivities}>
              <h2>Recent Activities</h2>
              <div className={styles.activitiesList}>
                {[
                  { action: 'New appointment booked', user: 'John Doe', time: '2 minutes ago', type: 'appointment' },
                  { action: 'Doctor updated availability', user: 'Dr. Sarah Johnson', time: '15 minutes ago', type: 'doctor' },
                  { action: 'New hospital added', user: 'Admin', time: '1 hour ago', type: 'hospital' },
                  { action: 'Patient registration', user: 'Jane Smith', time: '2 hours ago', type: 'user' },
                ].map((activity, index) => (
                  <div key={index} className={styles.activityItem}>
                    <div className={styles.activityIcon}>
                      {activity.type === 'appointment' && '📅'}
                      {activity.type === 'doctor' && '👩‍⚕️'}
                      {activity.type === 'hospital' && '🏥'}
                      {activity.type === 'user' && '👤'}
                    </div>
                    <div className={styles.activityContent}>
                      <p className={styles.activityAction}>{activity.action}</p>
                      <p className={styles.activityMeta}>by {activity.user} • {activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {activeTab === 'hospitals' && (
          <div className={styles.tableContainer}>
            <h2>Manage Hospitals</h2>
            <div className={styles.table}>
              <div className={styles.tableHeader}>
                <div>Hospital Name</div>
                <div>City</div>
                <div>Beds</div>
                <div>Status</div>
                <div>Actions</div>
              </div>
              {[
                { name: 'Apollo Hospital', city: 'Delhi', beds: 500, status: 'Active' },
                { name: 'Fortis Hospital', city: 'Bangalore', beds: 400, status: 'Active' },
                { name: 'Max Healthcare', city: 'Mumbai', beds: 350, status: 'Pending' },
                { name: 'AIIMS', city: 'New Delhi', beds: 2500, status: 'Active' }
              ].map((hospital, index) => (
                <div key={index} className={styles.tableRow}>
                  <div>{hospital.name}</div>
                  <div>{hospital.city}</div>
                  <div>{hospital.beds}</div>
                  <div>
                    <span className={`${styles.statusBadge} ${hospital.status === 'Active' ? styles.statusActive : styles.statusPending}`}>
                      {hospital.status}
                    </span>
                  </div>
                  <div className={styles.tableActions}>
                    <button className="btn-secondary">Edit</button>
                    <button className="btn-accent">View</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <div className={styles.tableContainer}>
            <h2>Manage Users</h2>
            <div className={styles.table}>
              <div className={styles.tableHeader}>
                <div>Name</div>
                <div>Role</div>
                <div>Hospital</div>
                <div>Status</div>
                <div>Actions</div>
              </div>
              {[
                { name: 'Dr. Meena Sharma', role: 'Doctor', hospital: 'Apollo Hospital', status: 'Active' },
                { name: 'John Doe', role: 'Patient', hospital: '-', status: 'Active' },
                { name: 'Sarah Wilson', role: 'Receptionist', hospital: 'Fortis Hospital', status: 'Active' },
                { name: 'Dr. Arjun Rao', role: 'Doctor', hospital: 'Max Healthcare', status: 'Pending' }
              ].map((user, index) => (
                <div key={index} className={styles.tableRow}>
                  <div>{user.name}</div>
                  <div>{user.role}</div>
                  <div>{user.hospital}</div>
                  <div>
                    <span className={`${styles.statusBadge} ${user.status === 'Active' ? styles.statusActive : styles.statusPending}`}>
                      {user.status}
                    </span>
                  </div>
                  <div className={styles.tableActions}>
                    <button className="btn-secondary">Edit</button>
                    <button className="btn-accent">View</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
